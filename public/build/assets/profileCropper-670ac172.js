/*!
 * Cropper.js v1.6.2
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present <PERSON>
 * Released under the MIT license
 *
 * Date: 2024-04-21T07:43:05.335Z
 */function _t(a,t){var i=Object.keys(a);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(a);t&&(e=e.filter(function(o){return Object.getOwnPropertyDescriptor(a,o).enumerable})),i.push.apply(i,e)}return i}function Zt(a){for(var t=1;t<arguments.length;t++){var i=arguments[t]!=null?arguments[t]:{};t%2?_t(Object(i),!0).forEach(function(e){ye(a,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(i)):_t(Object(i)).forEach(function(e){Object.defineProperty(a,e,Object.getOwnPropertyDescriptor(i,e))})}return a}function me(a,t){if(typeof a!="object"||!a)return a;var i=a[Symbol.toPrimitive];if(i!==void 0){var e=i.call(a,t||"default");if(typeof e!="object")return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(a)}function Jt(a){var t=me(a,"string");return typeof t=="symbol"?t:t+""}function vt(a){"@babel/helpers - typeof";return vt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vt(a)}function ve(a,t){if(!(a instanceof t))throw new TypeError("Cannot call a class as a function")}function Pt(a,t){for(var i=0;i<t.length;i++){var e=t[i];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(a,Jt(e.key),e)}}function we(a,t,i){return t&&Pt(a.prototype,t),i&&Pt(a,i),Object.defineProperty(a,"prototype",{writable:!1}),a}function ye(a,t,i){return t=Jt(t),t in a?Object.defineProperty(a,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):a[t]=i,a}function te(a){return be(a)||xe(a)||De(a)||Ee()}function be(a){if(Array.isArray(a))return wt(a)}function xe(a){if(typeof Symbol<"u"&&a[Symbol.iterator]!=null||a["@@iterator"]!=null)return Array.from(a)}function De(a,t){if(a){if(typeof a=="string")return wt(a,t);var i=Object.prototype.toString.call(a).slice(8,-1);if(i==="Object"&&a.constructor&&(i=a.constructor.name),i==="Map"||i==="Set")return Array.from(a);if(i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return wt(a,t)}}function wt(a,t){(t==null||t>a.length)&&(t=a.length);for(var i=0,e=new Array(t);i<t;i++)e[i]=a[i];return e}function Ee(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var ft=typeof window<"u"&&typeof window.document<"u",P=ft?window:{},Ot=ft&&P.document.documentElement?"ontouchstart"in P.document.documentElement:!1,Nt=ft?"PointerEvent"in P:!1,x="cropper",At="all",ee="crop",ie="move",ae="zoom",G="e",q="w",K="s",H="n",et="ne",it="nw",at="se",rt="sw",yt="".concat(x,"-crop"),Yt="".concat(x,"-disabled"),R="".concat(x,"-hidden"),Xt="".concat(x,"-hide"),Me="".concat(x,"-invisible"),dt="".concat(x,"-modal"),bt="".concat(x,"-move"),ot="".concat(x,"Action"),ct="".concat(x,"Preview"),Rt="crop",re="move",ne="none",xt="crop",Dt="cropend",Et="cropmove",Mt="cropstart",zt="dblclick",Ce=Ot?"touchstart":"mousedown",Te=Ot?"touchmove":"mousemove",Oe=Ot?"touchend touchcancel":"mouseup",Ht=Nt?"pointerdown":Ce,Wt=Nt?"pointermove":Te,Ut=Nt?"pointerup pointercancel":Oe,jt="ready",$t="resize",Vt="wheel",Ct="zoom",Gt="image/jpeg",Ne=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,Ae=/^data:/,Re=/^data:image\/jpeg;base64,/,Ie=/^img|canvas$/i,oe=200,se=100,qt={viewMode:0,dragMode:Rt,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:oe,minContainerHeight:se,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},Se='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',Be=Number.isNaN||P.isNaN;function m(a){return typeof a=="number"&&!Be(a)}var Ft=function(t){return t>0&&t<1/0};function gt(a){return typeof a>"u"}function F(a){return vt(a)==="object"&&a!==null}var Le=Object.prototype.hasOwnProperty;function Q(a){if(!F(a))return!1;try{var t=a.constructor,i=t.prototype;return t&&i&&Le.call(i,"isPrototypeOf")}catch{return!1}}function A(a){return typeof a=="function"}var ke=Array.prototype.slice;function he(a){return Array.from?Array.from(a):ke.call(a)}function C(a,t){return a&&A(t)&&(Array.isArray(a)||m(a.length)?he(a).forEach(function(i,e){t.call(a,i,e,a)}):F(a)&&Object.keys(a).forEach(function(i){t.call(a,a[i],i,a)})),a}var D=Object.assign||function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),o=1;o<i;o++)e[o-1]=arguments[o];return F(t)&&e.length>0&&e.forEach(function(r){F(r)&&Object.keys(r).forEach(function(n){t[n]=r[n]})}),t},_e=/\.\d*(?:0|9){12}\d*$/;function J(a){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return _e.test(a)?Math.round(a*t)/t:a}var Pe=/^width|height|left|top|marginLeft|marginTop$/;function W(a,t){var i=a.style;C(t,function(e,o){Pe.test(o)&&m(e)&&(e="".concat(e,"px")),i[o]=e})}function Ye(a,t){return a.classList?a.classList.contains(t):a.className.indexOf(t)>-1}function O(a,t){if(t){if(m(a.length)){C(a,function(e){O(e,t)});return}if(a.classList){a.classList.add(t);return}var i=a.className.trim();i?i.indexOf(t)<0&&(a.className="".concat(i," ").concat(t)):a.className=t}}function _(a,t){if(t){if(m(a.length)){C(a,function(i){_(i,t)});return}if(a.classList){a.classList.remove(t);return}a.className.indexOf(t)>=0&&(a.className=a.className.replace(t,""))}}function Z(a,t,i){if(t){if(m(a.length)){C(a,function(e){Z(e,t,i)});return}i?O(a,t):_(a,t)}}var Xe=/([a-z\d])([A-Z])/g;function It(a){return a.replace(Xe,"$1-$2").toLowerCase()}function Tt(a,t){return F(a[t])?a[t]:a.dataset?a.dataset[t]:a.getAttribute("data-".concat(It(t)))}function st(a,t,i){F(i)?a[t]=i:a.dataset?a.dataset[t]=i:a.setAttribute("data-".concat(It(t)),i)}function ze(a,t){if(F(a[t]))try{delete a[t]}catch{a[t]=void 0}else if(a.dataset)try{delete a.dataset[t]}catch{a.dataset[t]=void 0}else a.removeAttribute("data-".concat(It(t)))}var ce=/\s\s*/,le=function(){var a=!1;if(ft){var t=!1,i=function(){},e=Object.defineProperty({},"once",{get:function(){return a=!0,t},set:function(r){t=r}});P.addEventListener("test",i,e),P.removeEventListener("test",i,e)}return a}();function L(a,t,i){var e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},o=i;t.trim().split(ce).forEach(function(r){if(!le){var n=a.listeners;n&&n[r]&&n[r][i]&&(o=n[r][i],delete n[r][i],Object.keys(n[r]).length===0&&delete n[r],Object.keys(n).length===0&&delete a.listeners)}a.removeEventListener(r,o,e)})}function B(a,t,i){var e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},o=i;t.trim().split(ce).forEach(function(r){if(e.once&&!le){var n=a.listeners,s=n===void 0?{}:n;o=function(){delete s[r][i],a.removeEventListener(r,o,e);for(var l=arguments.length,h=new Array(l),c=0;c<l;c++)h[c]=arguments[c];i.apply(a,h)},s[r]||(s[r]={}),s[r][i]&&a.removeEventListener(r,s[r][i],e),s[r][i]=o,a.listeners=s}a.addEventListener(r,o,e)})}function tt(a,t,i){var e;return A(Event)&&A(CustomEvent)?e=new CustomEvent(t,{detail:i,bubbles:!0,cancelable:!0}):(e=document.createEvent("CustomEvent"),e.initCustomEvent(t,!0,!0,i)),a.dispatchEvent(e)}function de(a){var t=a.getBoundingClientRect();return{left:t.left+(window.pageXOffset-document.documentElement.clientLeft),top:t.top+(window.pageYOffset-document.documentElement.clientTop)}}var mt=P.location,He=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function Kt(a){var t=a.match(He);return t!==null&&(t[1]!==mt.protocol||t[2]!==mt.hostname||t[3]!==mt.port)}function Qt(a){var t="timestamp=".concat(new Date().getTime());return a+(a.indexOf("?")===-1?"?":"&")+t}function nt(a){var t=a.rotate,i=a.scaleX,e=a.scaleY,o=a.translateX,r=a.translateY,n=[];m(o)&&o!==0&&n.push("translateX(".concat(o,"px)")),m(r)&&r!==0&&n.push("translateY(".concat(r,"px)")),m(t)&&t!==0&&n.push("rotate(".concat(t,"deg)")),m(i)&&i!==1&&n.push("scaleX(".concat(i,")")),m(e)&&e!==1&&n.push("scaleY(".concat(e,")"));var s=n.length?n.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function We(a){var t=Zt({},a),i=0;return C(a,function(e,o){delete t[o],C(t,function(r){var n=Math.abs(e.startX-r.startX),s=Math.abs(e.startY-r.startY),f=Math.abs(e.endX-r.endX),l=Math.abs(e.endY-r.endY),h=Math.sqrt(n*n+s*s),c=Math.sqrt(f*f+l*l),d=(c-h)/h;Math.abs(d)>Math.abs(i)&&(i=d)})}),i}function lt(a,t){var i=a.pageX,e=a.pageY,o={endX:i,endY:e};return t?o:Zt({startX:i,startY:e},o)}function Ue(a){var t=0,i=0,e=0;return C(a,function(o){var r=o.startX,n=o.startY;t+=r,i+=n,e+=1}),t/=e,i/=e,{pageX:t,pageY:i}}function U(a){var t=a.aspectRatio,i=a.height,e=a.width,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",r=Ft(e),n=Ft(i);if(r&&n){var s=i*t;o==="contain"&&s>e||o==="cover"&&s<e?i=e/t:e=i*t}else r?i=e/t:n&&(e=i*t);return{width:e,height:i}}function je(a){var t=a.width,i=a.height,e=a.degree;if(e=Math.abs(e)%180,e===90)return{width:i,height:t};var o=e%90*Math.PI/180,r=Math.sin(o),n=Math.cos(o),s=t*n+i*r,f=t*r+i*n;return e>90?{width:f,height:s}:{width:s,height:f}}function $e(a,t,i,e){var o=t.aspectRatio,r=t.naturalWidth,n=t.naturalHeight,s=t.rotate,f=s===void 0?0:s,l=t.scaleX,h=l===void 0?1:l,c=t.scaleY,d=c===void 0?1:c,g=i.aspectRatio,u=i.naturalWidth,y=i.naturalHeight,v=e.fillColor,b=v===void 0?"transparent":v,E=e.imageSmoothingEnabled,M=E===void 0?!0:E,Y=e.imageSmoothingQuality,I=Y===void 0?"low":Y,p=e.maxWidth,w=p===void 0?1/0:p,T=e.maxHeight,S=T===void 0?1/0:T,X=e.minWidth,j=X===void 0?0:X,$=e.minHeight,z=$===void 0?0:$,k=document.createElement("canvas"),N=k.getContext("2d"),V=U({aspectRatio:g,width:w,height:S}),ht=U({aspectRatio:g,width:j,height:z},"cover"),pt=Math.min(V.width,Math.max(ht.width,u)),ut=Math.min(V.height,Math.max(ht.height,y)),St=U({aspectRatio:o,width:w,height:S}),Bt=U({aspectRatio:o,width:j,height:z},"cover"),Lt=Math.min(St.width,Math.max(Bt.width,r)),kt=Math.min(St.height,Math.max(Bt.height,n)),ue=[-Lt/2,-kt/2,Lt,kt];return k.width=J(pt),k.height=J(ut),N.fillStyle=b,N.fillRect(0,0,pt,ut),N.save(),N.translate(pt/2,ut/2),N.rotate(f*Math.PI/180),N.scale(h,d),N.imageSmoothingEnabled=M,N.imageSmoothingQuality=I,N.drawImage.apply(N,[a].concat(te(ue.map(function(ge){return Math.floor(J(ge))})))),N.restore(),k}var fe=String.fromCharCode;function Ve(a,t,i){var e="";i+=t;for(var o=t;o<i;o+=1)e+=fe(a.getUint8(o));return e}var Ge=/^data:.*,/;function qe(a){var t=a.replace(Ge,""),i=atob(t),e=new ArrayBuffer(i.length),o=new Uint8Array(e);return C(o,function(r,n){o[n]=i.charCodeAt(n)}),e}function Fe(a,t){for(var i=[],e=8192,o=new Uint8Array(a);o.length>0;)i.push(fe.apply(null,he(o.subarray(0,e)))),o=o.subarray(e);return"data:".concat(t,";base64,").concat(btoa(i.join("")))}function Ke(a){var t=new DataView(a),i;try{var e,o,r;if(t.getUint8(0)===255&&t.getUint8(1)===216)for(var n=t.byteLength,s=2;s+1<n;){if(t.getUint8(s)===255&&t.getUint8(s+1)===225){o=s;break}s+=1}if(o){var f=o+4,l=o+10;if(Ve(t,f,4)==="Exif"){var h=t.getUint16(l);if(e=h===18761,(e||h===19789)&&t.getUint16(l+2,e)===42){var c=t.getUint32(l+4,e);c>=8&&(r=l+c)}}}if(r){var d=t.getUint16(r,e),g,u;for(u=0;u<d;u+=1)if(g=r+u*12+2,t.getUint16(g,e)===274){g+=8,i=t.getUint16(g,e),t.setUint16(g,1,e);break}}}catch{i=1}return i}function Qe(a){var t=0,i=1,e=1;switch(a){case 2:i=-1;break;case 3:t=-180;break;case 4:e=-1;break;case 5:t=90,e=-1;break;case 6:t=90;break;case 7:t=90,i=-1;break;case 8:t=-90;break}return{rotate:t,scaleX:i,scaleY:e}}var Ze={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,i=this.options,e=this.container,o=this.cropper,r=Number(i.minContainerWidth),n=Number(i.minContainerHeight);O(o,R),_(t,R);var s={width:Math.max(e.offsetWidth,r>=0?r:oe),height:Math.max(e.offsetHeight,n>=0?n:se)};this.containerData=s,W(o,{width:s.width,height:s.height}),O(t,R),_(o,R)},initCanvas:function(){var t=this.containerData,i=this.imageData,e=this.options.viewMode,o=Math.abs(i.rotate)%180===90,r=o?i.naturalHeight:i.naturalWidth,n=o?i.naturalWidth:i.naturalHeight,s=r/n,f=t.width,l=t.height;t.height*s>t.width?e===3?f=t.height*s:l=t.width/s:e===3?l=t.width/s:f=t.height*s;var h={aspectRatio:s,naturalWidth:r,naturalHeight:n,width:f,height:l};this.canvasData=h,this.limited=e===1||e===2,this.limitCanvas(!0,!0),h.width=Math.min(Math.max(h.width,h.minWidth),h.maxWidth),h.height=Math.min(Math.max(h.height,h.minHeight),h.maxHeight),h.left=(t.width-h.width)/2,h.top=(t.height-h.height)/2,h.oldLeft=h.left,h.oldTop=h.top,this.initialCanvasData=D({},h)},limitCanvas:function(t,i){var e=this.options,o=this.containerData,r=this.canvasData,n=this.cropBoxData,s=e.viewMode,f=r.aspectRatio,l=this.cropped&&n;if(t){var h=Number(e.minCanvasWidth)||0,c=Number(e.minCanvasHeight)||0;s>1?(h=Math.max(h,o.width),c=Math.max(c,o.height),s===3&&(c*f>h?h=c*f:c=h/f)):s>0&&(h?h=Math.max(h,l?n.width:0):c?c=Math.max(c,l?n.height:0):l&&(h=n.width,c=n.height,c*f>h?h=c*f:c=h/f));var d=U({aspectRatio:f,width:h,height:c});h=d.width,c=d.height,r.minWidth=h,r.minHeight=c,r.maxWidth=1/0,r.maxHeight=1/0}if(i)if(s>(l?0:1)){var g=o.width-r.width,u=o.height-r.height;r.minLeft=Math.min(0,g),r.minTop=Math.min(0,u),r.maxLeft=Math.max(0,g),r.maxTop=Math.max(0,u),l&&this.limited&&(r.minLeft=Math.min(n.left,n.left+(n.width-r.width)),r.minTop=Math.min(n.top,n.top+(n.height-r.height)),r.maxLeft=n.left,r.maxTop=n.top,s===2&&(r.width>=o.width&&(r.minLeft=Math.min(0,g),r.maxLeft=Math.max(0,g)),r.height>=o.height&&(r.minTop=Math.min(0,u),r.maxTop=Math.max(0,u))))}else r.minLeft=-r.width,r.minTop=-r.height,r.maxLeft=o.width,r.maxTop=o.height},renderCanvas:function(t,i){var e=this.canvasData,o=this.imageData;if(i){var r=je({width:o.naturalWidth*Math.abs(o.scaleX||1),height:o.naturalHeight*Math.abs(o.scaleY||1),degree:o.rotate||0}),n=r.width,s=r.height,f=e.width*(n/e.naturalWidth),l=e.height*(s/e.naturalHeight);e.left-=(f-e.width)/2,e.top-=(l-e.height)/2,e.width=f,e.height=l,e.aspectRatio=n/s,e.naturalWidth=n,e.naturalHeight=s,this.limitCanvas(!0,!1)}(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCanvas(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,W(this.canvas,D({width:e.width,height:e.height},nt({translateX:e.left,translateY:e.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var i=this.canvasData,e=this.imageData,o=e.naturalWidth*(i.width/i.naturalWidth),r=e.naturalHeight*(i.height/i.naturalHeight);D(e,{width:o,height:r,left:(i.width-o)/2,top:(i.height-r)/2}),W(this.image,D({width:e.width,height:e.height},nt(D({translateX:e.left,translateY:e.top},e)))),t&&this.output()},initCropBox:function(){var t=this.options,i=this.canvasData,e=t.aspectRatio||t.initialAspectRatio,o=Number(t.autoCropArea)||.8,r={width:i.width,height:i.height};e&&(i.height*e>i.width?r.height=r.width/e:r.width=r.height*e),this.cropBoxData=r,this.limitCropBox(!0,!0),r.width=Math.min(Math.max(r.width,r.minWidth),r.maxWidth),r.height=Math.min(Math.max(r.height,r.minHeight),r.maxHeight),r.width=Math.max(r.minWidth,r.width*o),r.height=Math.max(r.minHeight,r.height*o),r.left=i.left+(i.width-r.width)/2,r.top=i.top+(i.height-r.height)/2,r.oldLeft=r.left,r.oldTop=r.top,this.initialCropBoxData=D({},r)},limitCropBox:function(t,i){var e=this.options,o=this.containerData,r=this.canvasData,n=this.cropBoxData,s=this.limited,f=e.aspectRatio;if(t){var l=Number(e.minCropBoxWidth)||0,h=Number(e.minCropBoxHeight)||0,c=s?Math.min(o.width,r.width,r.width+r.left,o.width-r.left):o.width,d=s?Math.min(o.height,r.height,r.height+r.top,o.height-r.top):o.height;l=Math.min(l,o.width),h=Math.min(h,o.height),f&&(l&&h?h*f>l?h=l/f:l=h*f:l?h=l/f:h&&(l=h*f),d*f>c?d=c/f:c=d*f),n.minWidth=Math.min(l,c),n.minHeight=Math.min(h,d),n.maxWidth=c,n.maxHeight=d}i&&(s?(n.minLeft=Math.max(0,r.left),n.minTop=Math.max(0,r.top),n.maxLeft=Math.min(o.width,r.left+r.width)-n.width,n.maxTop=Math.min(o.height,r.top+r.height)-n.height):(n.minLeft=0,n.minTop=0,n.maxLeft=o.width-n.width,n.maxTop=o.height-n.height))},renderCropBox:function(){var t=this.options,i=this.containerData,e=this.cropBoxData;(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCropBox(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,t.movable&&t.cropBoxMovable&&st(this.face,ot,e.width>=i.width&&e.height>=i.height?ie:At),W(this.cropBox,D({width:e.width,height:e.height},nt({translateX:e.left,translateY:e.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),tt(this.element,xt,this.getData())}},Je={initPreview:function(){var t=this.element,i=this.crossOrigin,e=this.options.preview,o=i?this.crossOriginUrl:this.url,r=t.alt||"The image to preview",n=document.createElement("img");if(i&&(n.crossOrigin=i),n.src=o,n.alt=r,this.viewBox.appendChild(n),this.viewBoxImage=n,!!e){var s=e;typeof e=="string"?s=t.ownerDocument.querySelectorAll(e):e.querySelector&&(s=[e]),this.previews=s,C(s,function(f){var l=document.createElement("img");st(f,ct,{width:f.offsetWidth,height:f.offsetHeight,html:f.innerHTML}),i&&(l.crossOrigin=i),l.src=o,l.alt=r,l.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',f.innerHTML="",f.appendChild(l)})}},resetPreview:function(){C(this.previews,function(t){var i=Tt(t,ct);W(t,{width:i.width,height:i.height}),t.innerHTML=i.html,ze(t,ct)})},preview:function(){var t=this.imageData,i=this.canvasData,e=this.cropBoxData,o=e.width,r=e.height,n=t.width,s=t.height,f=e.left-i.left-t.left,l=e.top-i.top-t.top;!this.cropped||this.disabled||(W(this.viewBoxImage,D({width:n,height:s},nt(D({translateX:-f,translateY:-l},t)))),C(this.previews,function(h){var c=Tt(h,ct),d=c.width,g=c.height,u=d,y=g,v=1;o&&(v=d/o,y=r*v),r&&y>g&&(v=g/r,u=o*v,y=g),W(h,{width:u,height:y}),W(h.getElementsByTagName("img")[0],D({width:n*v,height:s*v},nt(D({translateX:-f*v,translateY:-l*v},t))))}))}},ti={bind:function(){var t=this.element,i=this.options,e=this.cropper;A(i.cropstart)&&B(t,Mt,i.cropstart),A(i.cropmove)&&B(t,Et,i.cropmove),A(i.cropend)&&B(t,Dt,i.cropend),A(i.crop)&&B(t,xt,i.crop),A(i.zoom)&&B(t,Ct,i.zoom),B(e,Ht,this.onCropStart=this.cropStart.bind(this)),i.zoomable&&i.zoomOnWheel&&B(e,Vt,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&B(e,zt,this.onDblclick=this.dblclick.bind(this)),B(t.ownerDocument,Wt,this.onCropMove=this.cropMove.bind(this)),B(t.ownerDocument,Ut,this.onCropEnd=this.cropEnd.bind(this)),i.responsive&&B(window,$t,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,i=this.options,e=this.cropper;A(i.cropstart)&&L(t,Mt,i.cropstart),A(i.cropmove)&&L(t,Et,i.cropmove),A(i.cropend)&&L(t,Dt,i.cropend),A(i.crop)&&L(t,xt,i.crop),A(i.zoom)&&L(t,Ct,i.zoom),L(e,Ht,this.onCropStart),i.zoomable&&i.zoomOnWheel&&L(e,Vt,this.onWheel,{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&L(e,zt,this.onDblclick),L(t.ownerDocument,Wt,this.onCropMove),L(t.ownerDocument,Ut,this.onCropEnd),i.responsive&&L(window,$t,this.onResize)}},ei={resize:function(){if(!this.disabled){var t=this.options,i=this.container,e=this.containerData,o=i.offsetWidth/e.width,r=i.offsetHeight/e.height,n=Math.abs(o-1)>Math.abs(r-1)?o:r;if(n!==1){var s,f;t.restore&&(s=this.getCanvasData(),f=this.getCropBoxData()),this.render(),t.restore&&(this.setCanvasData(C(s,function(l,h){s[h]=l*n})),this.setCropBoxData(C(f,function(l,h){f[h]=l*n})))}}},dblclick:function(){this.disabled||this.options.dragMode===ne||this.setDragMode(Ye(this.dragBox,yt)?re:Rt)},wheel:function(t){var i=this,e=Number(this.options.wheelZoomRatio)||.1,o=1;this.disabled||(t.preventDefault(),!this.wheeling&&(this.wheeling=!0,setTimeout(function(){i.wheeling=!1},50),t.deltaY?o=t.deltaY>0?1:-1:t.wheelDelta?o=-t.wheelDelta/120:t.detail&&(o=t.detail>0?1:-1),this.zoom(-o*e,t)))},cropStart:function(t){var i=t.buttons,e=t.button;if(!(this.disabled||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(m(i)&&i!==1||m(e)&&e!==0||t.ctrlKey))){var o=this.options,r=this.pointers,n;t.changedTouches?C(t.changedTouches,function(s){r[s.identifier]=lt(s)}):r[t.pointerId||0]=lt(t),Object.keys(r).length>1&&o.zoomable&&o.zoomOnTouch?n=ae:n=Tt(t.target,ot),Ne.test(n)&&tt(this.element,Mt,{originalEvent:t,action:n})!==!1&&(t.preventDefault(),this.action=n,this.cropping=!1,n===ee&&(this.cropping=!0,O(this.dragBox,dt)))}},cropMove:function(t){var i=this.action;if(!(this.disabled||!i)){var e=this.pointers;t.preventDefault(),tt(this.element,Et,{originalEvent:t,action:i})!==!1&&(t.changedTouches?C(t.changedTouches,function(o){D(e[o.identifier]||{},lt(o,!0))}):D(e[t.pointerId||0]||{},lt(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var i=this.action,e=this.pointers;t.changedTouches?C(t.changedTouches,function(o){delete e[o.identifier]}):delete e[t.pointerId||0],i&&(t.preventDefault(),Object.keys(e).length||(this.action=""),this.cropping&&(this.cropping=!1,Z(this.dragBox,dt,this.cropped&&this.options.modal)),tt(this.element,Dt,{originalEvent:t,action:i}))}}},ii={change:function(t){var i=this.options,e=this.canvasData,o=this.containerData,r=this.cropBoxData,n=this.pointers,s=this.action,f=i.aspectRatio,l=r.left,h=r.top,c=r.width,d=r.height,g=l+c,u=h+d,y=0,v=0,b=o.width,E=o.height,M=!0,Y;!f&&t.shiftKey&&(f=c&&d?c/d:1),this.limited&&(y=r.minLeft,v=r.minTop,b=y+Math.min(o.width,e.width,e.left+e.width),E=v+Math.min(o.height,e.height,e.top+e.height));var I=n[Object.keys(n)[0]],p={x:I.endX-I.startX,y:I.endY-I.startY},w=function(S){switch(S){case G:g+p.x>b&&(p.x=b-g);break;case q:l+p.x<y&&(p.x=y-l);break;case H:h+p.y<v&&(p.y=v-h);break;case K:u+p.y>E&&(p.y=E-u);break}};switch(s){case At:l+=p.x,h+=p.y;break;case G:if(p.x>=0&&(g>=b||f&&(h<=v||u>=E))){M=!1;break}w(G),c+=p.x,c<0&&(s=q,c=-c,l-=c),f&&(d=c/f,h+=(r.height-d)/2);break;case H:if(p.y<=0&&(h<=v||f&&(l<=y||g>=b))){M=!1;break}w(H),d-=p.y,h+=p.y,d<0&&(s=K,d=-d,h-=d),f&&(c=d*f,l+=(r.width-c)/2);break;case q:if(p.x<=0&&(l<=y||f&&(h<=v||u>=E))){M=!1;break}w(q),c-=p.x,l+=p.x,c<0&&(s=G,c=-c,l-=c),f&&(d=c/f,h+=(r.height-d)/2);break;case K:if(p.y>=0&&(u>=E||f&&(l<=y||g>=b))){M=!1;break}w(K),d+=p.y,d<0&&(s=H,d=-d,h-=d),f&&(c=d*f,l+=(r.width-c)/2);break;case et:if(f){if(p.y<=0&&(h<=v||g>=b)){M=!1;break}w(H),d-=p.y,h+=p.y,c=d*f}else w(H),w(G),p.x>=0?g<b?c+=p.x:p.y<=0&&h<=v&&(M=!1):c+=p.x,p.y<=0?h>v&&(d-=p.y,h+=p.y):(d-=p.y,h+=p.y);c<0&&d<0?(s=rt,d=-d,c=-c,h-=d,l-=c):c<0?(s=it,c=-c,l-=c):d<0&&(s=at,d=-d,h-=d);break;case it:if(f){if(p.y<=0&&(h<=v||l<=y)){M=!1;break}w(H),d-=p.y,h+=p.y,c=d*f,l+=r.width-c}else w(H),w(q),p.x<=0?l>y?(c-=p.x,l+=p.x):p.y<=0&&h<=v&&(M=!1):(c-=p.x,l+=p.x),p.y<=0?h>v&&(d-=p.y,h+=p.y):(d-=p.y,h+=p.y);c<0&&d<0?(s=at,d=-d,c=-c,h-=d,l-=c):c<0?(s=et,c=-c,l-=c):d<0&&(s=rt,d=-d,h-=d);break;case rt:if(f){if(p.x<=0&&(l<=y||u>=E)){M=!1;break}w(q),c-=p.x,l+=p.x,d=c/f}else w(K),w(q),p.x<=0?l>y?(c-=p.x,l+=p.x):p.y>=0&&u>=E&&(M=!1):(c-=p.x,l+=p.x),p.y>=0?u<E&&(d+=p.y):d+=p.y;c<0&&d<0?(s=et,d=-d,c=-c,h-=d,l-=c):c<0?(s=at,c=-c,l-=c):d<0&&(s=it,d=-d,h-=d);break;case at:if(f){if(p.x>=0&&(g>=b||u>=E)){M=!1;break}w(G),c+=p.x,d=c/f}else w(K),w(G),p.x>=0?g<b?c+=p.x:p.y>=0&&u>=E&&(M=!1):c+=p.x,p.y>=0?u<E&&(d+=p.y):d+=p.y;c<0&&d<0?(s=it,d=-d,c=-c,h-=d,l-=c):c<0?(s=rt,c=-c,l-=c):d<0&&(s=et,d=-d,h-=d);break;case ie:this.move(p.x,p.y),M=!1;break;case ae:this.zoom(We(n),t),M=!1;break;case ee:if(!p.x||!p.y){M=!1;break}Y=de(this.cropper),l=I.startX-Y.left,h=I.startY-Y.top,c=r.minWidth,d=r.minHeight,p.x>0?s=p.y>0?at:et:p.x<0&&(l-=c,s=p.y>0?rt:it),p.y<0&&(h-=d),this.cropped||(_(this.cropBox,R),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0));break}M&&(r.width=c,r.height=d,r.left=l,r.top=h,this.action=s,this.renderCropBox()),C(n,function(T){T.startX=T.endX,T.startY=T.endY})}},ai={crop:function(){return this.ready&&!this.cropped&&!this.disabled&&(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&O(this.dragBox,dt),_(this.cropBox,R),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=D({},this.initialImageData),this.canvasData=D({},this.initialCanvasData),this.cropBoxData=D({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(D(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),_(this.dragBox,dt),O(this.cropBox,R)),this},replace:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return!this.disabled&&t&&(this.isImg&&(this.element.src=t),i?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,C(this.previews,function(e){e.getElementsByTagName("img")[0].src=t}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,_(this.cropper,Yt)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,O(this.cropper,Yt)),this},destroy:function(){var t=this.element;return t[x]?(t[x]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,o=e.left,r=e.top;return this.moveTo(gt(t)?t:o+Number(t),gt(i)?i:r+Number(i))},moveTo:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,o=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.movable&&(m(t)&&(e.left=t,o=!0),m(i)&&(e.top=i,o=!0),o&&this.renderCanvas(!0)),this},zoom:function(t,i){var e=this.canvasData;return t=Number(t),t<0?t=1/(1-t):t=1+t,this.zoomTo(e.width*t/e.naturalWidth,null,i)},zoomTo:function(t,i,e){var o=this.options,r=this.canvasData,n=r.width,s=r.height,f=r.naturalWidth,l=r.naturalHeight;if(t=Number(t),t>=0&&this.ready&&!this.disabled&&o.zoomable){var h=f*t,c=l*t;if(tt(this.element,Ct,{ratio:t,oldRatio:n/f,originalEvent:e})===!1)return this;if(e){var d=this.pointers,g=de(this.cropper),u=d&&Object.keys(d).length?Ue(d):{pageX:e.pageX,pageY:e.pageY};r.left-=(h-n)*((u.pageX-g.left-r.left)/n),r.top-=(c-s)*((u.pageY-g.top-r.top)/s)}else Q(i)&&m(i.x)&&m(i.y)?(r.left-=(h-n)*((i.x-r.left)/n),r.top-=(c-s)*((i.y-r.top)/s)):(r.left-=(h-n)/2,r.top-=(c-s)/2);r.width=h,r.height=c,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return t=Number(t),m(t)&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var i=this.imageData.scaleY;return this.scale(t,m(i)?i:1)},scaleY:function(t){var i=this.imageData.scaleX;return this.scale(m(i)?i:1,t)},scale:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.imageData,o=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.scalable&&(m(t)&&(e.scaleX=t,o=!0),m(i)&&(e.scaleY=i,o=!0),o&&this.renderCanvas(!0,!0)),this},getData:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,i=this.options,e=this.imageData,o=this.canvasData,r=this.cropBoxData,n;if(this.ready&&this.cropped){n={x:r.left-o.left,y:r.top-o.top,width:r.width,height:r.height};var s=e.width/e.naturalWidth;if(C(n,function(h,c){n[c]=h/s}),t){var f=Math.round(n.y+n.height),l=Math.round(n.x+n.width);n.x=Math.round(n.x),n.y=Math.round(n.y),n.width=l-n.x,n.height=f-n.y}}else n={x:0,y:0,width:0,height:0};return i.rotatable&&(n.rotate=e.rotate||0),i.scalable&&(n.scaleX=e.scaleX||1,n.scaleY=e.scaleY||1),n},setData:function(t){var i=this.options,e=this.imageData,o=this.canvasData,r={};if(this.ready&&!this.disabled&&Q(t)){var n=!1;i.rotatable&&m(t.rotate)&&t.rotate!==e.rotate&&(e.rotate=t.rotate,n=!0),i.scalable&&(m(t.scaleX)&&t.scaleX!==e.scaleX&&(e.scaleX=t.scaleX,n=!0),m(t.scaleY)&&t.scaleY!==e.scaleY&&(e.scaleY=t.scaleY,n=!0)),n&&this.renderCanvas(!0,!0);var s=e.width/e.naturalWidth;m(t.x)&&(r.left=t.x*s+o.left),m(t.y)&&(r.top=t.y*s+o.top),m(t.width)&&(r.width=t.width*s),m(t.height)&&(r.height=t.height*s),this.setCropBoxData(r)}return this},getContainerData:function(){return this.ready?D({},this.containerData):{}},getImageData:function(){return this.sized?D({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,i={};return this.ready&&C(["left","top","width","height","naturalWidth","naturalHeight"],function(e){i[e]=t[e]}),i},setCanvasData:function(t){var i=this.canvasData,e=i.aspectRatio;return this.ready&&!this.disabled&&Q(t)&&(m(t.left)&&(i.left=t.left),m(t.top)&&(i.top=t.top),m(t.width)?(i.width=t.width,i.height=t.width/e):m(t.height)&&(i.height=t.height,i.width=t.height*e),this.renderCanvas(!0)),this},getCropBoxData:function(){var t=this.cropBoxData,i;return this.ready&&this.cropped&&(i={left:t.left,top:t.top,width:t.width,height:t.height}),i||{}},setCropBoxData:function(t){var i=this.cropBoxData,e=this.options.aspectRatio,o,r;return this.ready&&this.cropped&&!this.disabled&&Q(t)&&(m(t.left)&&(i.left=t.left),m(t.top)&&(i.top=t.top),m(t.width)&&t.width!==i.width&&(o=!0,i.width=t.width),m(t.height)&&t.height!==i.height&&(r=!0,i.height=t.height),e&&(o?i.height=i.width/e:r&&(i.width=i.height*e)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var i=this.canvasData,e=$e(this.image,this.imageData,i,t);if(!this.cropped)return e;var o=this.getData(t.rounded),r=o.x,n=o.y,s=o.width,f=o.height,l=e.width/Math.floor(i.naturalWidth);l!==1&&(r*=l,n*=l,s*=l,f*=l);var h=s/f,c=U({aspectRatio:h,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),d=U({aspectRatio:h,width:t.minWidth||0,height:t.minHeight||0},"cover"),g=U({aspectRatio:h,width:t.width||(l!==1?e.width:s),height:t.height||(l!==1?e.height:f)}),u=g.width,y=g.height;u=Math.min(c.width,Math.max(d.width,u)),y=Math.min(c.height,Math.max(d.height,y));var v=document.createElement("canvas"),b=v.getContext("2d");v.width=J(u),v.height=J(y),b.fillStyle=t.fillColor||"transparent",b.fillRect(0,0,u,y);var E=t.imageSmoothingEnabled,M=E===void 0?!0:E,Y=t.imageSmoothingQuality;b.imageSmoothingEnabled=M,Y&&(b.imageSmoothingQuality=Y);var I=e.width,p=e.height,w=r,T=n,S,X,j,$,z,k;w<=-s||w>I?(w=0,S=0,j=0,z=0):w<=0?(j=-w,w=0,S=Math.min(I,s+w),z=S):w<=I&&(j=0,S=Math.min(s,I-w),z=S),S<=0||T<=-f||T>p?(T=0,X=0,$=0,k=0):T<=0?($=-T,T=0,X=Math.min(p,f+T),k=X):T<=p&&($=0,X=Math.min(f,p-T),k=X);var N=[w,T,S,X];if(z>0&&k>0){var V=u/s;N.push(j*V,$*V,z*V,k*V)}return b.drawImage.apply(b,[e].concat(te(N.map(function(ht){return Math.floor(J(ht))})))),v},setAspectRatio:function(t){var i=this.options;return!this.disabled&&!gt(t)&&(i.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var i=this.options,e=this.dragBox,o=this.face;if(this.ready&&!this.disabled){var r=t===Rt,n=i.movable&&t===re;t=r||n?t:ne,i.dragMode=t,st(e,ot,t),Z(e,yt,r),Z(e,bt,n),i.cropBoxMovable||(st(o,ot,t),Z(o,yt,r),Z(o,bt,n))}return this}},ri=P.Cropper,pe=function(){function a(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(ve(this,a),!t||!Ie.test(t.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=t,this.options=D({},qt,Q(i)&&i),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return we(a,[{key:"init",value:function(){var i=this.element,e=i.tagName.toLowerCase(),o;if(!i[x]){if(i[x]=this,e==="img"){if(this.isImg=!0,o=i.getAttribute("src")||"",this.originalUrl=o,!o)return;o=i.src}else e==="canvas"&&window.HTMLCanvasElement&&(o=i.toDataURL());this.load(o)}}},{key:"load",value:function(i){var e=this;if(i){this.url=i,this.imageData={};var o=this.element,r=this.options;if(!r.rotatable&&!r.scalable&&(r.checkOrientation=!1),!r.checkOrientation||!window.ArrayBuffer){this.clone();return}if(Ae.test(i)){Re.test(i)?this.read(qe(i)):this.clone();return}var n=new XMLHttpRequest,s=this.clone.bind(this);this.reloading=!0,this.xhr=n,n.onabort=s,n.onerror=s,n.ontimeout=s,n.onprogress=function(){n.getResponseHeader("content-type")!==Gt&&n.abort()},n.onload=function(){e.read(n.response)},n.onloadend=function(){e.reloading=!1,e.xhr=null},r.checkCrossOrigin&&Kt(i)&&o.crossOrigin&&(i=Qt(i)),n.open("GET",i,!0),n.responseType="arraybuffer",n.withCredentials=o.crossOrigin==="use-credentials",n.send()}}},{key:"read",value:function(i){var e=this.options,o=this.imageData,r=Ke(i),n=0,s=1,f=1;if(r>1){this.url=Fe(i,Gt);var l=Qe(r);n=l.rotate,s=l.scaleX,f=l.scaleY}e.rotatable&&(o.rotate=n),e.scalable&&(o.scaleX=s,o.scaleY=f),this.clone()}},{key:"clone",value:function(){var i=this.element,e=this.url,o=i.crossOrigin,r=e;this.options.checkCrossOrigin&&Kt(e)&&(o||(o="anonymous"),r=Qt(e)),this.crossOrigin=o,this.crossOriginUrl=r;var n=document.createElement("img");o&&(n.crossOrigin=o),n.src=r||e,n.alt=i.alt||"The image to crop",this.image=n,n.onload=this.start.bind(this),n.onerror=this.stop.bind(this),O(n,Xt),i.parentNode.insertBefore(n,i.nextSibling)}},{key:"start",value:function(){var i=this,e=this.image;e.onload=null,e.onerror=null,this.sizing=!0;var o=P.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(P.navigator.userAgent),r=function(l,h){D(i.imageData,{naturalWidth:l,naturalHeight:h,aspectRatio:l/h}),i.initialImageData=D({},i.imageData),i.sizing=!1,i.sized=!0,i.build()};if(e.naturalWidth&&!o){r(e.naturalWidth,e.naturalHeight);return}var n=document.createElement("img"),s=document.body||document.documentElement;this.sizingImage=n,n.onload=function(){r(n.width,n.height),o||s.removeChild(n)},n.src=e.src,o||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",s.appendChild(n))}},{key:"stop",value:function(){var i=this.image;i.onload=null,i.onerror=null,i.parentNode.removeChild(i),this.image=null}},{key:"build",value:function(){if(!(!this.sized||this.ready)){var i=this.element,e=this.options,o=this.image,r=i.parentNode,n=document.createElement("div");n.innerHTML=Se;var s=n.querySelector(".".concat(x,"-container")),f=s.querySelector(".".concat(x,"-canvas")),l=s.querySelector(".".concat(x,"-drag-box")),h=s.querySelector(".".concat(x,"-crop-box")),c=h.querySelector(".".concat(x,"-face"));this.container=r,this.cropper=s,this.canvas=f,this.dragBox=l,this.cropBox=h,this.viewBox=s.querySelector(".".concat(x,"-view-box")),this.face=c,f.appendChild(o),O(i,R),r.insertBefore(s,i.nextSibling),_(o,Xt),this.initPreview(),this.bind(),e.initialAspectRatio=Math.max(0,e.initialAspectRatio)||NaN,e.aspectRatio=Math.max(0,e.aspectRatio)||NaN,e.viewMode=Math.max(0,Math.min(3,Math.round(e.viewMode)))||0,O(h,R),e.guides||O(h.getElementsByClassName("".concat(x,"-dashed")),R),e.center||O(h.getElementsByClassName("".concat(x,"-center")),R),e.background&&O(s,"".concat(x,"-bg")),e.highlight||O(c,Me),e.cropBoxMovable&&(O(c,bt),st(c,ot,At)),e.cropBoxResizable||(O(h.getElementsByClassName("".concat(x,"-line")),R),O(h.getElementsByClassName("".concat(x,"-point")),R)),this.render(),this.ready=!0,this.setDragMode(e.dragMode),e.autoCrop&&this.crop(),this.setData(e.data),A(e.ready)&&B(i,jt,e.ready,{once:!0}),tt(i,jt)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var i=this.cropper.parentNode;i&&i.removeChild(this.cropper),_(this.element,R)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=ri,a}},{key:"setDefaults",value:function(i){D(qt,Q(i)&&i)}}])}();D(pe.prototype,Ze,Je,ti,ei,ii,ai);document.addEventListener("DOMContentLoaded",function(){window.profileCropperConfig&&window.profileCropperConfig.forEach(a=>{const t=document.getElementById(`${a}-profileImageInput`),i=document.getElementById(`${a}-imageToCrop`),e=document.getElementById(`${a}-cropContainer`),o=document.getElementById(`${a}-displayContainer`),r=document.getElementById(`${a}-profileImageViewer`),n=document.getElementById(`${a}-editContainer`),s=document.getElementById(`${a}-croppedImage`),f=document.getElementById(`${a}-cropButton`),l=document.getElementById(`${a}-modifyButton`),h=document.getElementById(`${a}-deleteButton`),c=document.getElementById(`${a}-uploadButton`),d=document.getElementById(`${a}-modal`),g=document.getElementById(`${a}-closeModal`);let u;c.addEventListener("click",function(){t.click()}),t.addEventListener("change",function(y){const v=y.target.files[0];if(v){const b=new FileReader;b.onload=function(E){i.src=E.target.result,d.classList.remove("hidden"),u&&u.destroy(),u=new pe(i,{aspectRatio:1,viewMode:1})},b.readAsDataURL(v),e.style.display="block"}}),f.addEventListener("click",function(){u&&u.getCroppedCanvas({width:300,height:300}).toBlob(v=>{const b=URL.createObjectURL(v);s.src=b,o.style.display="flex",n.style.display="none",r.style.display="none",d.classList.add("hidden")})}),g.addEventListener("click",function(){d.classList.add("hidden"),t.value=""}),l.addEventListener("click",function(){d.classList.remove("hidden"),t.value=""}),h.addEventListener("click",function(){u&&(u.destroy(),u=null),s.src="",i.src="",t.value="",o.style.display="none",n.style.display="block",e.style.display="none",r.style.display="flex"})})});
