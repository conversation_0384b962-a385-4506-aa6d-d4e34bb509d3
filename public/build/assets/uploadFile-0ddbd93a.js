document.addEventListener("DOMContentLoaded",function(){window.uploadFileConfig&&window.uploadFileConfig.forEach(e=>{const n=document.getElementById(`${e}-fileInput`),i=document.getElementById(`${e}-displayContainer`),d=document.getElementById(`${e}-editContainer`),a=document.getElementById(`${e}-fileName`),t=document.getElementById(`${e}-filePreview`),u=document.getElementById(`${e}-modifyButton`),f=document.getElementById(`${e}-deleteButton`),y=document.getElementById(`${e}-uploadButton`),l=document.getElementById(`${e}-fileData`);y.addEventListener("click",function(){n.click()}),n.addEventListener("change",function(m){const o=m.target.files[0];if(o){a.textContent=o.name;const c=new FileReader;c.onload=function(s){l&&(l.value=s.target.result),o.type.startsWith("image/")?(t.src=s.target.result,t.style.display="block"):(t.style.display="none",t.src="#")},c.readAsDataURL(o),i.style.display="flex",d.style.display="none"}}),u.addEventListener("click",function(){n.click()}),f.addEventListener("click",function(){n.value="",a.textContent="",t.src="#",t.style.display="none",l&&(l.value=""),i.style.display="none",d.style.display="flex"})})});
