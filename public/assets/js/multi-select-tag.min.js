function MultiSelectTag(e,t){var n,l,r,i,a,o=[],c=t.onChange||function(){},d=t.required||!1,u="number"==typeof t.maxSelection?t.maxSelection:1/0,s=t.placeholder||"Search",f=[],v=[],p=-1;if(!(n=document.getElementById(e)))throw new Error("Select element not found.");if("SELECT"!==n.tagName)throw new Error("Element is not a select element.");n.style.display="none";for(var h=0;h<n.options.length;h++){var g=n.options[h];o.push({id:g.value,label:g.text,preselected:g.selected})}(l=document.createElement("div")).className="multi-select-tag",n.parentNode.insertBefore(l,n.nextSibling);for(var m=0;m<o.length;m++)o[m].preselected&&f.push({id:o[m].id,label:o[m].label});function normalizeText(e){return e.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[çÇ]/g,"c").replace(/[œŒ]/g,"oe").replace(/[æÆ]/g,"ae")}function E(){a.innerHTML="";var e=v.filter(function(e){return!f.find(function(t){return t.id===e.id})});if(0!==e.length){if(e.forEach(function(e,t){var n=document.createElement("li");n.textContent=e.label,n.className="li",t===p&&n.classList.add("li-arrow"),n.addEventListener("click",function(){L(e)}),a.appendChild(n)}),a.classList.remove("hidden"),p>-1){var t=a.children[p];t&&t.scrollIntoView({block:"nearest"})}}else a.classList.add("hidden")}function w(){for(var e=r.querySelectorAll(".tag-item"),t=0;t<e.length;t++)e[t].remove();f.forEach(function(e){var t=document.createElement("span");t.className="tag-item",t.textContent=e.label;var n=document.createElement("span");n.className="cross",n.innerHTML="&times;",n.addEventListener("click",function(){!function(e){f=f.filter(function(t){return t.id!==e.id}),w(),E(),b(),c(f)}(e)}),t.appendChild(n),r.insertBefore(t,i)})}function L(e){f.length>=u||(f.find(function(t){return t.id===e.id})||(f.push({id:e.id,label:e.label}),i.value="",v=o.filter(function(e){return normalizeText(e.label).includes(normalizeText(i.value))}),p=-1,w(),E(),b(),c(f)))}function b(){for(var e=0;e<n.options.length;e++){var t=n.options[e],l=f.find(function(e){return e.id===t.value});t.selected=!!l}i.required=!!d&&!f.length}return v=o.slice(),l.innerHTML='\n        <div class="wrapper">\n          <div id="selected-tags" class="tag-container">\n            <input type="text" id="tag-input" placeholder="'+s+'" class="tag-input" autocomplete="off">\n          </div>\n          <ul id="dropdown" class="dropdown hidden"></ul>\n        </div>',r=l.querySelector("#selected-tags"),i=l.querySelector("#tag-input"),a=l.querySelector("#dropdown"),i.addEventListener("input",function(e){var t=normalizeText(e.target.value);v=o.filter(function(e){return normalizeText(e.label).includes(t)}),p=-1,E()}),i.addEventListener("keydown",function(e){var t=a.querySelectorAll("li");if("Backspace"===e.key&&""===i.value&&f.length>0)return f.pop(),w(),E(),b(),c(f),void e.preventDefault();if("ArrowDown"===e.key){if(e.preventDefault(),0===t.length)return;p=(p+1)%t.length,E()}else if("ArrowUp"===e.key){if(e.preventDefault(),0===t.length)return;p=(p-1+t.length)%t.length,E()}else if("Enter"===e.key&&(e.preventDefault(),p>-1&&t[p])){var n=t[p].textContent,l=o.find(function(e){return e.label===n});l&&L(l)}}),document.addEventListener("click",function(e){l.contains(e.target)||(p=-1,a.classList.add("hidden"))}),i.addEventListener("focus",function(){E()}),w(),b(),{selectAll:function(){for(var e=0;e<o.length&&!(f.length>=u);e++){var t=o[e];f.find(function(e){return e.id===t.id})||f.push({id:t.id,label:t.label})}i.value="",v=o.slice(),p=-1,w(),E(),b(),c(f)},clearAll:function(){f=[],w(),E(),b(),c(f)},getSelectedTags:function(){return f}}}