<?php

namespace Database\Seeders;

use App\Models\Country;
use App\Models\Region;
use Illuminate\Database\Seeder;

class RegionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupère les pays
        $switzerland = Country::where('code', 'CH')->first();
        $france = Country::where('code', 'FR')->first();
        $germany = Country::where('code', 'DE')->first();
        $italy = Country::where('code', 'IT')->first();
        $austria = Country::where('code', 'AT')->first();

        if (!$switzerland) {
            $this->command->error('La Suisse n\'a pas été trouvée dans la table countries. Exécutez d\'abord CountrySeeder.');
            return;
        }

        if (!$france) {
            $this->command->error('La France n\'a pas été trouvée dans la table countries. Exécutez d\'abord CountrySeeder.');
            return;
        }

        if (!$germany) {
            $this->command->error('L\'Allemagne n\'a pas été trouvée dans la table countries. Exécutez d\'abord CountrySeeder.');
            return;
        }

        if (!$italy) {
            $this->command->error('L\'Italie n\'a pas été trouvée dans la table countries. Exécutez d\'abord CountrySeeder.');
            return;
        }

        if (!$austria) {
            $this->command->error('L\'Autriche n\'a pas été trouvée dans la table countries. Exécutez d\'abord CountrySeeder.');
            return;
        }

        $regions = [
            // Suisse
            ['name' => 'Appenzell Rhodes-Extérieures', 'country_id' => $switzerland->id],
            ['name' => 'Appenzell Rhodes-Intérieures', 'country_id' => $switzerland->id],
            ['name' => 'Argovie', 'country_id' => $switzerland->id],
            ['name' => 'Bâle-Campagne', 'country_id' => $switzerland->id],
            ['name' => 'Bâle-Ville', 'country_id' => $switzerland->id],
            ['name' => 'Berne / Bern', 'country_id' => $switzerland->id],
            ['name' => 'Fribourg / Freiburg', 'country_id' => $switzerland->id],
            ['name' => 'Genève', 'country_id' => $switzerland->id],
            ['name' => 'Glaris', 'country_id' => $switzerland->id],
            ['name' => 'Grisons / Graubünden', 'country_id' => $switzerland->id],
            ['name' => 'Jura', 'country_id' => $switzerland->id],
            ['name' => 'Lucerne', 'country_id' => $switzerland->id],
            ['name' => 'Neuchâtel', 'country_id' => $switzerland->id],
            ['name' => 'Nidwald', 'country_id' => $switzerland->id],
            ['name' => 'Obwald', 'country_id' => $switzerland->id],
            ['name' => 'Saint-Gall', 'country_id' => $switzerland->id],
            ['name' => 'Schaffhouse', 'country_id' => $switzerland->id],
            ['name' => 'Schwytz', 'country_id' => $switzerland->id],
            ['name' => 'Soleure', 'country_id' => $switzerland->id],
            ['name' => 'Tessin', 'country_id' => $switzerland->id],
            ['name' => 'Thurgovie', 'country_id' => $switzerland->id],
            ['name' => 'Uri', 'country_id' => $switzerland->id],
            ['name' => 'Valais / Wallis', 'country_id' => $switzerland->id],
            ['name' => 'Vaud', 'country_id' => $switzerland->id],
            ['name' => 'Zoug', 'country_id' => $switzerland->id],
            ['name' => 'Zurich', 'country_id' => $switzerland->id],

            // France
            ['name' => 'Auvergne-Rhône-Alpes', 'country_id' => $france->id],
            ['name' => 'Bourgogne-Franche-Comté', 'country_id' => $france->id],
            ['name' => 'Bretagne', 'country_id' => $france->id],
            ['name' => 'Centre-Val de Loire', 'country_id' => $france->id],
            ['name' => 'Corse', 'country_id' => $france->id],
            ['name' => 'Grand Est', 'country_id' => $france->id],
            ['name' => 'Guadeloupe', 'country_id' => $france->id],
            ['name' => 'Guyane', 'country_id' => $france->id],
            ['name' => 'Hauts-de-France', 'country_id' => $france->id],
            ['name' => 'Île-de-France', 'country_id' => $france->id],
            ['name' => 'La Réunion', 'country_id' => $france->id],
            ['name' => 'Martinique', 'country_id' => $france->id],
            ['name' => 'Mayotte', 'country_id' => $france->id],
            ['name' => 'Normandie', 'country_id' => $france->id],
            ['name' => 'Nouvelle-Aquitaine', 'country_id' => $france->id],
            ['name' => 'Occitanie', 'country_id' => $france->id],
            ['name' => 'Pays de la Loire', 'country_id' => $france->id],
            ['name' => 'Provence-Alpes-Côte d\'Azur', 'country_id' => $france->id],

            // Allemagne (Länder)
            ['name' => 'Bade-Wurtemberg', 'country_id' => $germany->id],
            ['name' => 'Basse-Saxe', 'country_id' => $germany->id],
            ['name' => 'Bavière', 'country_id' => $germany->id],
            ['name' => 'Berlin', 'country_id' => $germany->id],
            ['name' => 'Brandebourg', 'country_id' => $germany->id],
            ['name' => 'Brême', 'country_id' => $germany->id],
            ['name' => 'Hambourg', 'country_id' => $germany->id],
            ['name' => 'Hesse', 'country_id' => $germany->id],
            ['name' => 'Mecklembourg-Poméranie-Occidentale', 'country_id' => $germany->id],
            ['name' => 'Rhénanie-du-Nord-Westphalie', 'country_id' => $germany->id],
            ['name' => 'Rhénanie-Palatinat', 'country_id' => $germany->id],
            ['name' => 'Sarre', 'country_id' => $germany->id],
            ['name' => 'Saxe', 'country_id' => $germany->id],
            ['name' => 'Saxe-Anhalt', 'country_id' => $germany->id],
            ['name' => 'Schleswig-Holstein', 'country_id' => $germany->id],
            ['name' => 'Thuringe', 'country_id' => $germany->id],

            // Italie (Régions)
            ['name' => 'Abruzzes', 'country_id' => $italy->id],
            ['name' => 'Basilicate', 'country_id' => $italy->id],
            ['name' => 'Calabre', 'country_id' => $italy->id],
            ['name' => 'Campanie', 'country_id' => $italy->id],
            ['name' => 'Émilie-Romagne', 'country_id' => $italy->id],
            ['name' => 'Frioul-Vénétie Julienne', 'country_id' => $italy->id],
            ['name' => 'Latium', 'country_id' => $italy->id],
            ['name' => 'Ligurie', 'country_id' => $italy->id],
            ['name' => 'Lombardie', 'country_id' => $italy->id],
            ['name' => 'Marches', 'country_id' => $italy->id],
            ['name' => 'Molise', 'country_id' => $italy->id],
            ['name' => 'Ombrie', 'country_id' => $italy->id],
            ['name' => 'Piémont', 'country_id' => $italy->id],
            ['name' => 'Pouilles', 'country_id' => $italy->id],
            ['name' => 'Sardaigne', 'country_id' => $italy->id],
            ['name' => 'Sicile', 'country_id' => $italy->id],
            ['name' => 'Toscane', 'country_id' => $italy->id],
            ['name' => 'Trentin-Haut-Adige', 'country_id' => $italy->id],
            ['name' => 'Vallée d\'Aoste', 'country_id' => $italy->id],
            ['name' => 'Vénétie', 'country_id' => $italy->id],

            // Autriche (Länder)
            ['name' => 'Basse-Autriche', 'country_id' => $austria->id],
            ['name' => 'Burgenland', 'country_id' => $austria->id],
            ['name' => 'Carinthie', 'country_id' => $austria->id],
            ['name' => 'Haute-Autriche', 'country_id' => $austria->id],
            ['name' => 'Salzbourg', 'country_id' => $austria->id],
            ['name' => 'Styrie', 'country_id' => $austria->id],
            ['name' => 'Tyrol', 'country_id' => $austria->id],
            ['name' => 'Vienne', 'country_id' => $austria->id],
            ['name' => 'Vorarlberg', 'country_id' => $austria->id],
        ];

        foreach ($regions as $region) {
            Region::create([
                'name' => $region['name'],
                'country_id' => $region['country_id'],
                'is_active' => true,
            ]);
        }

        $this->command->info('Toutes les régions ont été créées avec succès !');
    }
}
