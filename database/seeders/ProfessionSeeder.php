<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\FieldActivity;
use App\Models\Profession;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\AdminController; //
class ProfessionSeeder extends Seeder
{
    private $adminController;

    public function __construct(AdminController $adminController)
    {
        $this->adminController = $adminController;
    }

    /**
     * Get field activity ID by name
     */
    private function getActivityIdByName(string $name): ?string
    {
        $activity = FieldActivity::where('name', $name)->first();
        return $activity ? $activity->_id : null;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Tableau de correspondance entre domaines et professions basé sur les données Excel
        $domainMapping = [
            // Groupe de couleur 1
            'Paysagisme, Agriculture, horticulture, sylviculture, soins aux animaux' => [
                'Accompagnateur/trice en montagne',
                'Aboriculteur/trice',
                'Agriculteur/trice',
                'Agronome',
                'Arboriculteur/trice',
                'Bûcheron/ne',
                'Cavalier/ère professionnel/le',
                'Éleveur/euse de chiens',
                'Fleuriste',
                'Forestier/ère-bûcheron/ne',
                'Garde-faune',
                'Garde-forestier/ère',
                'Gardien/ne d\'animaux',
                'Horticulteur/trice',
                'Jardinier/ère',
                'Maraîcher/ère',
                'Paysagiste',
                'Professionnel/le du cheval',
                'Vétérinaire',
                'Viticulteur/trice',
                'Zoologiste',
            ],
            'Droit, sécurité, police' => [
                'Accompagnateur/trice en montagne',
                'Agent/e de détention',
                'Agent/e de sécurité',
                'Agent/e de sécurité publique',
                'Agent/e professionnel/le de sécurité',
                'Avocat/e',
                'Détective privé/e',
                'Garde-frontière',
                'Greffier/ière de tribunal',
                'Juge',
                'Juriste',
                'Notaire',
                'Policier/ère',
                'Sapeur-pompier professionnel/le',
            ],
            'Social, sciences humaines' => [
                'Accompagnateur/trice en montagne',
                'Accompagnateur/trice social/e',
                'Accompagnement/e socioprofessionnel/le',
                'Animateur/trice socioculturel/le',
                'Assistant/e social/e',
                'Conseiller/ère conjugal/e',
                'Conseiller/ère en orientation professionnelle',
                'Éducateur/trice de l\'enfance',
                'Éducateur/trice social/e',
                'Psychologue',
                'Sociologue',
                'Travailleur/euse social/e',
            ],

            // Groupe de couleur 2
            'Alimentation' => [
                'Accompagnateur/trice social/e',
                'Boucher/ère-charcutier/ère',
                'Boulanger/ère-pâtissier/ère',
                'Caviste',
                'Chef/fe de cuisine',
                'Chocolatier/ère',
                'Confiseur/euse-pâtissier/ère-glacier/ère',
                'Cuisinier/ère',
                'Cuisinier/ère en diététique',
                'Fromager/ère',
                'Meunier/ère',
                'Pâtissier/ère-confiseur/euse',
                'Technologue en denrées alimentaires',
                'Traiteur/euse',
            ],
            'Administration, économie' => [
                'Accompagnateur/trice social/e',
                'Actuaire',
                'Administrateur/trice de biens immobiliers',
                'Agent/e d\'affaires',
                'Agent/e fiduciaire',
                'Assistant/e de direction',
                'Comptable',
                'Conseiller/ère financier/ère',
                'Courtier/ère en assurances',
                'Économiste',
                'Employé/e de commerce',
                'Expert/e-comptable',
                'Gestionnaire en ressources humaines',
                'Responsable administratif/ve',
                'Secrétaire',
                'Secrétaire médical/e',
            ],
            'Informatique, multimédia' => [
                'Accompagnateur/trice social/e',
                'Administrateur/trice réseau',
                'Analyste en informatique',
                'Concepteur/trice multimédia',
                'Développeur/euse d\'applications',
                'Développeur/euse web',
                'Informaticien/ne',
                'Ingénieur/e en informatique',
                'Médiamaticien/ne',
                'Spécialiste en sécurité informatique',
                'Technicien/ne en informatique',
                'Webmaster',
            ],
            'Textile, habillement, soins corporels' => [
                'Accompagnateur/trice social/e',
                'Coiffeur/euse',
                'Cordonnier/ère',
                'Costumier/ère',
                'Couturier/ère',
                'Créateur/trice de vêtements',
                'Designer textile',
                'Esthéticien/ne',
                'Masseur/euse',
                'Modiste',
                'Styliste',
                'Tailleur/euse',
                'Technologue en textile',
            ],

            // Groupe de couleur 3
            'Bois, papier, cuir' => [
                'Accompagnement/e socioprofessionnel/le',
                'Charpentier/ère',
                'Doreur/euse-encadreur/euse',
                'Ébéniste',
                'Luthier/ère',
                'Menuisier/ère',
                'Papetier/ère',
                'Relieur/euse',
                'Scieur/euse de l\'industrie du bois',
                'Sculpteur/trice sur bois',
                'Tonnelier/ère',
            ],
            'Electricité, Electronique' => [
                'Accompagnement/e socioprofessionnel/le',
                'Automaticien/ne',
                'Électricien/ne',
                'Électricien/ne de montage',
                'Électricien/ne de réseau',
                'Électronicien/ne',
                'Électronicien/ne en multimédia',
                'Installateur/trice-électricien/ne',
                'Monteur/euse-automaticien/ne',
                'Technicien/ne en énergie',
            ],
            'Métallurgie, horlogerie' => [
                'Accompagnement/e socioprofessionnel/le',
                'Agent/e technique des matières synthétiques',
                'Bijoutier/ère',
                'Constructeur/trice métallique',
                'Dessinateur/trice en construction microtechnique',
                'Ferblantier/ère',
                'Horloger/ère',
                'Mécanicien/ne de production',
                'Micromécanicien/ne',
                'Opérateur/trice de machines automatisées',
                'Polymécanicien/ne',
                'Serrurier/ère',
                'Technicien/ne en microtechnique',
            ],
            'Médias, communication, marketing, industrie graphique' => [
                'Accompagnement/e socioprofessionnel/le',
                'Agent/e en information documentaire',
                'Animateur/trice radio ou télévision',
                'Correcteur/trice',
                'Graphiste',
                'Imprimeur/euse',
                'Journaliste',
                'Libraire',
                'Maquettiste',
                'Photographe',
                'Polygraphe',
                'Réalisateur/trice publicitaire',
                'Spécialiste en marketing',
                'Spécialiste en relations publiques',
                'Technicien/ne audiovisuel',
                'Technicien/ne en marketing',
            ],

            // Groupe de couleur 4
            'Arts appliqués, arts, musique' => [
                'Acousticien/ne en systèmes auditifs',
                'Architecte d\'intérieur',
                'Artiste de cirque',
                'Artiste plasticien/ne',
                'Art-thérapeute',
                'Céramiste',
                'Chorégraphe',
                'Comédien/ne',
                'Créateur/trice en arts visuels',
                'Danseur/euse interprète',
                'Designer',
                'Musicien/ne',
                'Peintre en décors de théâtre',
                'Réalisateur/trice de films',
                'Sculpteur/trice',
            ],
            'Enseignement' => [
                'Acousticien/ne en systèmes auditifs',
                'Enseignant/e d\'arts visuels',
                'Enseignant/e de musique',
                'Enseignant/e de rythmique',
                'Enseignant/e de sport',
                'Enseignant/e en école professionnelle',
                'Enseignant/e primaire',
                'Enseignant/e secondaire',
                'Enseignant/e spécialisé/e',
                'Formateur/trice',
                'Formateur/trice d\'adultes',
                'Professeur/e de danse',
                'Professeur/e de musique',
            ],
            'Mécanique, véhicules' => [
                'Acousticien/ne en systèmes auditifs',
                'Carrossier/ère-peintre',
                'Carrossier/ère-tôlier/ère',
                'Constructeur/trice d\'appareils industriels',
                'Dessinateur/trice-constructeur/trice industriel/le',
                'Ingénieur/e en mécanique',
                'Mécanicien/ne d\'appareils à moteur',
                'Mécanicien/ne en cycles',
                'Mécanicien/ne en machines agricoles',
                'Mécanicien/ne en machines de chantier',
                'Mécanicien/ne en motocycles',
                'Mécanicien/ne-boîtier/ère',
                'Mécatronicien/ne d\'automobiles',
                'Technicien/ne en systèmes industriels',
                'Analyste en informatique',
                'Concepteur/trice multimédia',
                'Développeur/euse d\'applications',
                'Développeur/euse web',
                'Médiamaticien/ne',
                'Spécialiste en sécurité informatique',
            ],
            'Transports, logistique' => [
                'Acousticien/ne en systèmes auditifs',
                'Agent/e de transport et logistique',
                'Agent/e de voyages',
                'Chauffeur/euse de taxi',
                'Chauffeur/euse poids lourds',
                'Conducteur/trice de véhicules légers',
                'Conducteur/trice de véhicules lourds',
                'Contrôleur/euse de la circulation aérienne',
                'Logisticien/ne',
                'Matelot de la navigation intérieure',
                'Pilote d\'avion',
                'Pilote de locomotive',
            ],

            // Groupe de couleur 5
            'Bâtiment, construction' => [
                'Actuaire',
                'Architecte',
                'Carreleur/euse',
                'Charpentier/ère',
                'Constructeur/trice de fondations',
                'Constructeur/trice de routes',
                'Constructeur/trice d\'éléments préfabriqués',
                'Contremaître/sse construction',
                'Couvreur/euse',
                'Dessinateur/trice en bâtiment',
                'Échafaudeur/euse',
                'Façadier/ère',
                'Géomaticien/ne',
                'Maçon/ne',
                'Peintre en bâtiment',
                'Plâtrier/ère-constructeur/trice à sec',
                'Plombier/ère',
                'Poseur/euse de pierres',
                'Vitrier/ère',
            ],
            'Environnement, nature' => [
                'Actuaire',
                'Biologiste',
                'Botaniste',
                'Conseiller/ère en environnement',
                'Écologue',
                'Géographe',
                'Géologue',
                'Hydrologue',
                'Ingénieur/e en environnement',
                'Météorologue',
                'Océanographe',
                'Recycleur/euse',
                'Spécialiste de la protection de l\'environnement',
                'Spécialiste en protection des eaux',
                'Technicien/ne en environnement',
            ],
            'Médecine, santé, sport' => [
                'Actuaire',
                'Ambulancier/ère',
                'Assistant/e dentaire',
                'Assistant/e en médecine vétérinaire',
                'Assistant/e médical/e',
                'Audioprothésiste',
                'Chiropraticien/ne',
                'Coach sportif',
                'Dentiste',
                'Diététicien/ne',
                'Droguiste',
                'Ergothérapeute',
                'Hygiéniste dentaire',
                'Infirmier/ère',
                'Kinésithérapeute',
                'Laborantin/e médical/e',
                'Médecin',
                'Naturopathe',
                'Opticien/ne',
                'Orthoptiste',
                'Ostéopathe',
                'Pharmacien/ne',
                'Physiothérapeute',
                'Podologue',
                'Sage-femme',
                'Technicien/ne en analyses biomédicales',
                'Technicien/ne en radiologie médicale',
                'Technicien/ne en salle d\'opération',
            ],
            'Intendance, garde d\'enfants' => [
                'Actuaire',
                'Agent/e d\'entretien',
                'Agent/e de propreté',
                'Concierge',
                'Employé/e en intendance',
                'Femme de chambre',
                'Garde d\'enfants',
                'Gouvernant/e',
                'Intendant/e',
                'Maman de jour',
                'Nettoyeur/euse de bâtiments',
            ],

            // Groupe de couleur 6
            'Biologie, chimie, physique' => [
                'Administrateur/trice de biens immobiliers',
                'Assistant/e en pharmacie',
                'Biochimiste',
                'Biologiste',
                'Chimiste',
                'Ingénieur/e chimiste',
                'Laborantin/e',
                'Laborantin/e en chimie',
                'Laborantin/e en physique',
                'Microbiologiste',
                'Physicien/ne',
                'Spécialiste pharmaceutique',
                'Technicien/ne en analyses biomédicales',
            ],
            'Hôtellerie, restauration, tourisme' => [
                'Administrateur/trice de biens immobiliers',
                'Agent/e de voyages',
                'Barman / Barmaid',
                'Cafetier/ère-restaurateur/trice',
                'Chef/fe de cuisine',
                'Chef/fe de réception',
                'Concierge d\'hôtel',
                'Cuisinier/ère',
                'Guide touristique',
                'Hôtelier/ère-restaurateur/trice',
                'Manager en tourisme',
                'Réceptionniste',
                'Responsable de la restauration',
                'Serveur/euse',
                'Sommelier/ère',
                'Spécialiste en restauration de système',
            ],
            'Vente, achat' => [
                'Administrateur/trice de biens immobiliers',
                'Agent/e immobilier/ère',
                'Agent/e relation client',
                'Chef/fe de vente',
                'Conseiller/ère de vente automobile',
                'Courtier/ère en immeubles',
                'Gestionnaire du commerce de détail',
                'Libraire',
                'Responsable commercial/e',
                'Spécialiste de vente',
                'Spécialiste du commerce de détail',
                'Spécialiste du commerce international',
                'Spécialiste du e-commerce',
                'Vendeur/euse',
            ]
        ];

        foreach ($domainMapping as $domainName => $professionNames) {
            // Récupérer l'ID du domaine d'activité
            $fieldActivityId = $this->getActivityIdByName($domainName);

            if (!$fieldActivityId) {
                $this->command->warn("Field activity '{$domainName}' not found, skipping professions in this domain.");
                continue; // Passer au domaine suivant si non trouvé
            }

            foreach ($professionNames as $professionName) {
                // Create a dummy Request
                $request = new Request([
                    'name' => $professionName,
                    'field_activity_id' => $fieldActivityId,
                ]);

                // Call the proffessionStore method
                $response = $this->adminController->proffessionStore($request);

                // Handle the response (which is a redirect in your controller)
                if ($response->getSession()->has('success')) {
                    $this->command->info("Profession '{$professionName}' added successfully.");
                } elseif ($response->getSession()->has('error')) {
                    $this->command->error("Error adding profession '{$professionName}': " . $response->getSession()->get('error'));
                }
            }
        }
    }
}
