<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ResidencePermitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
    //     <option value="Autorisation de séjour C" >Autorisation de séjour C</option>
	// <option value="Autorisation de séjour B" >Autorisation de séjour B</option>
	// <option value="Autorisation frontalière G" >Autorisation frontalière G</option>
	// <option value="Autorisation de courte durée L" >Autorisation de courte durée L</option>
	// <option value="Autorisation provisoire F" >Autorisation provisoire F</option>
	// <option value="Personne à protéger S" >Personne à protéger S</option>
	// <option value="Requérant/e d’asile N" >Requérant/e d’asile N</option>
	// <option value="Sans permis" >Sans permis</option>
	// <option value="Je passe cette étape" >Je passe cette étape</option>

        $permits = [
            ['name' => 'Autorisation de séjour C', 'slug' => 'autorisation-de-sejour-c'],
            ['name' => 'Autorisation de séjour B', 'slug' => 'autorisation-de-sejour-b'],
            ['name' => 'Autorisation frontalière G', 'slug' => 'autorisation-frontaliere-g'],
            ['name' => 'Autorisation de courte durée L', 'slug' => 'autorisation-de-courte-duree-l'],
            ['name' => 'Autorisation provisoire F', 'slug' => 'autorisation-provisoire-f'],
            ['name' => 'Personne à protéger S', 'slug' => 'personne-a-proteger-s'],
            ['name' => 'Requérant/e d\'asile N', 'slug' => 'requerant-e-d-asile-n'],
            ['name' => 'Sans permis', 'slug' => 'sans-permis'],
            //['name' => 'Je passe cette étape', 'slug' => 'je-passe-cette-etape'],
        ];

        foreach ($permits as $permit) {
            $permit['is_active'] = true;
            \App\Models\ResidencePermit::create($permit);
        }
 
    }
}
