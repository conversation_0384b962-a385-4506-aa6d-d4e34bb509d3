<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ResponsibilityCandidateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // <select class="pw_select2 pw_select" name="custom-select-40880843" id="custom-select-40880843" data-placeholder='Disponibilité ' data-hash='56s76qejpb70' placeholder="Disponibilité " required="required" data-allowclear='1' data-width='100%'><option></option>	<option value="À convenir" >À convenir</option>
        //     <option value="De suite" >De suite</option>
        //     <option value="Une semaine" >Une semaine</option>
        //     <option value="Un mois" >Un mois</option>
        //     <option value="Deux mois" >Deux mois</option>
        //     <option value="Trois mois" >Trois mois</option>
        //     <option value="Quarte mois et plus" >Quarte mois et plus</option>
        // </select>

        $responsibilityCandidates = [
            ['name' => 'À convenir', 'slug' => 'a-convenir'],
            ['name' => 'De suite', 'slug' => 'de-suite'],
            ['name' => 'Une semaine', 'slug' => 'une-semaine'],
            ['name' => 'Un mois', 'slug' => 'un-mois'],
            ['name' => 'Deux mois', 'slug' => 'deux-mois'],
            ['name' => 'Trois mois', 'slug' => 'trois-mois'],
            ['name' => 'Quarte mois et plus', 'slug' => 'quarte-mois-et-plus'],
        ];

        foreach ($responsibilityCandidates as $responsibilityCandidate) {
            $responsibilityCandidate['is_active'] = true;
            \App\Models\ResponsibilityCandidate::create($responsibilityCandidate);
        }

    }
}
