<?php
// database/seeders/PrioritySeeder.php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PrioritySeeder extends Seeder
{
    public function run()
    {
        $priorities = [
            ['name' => 'Aucune', 'value' => 10, 'is_default' => false],
            ['name' => '<PERSON><PERSON>', 'value' => 20, 'is_default' => false],
            ['name' => 'Normale', 'value' => 30, 'is_default' => true],
            ['name' => 'Élevée', 'value' => 40, 'is_default' => false],
            ['name' => 'Urgente', 'value' => 50, 'is_default' => false],
            ['name' => 'Immédiate', 'value' => 60, 'is_default' => false],
        ];

        DB::table('priorities')->insert($priorities);
    }
}