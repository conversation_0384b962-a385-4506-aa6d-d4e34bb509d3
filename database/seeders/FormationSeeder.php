<?php

namespace Database\Seeders;

use App\Models\Formation; 
use Illuminate\Database\Seeder;

class FormationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // <select multiple="multiple" style="width: 99%" class="pw_select2 pw_multiselect" name="custom-multiselect-35928394[]" id="custom-multiselect-35928394" data-placeholder='Plusieurs choix possible' data-hash='4h53a5p3nhk0' placeholder="Plusieurs choix possible" required="required">	<option value="A) Sans qualification" >A) Sans qualification</option>
        //     <option value="A1) Expérience professionnelle acquise sans formation " >A1) Expérience professionnelle acquise sans formation</option>
        //     <option value="A2) Prêt/e à être formé/e" >A2) Prêt/e à être formé/e</option>
        //     <option value="B) Etude secondaire niveau 1 (scolaire)" >B) Etude secondaire niveau 1 (scolaire)</option>
        //     <option value="C) Certificat maturité gymnasiale" >C) Certificat maturité gymnasiale</option>
        //     <option value="D) Attestation de formation professionnelle" >D) Attestation de formation professionnelle</option>
        //     <option value="E) Attestation fédérale de formation professionnelle (AFP)" >E) Attestation fédérale de formation professionnelle (AFP)</option>
        //     <option value="F) Certificat Fédérale de Capacité (CFC)" >F) Certificat Fédérale de Capacité (CFC)</option>
        //     <option value="G) Certificat et/ou diplôme professionnel" >G) Certificat et/ou diplôme professionnel</option>
        //     <option value="H) Brevet fédéral" >H) Brevet fédéral</option>
        //     <option value="I) Formation HES, HEP, EPF ou universitaire" >I) Formation HES, HEP, EPF ou universitaire</option>
        // </select>

        $formations = [
            ['name'=>'A) Sans qualification', 'slug'=>'a-sans-qualification'],
            ['name'=>'A1) Expérience professionnelle acquise sans formation', 'slug'=>'a1-experience-professionnelle-acquise-sans-formation'],
            ['name'=>'A2) Prêt/e à être formé/e', 'slug'=>'a2-pret-e-a-etre-forme-e'],
            ['name'=>'B) Etude secondaire niveau 1 (scolaire)', 'slug'=>'b-etude-secondaire-niveau-1-scolaire'],
            ['name'=>'C) Certificat maturité gymnasiale', 'slug'=>'c-certificat-maturite-gymnasiale'],
            ['name'=>'D) Attestation de formation professionnelle', 'slug'=>'d-attestation-de-formation-professionnelle'],
            ['name'=>'E) Attestation fédérale de formation professionnelle (AFP)', 'slug'=>'e-attestation-federale-de-formation-professionnelle-afp'],
            ['name'=>'F) Certificat Fédérale de Capacité (CFC)', 'slug'=>'f-certificat-federale-de-capacite-cfc'],
            ['name'=>'G) Certificat et/ou diplôme professionnel', 'slug'=>'g-certificat-et-ou-diplome-professionnel'],
            ['name'=>'H) Brevet fédéral', 'slug'=>'h-brevet-federal'],
            ['name'=>'I) Formation HES, HEP, EPF ou universitaire', 'slug'=>'i-formation-hes-hep-epf-ou-universitaire'],
        ];

        foreach ($formations as $formation) {
            $formation['is_active'] = true;
            \App\Models\Formation::create($formation);
        }
    }
}
