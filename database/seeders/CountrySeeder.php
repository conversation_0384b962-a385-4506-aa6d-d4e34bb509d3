<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //<select class="pw_select2 pw_select" name="custom-select-59755194" id="custom-select-59755194" data-placeholder='' data-hash='24j4rlm37csg' required="required" data-allowclear='1' data-width='100%'><option></option>	<option value="Suisse" >Suisse</option>
        // <option value="France" >France</option>
        // <option value="Allemagne" >Allemagne</option>
        // <option value="Italie" >Italie</option>
        // <option value="Autriche" >Autriche</option>

        $countries = [
            ['name' => 'Suisse', 'code' => 'CH'],
            ['name' => 'France', 'code' => 'FR'],
            ['name' => 'Allemagne', 'code' => 'DE'],
            ['name' => 'Italie', 'code' => 'IT'],
            ['name' => 'Autriche', 'code' => 'AT'],
        ];

        foreach ($countries as $country) {
            $country['is_active'] = true;
            \App\Models\Country::create($country);
        }
    }
}
