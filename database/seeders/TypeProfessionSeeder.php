<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TypeProfessionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //<select class="pw_select2 pw_select" name="custom-select-34475011" id="custom-select-34475011" data-placeholder='' data-hash='1mctrp11apag' required="required" data-allowclear='1' data-width='100%'><option></option>	<option value="Stagiaire" >Stagiaire</option>
        //     <option value="Apprenti/e" >Apprenti/e</option>
        //     <option value="Employé/e" >Employé/e</option>
        //     <option value="Responsable de secteur/Chef/fe d’équipe" >Responsable de secteur/Chef/fe d’équipe</option>
        //     <option value="Cadre supérieur" >Cadre supérieur</option>
        // </select>

        $typeProfessions = [
            ['name' => 'Stagiaire', 'slug'=> 'stagiaire'],
            ['name' => 'Apprenti/e', 'slug'=> 'apprenti-e'],
            ['name' => 'Employé/e', 'slug'=> 'employe-e'],
            ['name' => 'Responsable de secteur/Chef/fe d’équipe', 'slug'=> 'responsable-de-secteur-chef-fe-d-equipe'],
            ['name' => 'Cadre supérieur', 'slug'=> 'cadre-superieur'],
        ];

        foreach ($typeProfessions as $typeProfession) {
            $typeProfession['is_active'] = true;
            \App\Models\TypeProfession::create($typeProfession);
        }
    }
}
