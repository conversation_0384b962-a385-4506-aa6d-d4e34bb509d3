<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PackageSeeder extends Seeder
{
    public function run()
    {
        $commonFeatures = [
            "Optimisez vos recherches de candidats",
            "Simplifiez votre processus de recrutement",
            "Identifiez en quelques clics la personne qui correspond à vos besoins",
            "Une solution pour recruter efficacement à moindre coût",
            "Gagnez du temps et de l’énergie dans vos recrutements",
            "Découvrez la simplicité du recrutement",
            "La solution recrutement : rapide, intuitive, ergonomique"
        ];

        $packages = [
            // [
            //     'name' => '24 HEURES',
            //     'price' => 99.00,
            //     'is_popular' => true,
            //     'features' => json_encode(array_merge(
            //         ["Sélection de candidat(e)s illimité(e)s en 24 heures"],
            //         $commonFeatures
            //     ))
            // ],
            [
                'name' => '1 SEMAINE',
                // 'price' => 199.00,
                'price' => 99.00,
                'features' => json_encode(array_merge(
                    ["Sélection de candidat(e)s illimité(e)s en 1 semaine"],
                    $commonFeatures
                ))
            ],
            // [
            //     'name' => '1 MOIS',
            //     'price' => 599.00,
            //     'features' => json_encode(array_merge(
            //         ["Sélection de candidat(e)s illimité(e)s en 1 mois"],
            //         $commonFeatures
            //     ))
            // ]
        ];

        DB::table('packages')->insert($packages);
    }
}