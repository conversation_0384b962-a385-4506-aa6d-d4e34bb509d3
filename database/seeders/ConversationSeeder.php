<?php

namespace Database\Seeders;

use App\Models\Conversation;
use App\Models\Message;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ConversationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $candidate_role = Role::where('slug', 'candidate')->first();
        $recruter_role = Role::where('slug', 'recruter')->first();
        // Récupérer tous les utilisateurs
        $recruterUsers = User::where('role_id', $recruter_role->id)->get();

        // Générer des conversations entre les utilisateurs
        foreach ($recruterUsers as $user) {
            // Sélectionner quelques utilisateurs (candidateUsers) au hasard pour créer des conversations
            $randomUsers = $this->getCandidateUsersRadoms(rand(0, 6));

            foreach ($randomUsers as $otherUser) {
                // Vérifier si une conversation existe déjà entre ces deux utilisateurs
                $conversation = Conversation::where(function ($query) use ($user, $otherUser) {
                    $query->where('user_one_id', $user->id)
                        ->where('user_two_id', $otherUser->id);
                })->orWhere(function ($query) use ($user, $otherUser) {
                    $query->where('user_one_id', $otherUser->id)
                        ->where('user_two_id', $user->id);
                })->first();

                // Créer la conversation si elle n'existe pas encore
                if (!$conversation) {
                    $conversation = Conversation::create([
                        'user_one_id' => $user->id,
                        'user_two_id' => $otherUser->id,
                    ]);
                }

                // Ajouter des messages fictifs dans cette conversation
                for ($i = 0; $i < rand(5, 10); $i++) {
                    Message::create([
                        'conversation_id' => $conversation->id,
                        'sender_id' => rand(0, 1) ? $user->id : $otherUser->id,
                        'content' => 'Message ' . ($i + 1) . ' in conversation between ' . $user->name . ' and ' . $otherUser->name,
                        'is_read' => rand(0, 1) == 1,
                    ]);
                }
            }
        }
    }

    public function getCandidateUsersRadoms($by)
    {
        $candidateUsers = User::where('role_id', Role::where('slug', 'candidate')->first()->id);
        if ($by == 0) {
            $candidateUsers = $candidateUsers->orderBy('created_at', 'asc')->limit(rand(2, 5));
        } else if ($by == 1) {
            $candidateUsers = $candidateUsers->orderBy('created_at', 'desc')->limit(rand(2, 5));
        } else if ($by == 2) {
            $candidateUsers = $candidateUsers->orderBy('name', 'asc')->limit(rand(2, 5));
        } else if ($by == 3) {
            $candidateUsers = $candidateUsers->orderBy('name', 'desc')->limit(rand(2, 5));
        } else if ($by == 4) {
            $candidateUsers = $candidateUsers->orderBy('id', 'asc')->limit(rand(2, 5));
        } else if ($by == 5) {
            $candidateUsers = $candidateUsers->orderBy('id', 'desc')->limit(rand(2, 5));
        } else {
            $candidateUsers = $candidateUsers->limit(rand(2, 5));
        }

        return $candidateUsers->get();
    }
}
