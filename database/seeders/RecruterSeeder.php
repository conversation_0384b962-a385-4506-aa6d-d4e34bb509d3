<?php

namespace Database\Seeders;

use App\Helpers\FileHelper;
use App\Models\Address;
use App\Models\Civility;
use App\Models\Country;
use App\Models\Phone;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class RecruterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $role = Role::where('slug', 'recruter')->first();
        $countryOfResidenceIds = Country::all()->pluck('id')->toArray();

        for ($i = 0; $i < 10; $i++) {
            $faker = \Faker\Factory::create();

            // Création de l'utilisateur
            $user = User::create([ 
                'email' => $faker->unique()->safeEmail,
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'role_id' => $role->id,
                'is_suspend' => false,
            ]);
            
            // Création de la civility
            $civility = Civility::create([
                'user_id' => $user->id,
                'role_id' => $role->id,
                'company_name' => $faker->company,
                'website' => $faker->url,  
            ]);
            
            $phone = new Phone();
            $phone->number = $faker->phoneNumber;
            $phone->user_id = $user->id;
            $phone->save();

            $address = Address::create([
                'user_id' => $user->id,
                'name' => $faker->address,
                'lat' => $faker->latitude,
                'log' => $faker->longitude,
            ]);
        
            // try {
            //     $profilePath = storage_path('app/public/profile_pictures');
            //     if (!is_dir($profilePath)) {
            //         mkdir($profilePath, 0755, true);
            //     }
        
            //     $profile = $faker->image($profilePath, 640, 480, null, false);
            //     $photoFile = FileHelper::store(new \Illuminate\Http\UploadedFile($profilePath . '/' . $profile, $profile), 'profile_picture', $user->id);
            //     $civility->photo_file_id = $photoFile->id;
            //     $civility->save();
            // } catch (\Exception $e) {
            //     // Gérer l'erreur ou ignorer
            //     echo "Erreur lors de la génération de l'image de profil : " . $e->getMessage();
            // }
        }
    }
}
