<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Plan;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $seederName = 'PlanSeeder';

        // Vérifier si le seeder a déjà été exécuté
        if (\App\Helpers\UsedUpFunction::breakSeeders($seederName, $this)) {
            return;
        }


        // Recherche rapide en un jour
        // Optimisez vos recherches de candidats
        // Simplifiez votre processus de recrutement
        // Identifiez en quelques clics la personne qui correspond à vos besoins
        // Une solution pour recruter efficacement à moindre coût
        // Gagnez du temps et de l'énergie dans vos recrutements
        // Découvrez la simplicité du recrutement
        // La solution recrutement : rapide, intuitive, ergonomique

        // Soit 28.5 de l'heure
        // Optimisez vos recherches de candidats
        // Simplifiez votre processus de recrutement
        // Identifiez en quelques clics la personne qui correspond à vos besoins
        // Une solution pour recruter efficacement à moindre coût
        // Gagnez du temps et de l'énergie dans vos recrutements
        // Découvrez la simplicité du recrutement
        // La solution recrutement : rapide, intuitive, ergonomique

        // Soit chf 149.- la semaine
        // Optimisez vos recherches de candidats
        // Simplifiez votre processus de recrutement
        // Identifiez en quelques clics la personne qui correspond à vos besoins
        // Une solution pour recruter efficacement à moindre coût
        // Gagnez du temps et de l'énergie dans vos recrutements
        // Découvrez la simplicité du recrutement
        // La solution recrutement : rapide, intuitive, ergonomique

        $plans = [
            // [
            //     'name' => 'POPULAR',
            //     'slug' => 'popular-plan',
            //     'stripe_product_id' => 'prod_R9MAlsPshMAjy0',
            //     'stripe_price_id' => 'price_1QH3SJFKK6JoGdxmbGrXYiRE',
            //     'price' => 99,
            //     'currency' => 'CHF',
            //     'show_name'=>true,
            //     'description_html'=>json_encode(["Recherche rapide en <b>un jour</b>",
            //     "Optimisez vos recherches de candidats","Simplifiez votre processus de recrutement",
            //     "Identifiez en quelques clics la personne qui correspond à vos besoins",
            //     "Une solution pour recruter efficacement à moindre coût",
            //     "Gagnez du temps et de l'énergie dans vos recrutements",
            //     "Découvrez la simplicité du recrutement","La solution recrutement : rapide, intuitive, ergonomique"]),
            //     'duration_in_days'=>1

            // ],
            [
                'name' => 'BUSINESS',
                'slug' => 'business-plan',
                'stripe_product_id' => 'prod_R9MVPAfEl7if4F',
                // 'stripe_price_id' => 'price_1QH3mAFKK6JoGdxmKRTqNSP3',
                'stripe_price_id' => 'price_1QH3SJFKK6JoGdxmbGrXYiRE',
                // 'price' => 199,
                'price' => 99,
                'currency' => 'CHF',
                'show_name'=>false,
                'description_html'=>json_encode(["Soit <b>28.5</b> de l'heure","Optimisez vos recherches de candidats",
                "Simplifiez votre processus de recrutement","Identifiez en quelques clics la personne qui correspond à vos besoins",
                "Une solution pour recruter efficacement à moindre coût","Gagnez du temps et de l'énergie dans vos recrutements",
                "Découvrez la simplicité du recrutement","La solution recrutement : rapide, intuitive, ergonomique"]),
                'duration_in_days'=>7

            ],
            // [
            //     'name' => 'PRO',
            //     'slug' => 'pro-plan',
            //     'stripe_product_id' => 'prod_R9MWL8Y5DHWeSf',
            //     'stripe_price_id' => 'price_1QH3nsFKK6JoGdxmfXrzyn9Z',
            //     'price' => 599,
            //     'currency' => 'CHF',
            //     'show_name'=>false,
            //     'description_html'=>json_encode(["Soit <b>149.-</b> la semaine","Optimisez vos recherches de candidats",
            //     "Simplifiez votre processus de recrutement","Identifiez en quelques clics la personne qui correspond à vos besoins",
            //     "Une solution pour recruter efficacement à moindre coût","Gagnez du temps et de l'énergie dans vos recrutements",
            //     "Découvrez la simplicité du recrutement","La solution recrutement : rapide, intuitive, ergonomique"]),
            //     'duration_in_days'=>30

            // ],

        ];

        foreach ($plans as $plan) {
            Plan::create($plan);
        }

    }
}
