<?php

namespace Database\Seeders;

use App\Helpers\FileHelper;
use App\Models\Civility;
use App\Models\Country;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $role = Role::where('slug', 'admin')->first();

        // Création de l'utilisateur
        $user = User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'role_id' => $role->id,
        ]);

        // Création de la civility
        $civility = Civility::create([
            'user_id' => $user->id,
            'role_id' => $role->id,
            'first_name' => "Admin",
            'last_name' => "Super",
        ]);
    }
}
