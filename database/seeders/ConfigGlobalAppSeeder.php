<?php

namespace Database\Seeders;

use App\Models\ConfigGlobalApp;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ConfigGlobalAppSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        // ConfigGlobalApp
        $configGlobalApp1 = ConfigGlobalApp::create([
            'value' => now(),
            'name' => 'date_publish',
            'comment' => 'value must be in string , need cast if put/get value TIMESTAMP'
        ]);

        // Configuration pour la date de fin (now + 2 mois)
        $futureDate = now()->addMonths(2);
        $timestamp = $futureDate->timestamp * 1000;
        $mongoDate = new \MongoDB\BSON\UTCDateTime($timestamp);

        $configGlobalApp2 = ConfigGlobalApp::create([
            'value' => $mongoDate,
            'name' => 'day_free_after_publish',
            'comment' => "End date of free trial period"
        ]);

        // Configuration séparée pour le bandeau
        ConfigGlobalApp::create([
            'value' => false,
            'name' => 'display_bandeau',
            'comment' => "Controls banner display"
        ]);
        // default_tax_rates

        $configGlobalApp3 = ConfigGlobalApp::create([
            'value' => 'txr_1QlCZFFKK6JoGdxmT2VBHx0S',
            'name' => 'default_tax_rates',
            'comment' => "value must be in string"
        ]);
        // To modify the percentage of TVA tax, the client need to create another id in dashboard stripe.


        // Configuration pour les clés Stripe (au format JSON)
        $configGlobalApp4 = ConfigGlobalApp::create([
            'value' => json_encode([
                'CASHIER_CURRENCY' => 'chf',
                'STRIPE_KEY' => 'pk_test_51OYqNsFKK6JoGdxmMWzT2SX9IciQGeQSItvJ8TxyoaCpVdrMs5dnpSCy2sSaygudhqbsnkznIavIu2l1hzmYwzaz00zTk4g7hM',
                'STRIPE_SECRET' => 'sk_test_51OYqNsFKK6JoGdxm411OjVQKO6Lv3q2Ta6sg2ry9G5PNZXnEYzpNDE43lVXp0hVHesYlxC3gzF2VYd6Lv4ELB7Sb00smdPudEY',
                'STRIPE_WEBHOOK_SECRET' => 'your-stripe-webhook-secret',
            ]),
            'name' => 'stripe_config',
            'comment' => 'Stripe configuration keys in JSON format'
        ]);

        // Configuration pour les paramètres de messagerie (au format JSON)
        $configGlobalApp5 = ConfigGlobalApp::create([
            'value' => json_encode([
                'MAIL_MAILER' => 'smtp',
                'MAIL_HOST' => 'mail.infomaniak.com',
                'MAIL_PORT' => 465,
                'MAIL_USERNAME' => '<EMAIL>',
                'MAIL_PASSWORD' => 'Cyclone@#placement2025',
                'MAIL_ENCRYPTION' => 'ssl',
                'MAIL_FROM_ADDRESS' => '<EMAIL>',
                'MAIL_FROM_NAME' => 'Cyclone Placement',
            ]),
            'name' => 'mail_config',
            'comment' => 'Mail configuration keys in JSON format'
        ]);

        $configGlobalApp6 = ConfigGlobalApp::create([
            'value' => json_encode([
                'NOCAPTCHA_SECRET' => '6LcVClYqAAAAACs8p05tgkrfVm8m3cXG86Wt755I',
                'NOCAPTCHA_SITEKEY' => '6LcVClYqAAAAAOth0hTq1lafMhQidZSMrw9rSkGR',
            ]),
            'name' => 'captcha_config',
            'comment' => 'Captcha configuration keys in JSON format'
        ]);

        $configGlobalApp7 = ConfigGlobalApp::create([
            'value' => '<EMAIL>',
            'name' => 'admin_mail',
            'comment' => 'Admin email address'
        ]);
    }
}
