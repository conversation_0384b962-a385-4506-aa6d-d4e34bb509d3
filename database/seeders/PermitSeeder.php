<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PermitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permits = [
            ['name' => 'Pas de permis', 'slug' => 'pas-de-permis'],
            ['name' => 'A', 'slug' => 'a'],
            ['name' => 'A1', 'slug' => 'a1'],
            ['name' => 'B', 'slug' => 'b'],
            ['name' => 'B1', 'slug' => 'b1'],
            ['name' => 'BE', 'slug' => 'be'],
            ['name' => 'C', 'slug' => 'c'],
            ['name' => 'C1', 'slug' => 'c1'],
            ['name' => 'C1E', 'slug' => 'c1e'],
            ['name' => 'CE', 'slug' => 'ce'],
            ['name' => 'D', 'slug' => 'd'],
            ['name' => 'D1', 'slug' => 'd1'],
            ['name' => 'D1E', 'slug' => 'd1e'],
            ['name' => 'DE', 'slug' => 'de'],
            ['name' => 'F', 'slug' => 'f'],
            ['name' => 'G', 'slug' => 'g'],
            ['name' => 'M', 'slug' => 'm'],
            ['name' => 'TPP', 'slug' => 'tpp'],
        ];

        foreach ($permits as $permit) {
            $permit['is_active'] = true;
            \App\Models\Permit::create($permit);
        }
    }
}
