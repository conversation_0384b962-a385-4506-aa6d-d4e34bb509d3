<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // (admin, recruter, candidate)
        $roles = [
            ['name' => 'admin', 'slug' => 'admin'],
            ['name' => 'recruter', 'slug' => 'recruter'],
            ['name' => 'candidate', 'slug' => 'candidate'],
        ];

        foreach ($roles as $role) {
            \App\Models\Role::create($role);
        }
    }
}
