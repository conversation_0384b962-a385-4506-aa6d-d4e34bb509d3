<?php

namespace Database\Seeders;

use App\Helpers\FileHelper;
use App\Models\Address;
use App\Models\Civility;
use App\Models\Country;
use App\Models\FieldActivity;
use App\Models\Formation;
use App\Models\Language;
use App\Models\Permit;
use App\Models\Phone;
use App\Models\Profession;
use App\Models\ResidencePermit;
use App\Models\ResponsibilityCandidate;
use App\Models\Role;
use App\Models\TypeProfession;
use App\Models\User;
use App\Models\UserFieldActivity;
use App\Models\UserFormation;
use App\Models\UserPermit;
use App\Models\UserProfession;
use App\Models\UserTypeProfession;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class CandidateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        // $user = new User();
        // $user->name = $request->name;
        // $user->email = $request->email;
        // $user->password = Hash::make($request->password);
        // $role = Role::where('slug', 'candidate')->first();
        // $user->role_id = $role->id;
        // $user->save();

        // $phone = new Phone();
        // $phone->number = $request->phone;
        // $phone->user_id = $user->id;
        // $phone->save();

        // if($request->permits){
        //     foreach ($request->permits as $permitId) {
        //         $userPermit = new UserPermit();
        //         $userPermit->user_id = $user->id;
        //         $userPermit->permit_id = $permitId;
        //         $userPermit->save();
        //     }
        // }

        // foreach ($request->activity_fields as $fieldActivity) {
        //     $userFieldActivity = new UserFieldActivity();
        //     $userFieldActivity->user_id = $user->id;
        //     $userFieldActivity->field_activity_id = $fieldActivity;
        //     $userFieldActivity->save();
        // }

        // foreach ($request->desired_professions as $profession) {
        //     $userProfession = new UserProfession();
        //     $userProfession->user_id = $user->id;
        //     $userProfession->profession_id = $profession;
        //     $userProfession->save();
        // }

        // foreach ($request->formations as $key => $formation) {
        //     $userFormation = new UserFormation();
        //     $userFormation->user_id = $user->id;
        //     $userFormation->formation_id = $formation;
        //     $userFormation->save();
        // }

        // $role = Role::where('slug', 'candidate')->first();

        // $civility = new Civility();
        // $civility->user_id = $user->id;
        // $civility->role_id = $role->id;
        // $civility->first_name = $request->first_name;
        // $civility->last_name = $request->last_name;
        // $civility->date_of_birth = $request->date_of_birth;
        // $civility->category = $request->category;
        // $civility->vehicle = $request->vehicle == 'yes' ? true : false;
        // $civility->residence_permit_id = $request->residence;
        // $civility->criminal_record = $request->criminal_record == 'yes' ? true : false;
        // $civility->country_of_residence_country_id = $request->country_of_residence;
        // $civility->commune = $request->commune;
        // $civility->open_professions = $request->open_professions == 'yes' ? true : false;
        // $civility->type_profession_id = $request->job_types;
        // $civility->contract_type = $request->contract_type;
        // $civility->responsibility_candidate_id = $request->availability;
        // $civility->work_rate = $request->work_rate;
        // $civility->native_language = $request->native_language;
        // $civility->fluent_languages = $request->fluent_languages;
        // $civility->intermediate_languages = $request->intermediate_languages;
        // $civility->basic_languages = $request->basic_languages;
        // $civility->profession_1 = $request->profession_1;
        // $civility->profession_2 = $request->profession_2;
        // $civility->profession_3 = $request->profession_3;
        // $civility->profession_4 = $request->profession_4;
        // $civility->profession_5 = $request->profession_5;
        // $civility->duration_1 = $request->duration_1;
        // $civility->duration_2 = $request->duration_2;
        // $civility->duration_3 = $request->duration_3;
        // $civility->duration_4 = $request->duration_4;
        // $civility->duration_5 = $request->duration_5;
        // $civility->visibility = "1";
        // $civility->save();

        // $profile = $request->file('profile_picture');
        // if ($profile) {
        //     $photoFile = FileHelper::store($profile, 'profile_picture', $user->id);
        //     $civility->photo_file_id = $photoFile->id;
        //     $civility->save();
        // }

        // $cv = $request->file('cv');
        // if ($cv) {
        //     $cvFile = FileHelper::store($cv, 'cv', $user->id);
        //     $civility->cv_file_id = $cvFile->id;
        //     $civility->save();
        // }

        // $workCertificates = $request->file('work_certificates');
        // if ($workCertificates) {
        //     $workCertificatesFile = FileHelper::store($workCertificates, 'work_certificates', $user->id);
        //     $civility->work_certificates_file_id = $workCertificatesFile->id;
        //     $civility->save();
        // }

        // $studyCertificates = $request->file('study_certificates');
        // if ($studyCertificates) {
        //     $studyCertificatesFile = FileHelper::store($studyCertificates, 'study_certificates', $user->id);
        //     $civility->study_certificates_file_id = $studyCertificatesFile->id;
        //     $civility->save();
        // }

        // create 20 candidates with random data white Faker get id of supplementary tables by name

        $fieldActivityIds = FieldActivity::all()->pluck('id')->toArray();
        $professionIds = Profession::all()->pluck('id')->toArray();
        $formationIds = Formation::all()->pluck('id')->toArray();
        $permitIds = Permit::all()->pluck('id')->toArray();
        $residencePermitIds = ResidencePermit::all()->pluck('id')->toArray();
        $typeProfessionIds = TypeProfession::all()->pluck('id')->toArray();
        $responsibilityCandidateIds = ResponsibilityCandidate::all()->pluck('id')->toArray();
        $countryOfResidenceIds = Country::all()->pluck('id')->toArray();
        $languagesIds = Language::all()->pluck('id')->toArray();
        $role = Role::where('slug', 'candidate')->first();

        for ($i = 0; $i < 10; $i++) {
            $faker = \Faker\Factory::create();

            $first_name = $faker->firstName;
            $last_name = $faker->lastName;

            // Création de l'utilisateur
            $user = User::create([
                'name' => $first_name . ' ' . $last_name,
                'email' => $faker->unique()->safeEmail,
                'password' => Hash::make('password'),
                'email_verified_at'=> now(),
                'role_id' => $role->id,
                'is_suspend' => false,
            ]);

            // Ajout du téléphone
            Phone::create([
                'number' => $faker->phoneNumber,
                'user_id' => $user->id,
            ]);

            // Associations multiples
            UserFieldActivity::create([
                'user_id' => $user->id,
                'field_activity_id' => $faker->randomElement($fieldActivityIds),
            ]);

            UserProfession::create([
                'user_id' => $user->id,
                'profession_id' => $faker->randomElement($professionIds),
            ]);

            UserFormation::create([
                'user_id' => $user->id,
                'formation_id' => $faker->randomElement($formationIds),
            ]);

            $permitSelected = $faker->randomElements($permitIds);
            foreach ($permitSelected as $permitId) {
                UserPermit::create([
                    'user_id' => $user->id,
                    'permit_id' => $permitId,
                ]);
            } 
            $typeProfessionSelected = $faker->randomElements($typeProfessionIds);
            foreach ($typeProfessionSelected as $typeProfessionId) {
                UserTypeProfession::create([
                    'user_id' => $user->id,
                    'type_profession_id' => $typeProfessionId,
                ]);
            }

            $address = Address::create([
                'user_id' => $user->id,
                'name' => $faker->address,
                'lat' => $faker->latitude,
                'log' => $faker->longitude,
            ]);
        
            // Création de la civility
            $civility = Civility::create([
                'user_id' => $user->id,
                'role_id' => $role->id,
                'first_name' => $first_name,
                'last_name' => $last_name,
                'date_of_birth' => $faker->date,
                'category' => $faker->randomElement(['current_profiles', 'retired', 'migrants', 'students']),
                'vehicle' => $faker->boolean,
                'residence_permit_id' => $faker->randomElement($residencePermitIds),
                'criminal_record' => $faker->boolean,
                'country_of_residence_country_id' => $faker->randomElement($countryOfResidenceIds),
                'commune' => $faker->city, 
                'open_professions' => $faker->boolean,
                'contract_type' => $faker->randomElement(['cdi', 'cdd', 'call']),
                'responsibility_candidate_id' => $faker->randomElement($responsibilityCandidateIds),
                'work_rate' => $faker->randomElement(['100', '90-100', '80-100', '90', '80-90', '70-90', '80', '70-80', '60-80', '70', '60-70', '50-70', '60', '50-60', '40-60', '50', '40-50', '30-50', '40', '30-40', '20-40', '20-30', '30', '10-30', '20', '10-20', '10']),
                'native_language' => $faker->randomElements($languagesIds),
                'fluent_languages' => $faker->randomElements($languagesIds),
                'intermediate_languages' => $faker->randomElements($languagesIds),
                'basic_languages' => $faker->randomElements($languagesIds),
                'profession_1' => $faker->randomElement(['Profession 1', 'Profession 2', 'Profession 3', 'Profession 4', 'Profession 5']),
                'profession_2' => $faker->randomElement(['Profession 1', 'Profession 2', 'Profession 3', 'Profession 4', 'Profession 5']),
                'profession_3' => $faker->randomElement(['Profession 1', 'Profession 2', 'Profession 3', 'Profession 4', 'Profession 5']),
                'profession_4' => $faker->randomElement(['Profession 1', 'Profession 2', 'Profession 3', 'Profession 4', 'Profession 5']),
                'profession_5' => $faker->randomElement(['Profession 1', 'Profession 2', 'Profession 3', 'Profession 4', 'Profession 5']),
                'duration_1' => $faker->randomElement(['1 semaine', '2 semaine', '3 semaine', 'Entre 1 mois et 6 mois', 'Entre 6 mois et 12 mois', 'Entre 2 ans et 5 ans', 'Entre 6 ans et 10 ans', 'Plus de 11 ans']),
                'duration_2' => $faker->randomElement(['1 semaine', '2 semaine', '3 semaine', 'Entre 1 mois et 6 mois', 'Entre 6 mois et 12 mois', 'Entre 2 ans et 5 ans', 'Entre 6 ans et 10 ans', 'Plus de 11 ans']),
                'duration_3' => $faker->randomElement(['1 semaine', '2 semaine', '3 semaine', 'Entre 1 mois et 6 mois', 'Entre 6 mois et 12 mois', 'Entre 2 ans et 5 ans', 'Entre 6 ans et 10 ans', 'Plus de 11 ans']),
                'duration_4' => $faker->randomElement(['1 semaine', '2 semaine', '3 semaine', 'Entre 1 mois et 6 mois', 'Entre 6 mois et 12 mois', 'Entre 2 ans et 5 ans', 'Entre 6 ans et 10 ans', 'Plus de 11 ans']),
                'duration_5' => $faker->randomElement(['1 semaine', '2 semaine', '3 semaine', 'Entre 1 mois et 6 mois', 'Entre 6 mois et 12 mois', 'Entre 2 ans et 5 ans', 'Entre 6 ans et 10 ans', 'Plus de 11 ans']),
                'visibility' => "1",
            ]);

            // Gestion des fichiers
            // Image de profil
            // try {
            //     $profilePath = storage_path('app/public/profile_pictures');
            //     if (!is_dir($profilePath)) {
            //         mkdir($profilePath, 0755, true);
            //     }

            //     $profile = $faker->image($profilePath, 640, 480, null, false);
            //     $photoFile = FileHelper::store(new \Illuminate\Http\UploadedFile($profilePath . '/' . $profile, $profile), 'profile_picture', $user->id);
            //     $civility->photo_file_id = $photoFile->id;
            //     $civility->save();
            // } catch (\Exception $e) {
            //     // Gérer l'erreur ou ignorer
            //     echo "Erreur lors de la génération de l'image de profil : " . $e->getMessage();
            // }
        }



    }
}
