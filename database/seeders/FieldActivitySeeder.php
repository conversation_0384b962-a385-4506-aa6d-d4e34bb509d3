<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Http\Request;
use App\Http\Controllers\AdminController;
use Illuminate\Support\Str;

class FieldActivitySeeder extends Seeder
{
    private $adminController;

    public function __construct(AdminController $adminController)
    {
        $this->adminController = $adminController;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Liste des domaines d'activité à créer
        $fieldActivities = [
            // Groupe de couleur 1
            'Paysagisme, Agriculture, horticulture, sylviculture, soins aux animaux',
            'Droit, sécurité, police',
            'Social, sciences humaines',

            // Groupe de couleur 2
            'Alimentation',
            'Administration, économie',
            'Informatique, multimédia',
            'Textile, habillement, soins corporels',

            // Groupe de couleur 3
            'Bois, papier, cuir',
            'Electricité, Electronique',
            'Métallurgie, horlogerie',
            'Médias, communication, marketing, industrie graphique',

            // Groupe de couleur 4
            'Arts appliqués, arts, musique',
            'Enseignement',
            'Mécanique, véhicules',
            'Transports, logistique',

            // Groupe de couleur 5
            'Bâtiment, construction',
            'Environnement, nature',
            'Médecine, santé, sport',
            'Intendance, garde d\'enfants',

            // Groupe de couleur 6
            'Biologie, chimie, physique',
            'Hôtellerie, restauration, tourisme',
            'Vente, achat',
        ];

        foreach ($fieldActivities as $activityName) {
            // Create a dummy Request
            $request = new Request([
                'name' => $activityName,
            ]);

            // Call the addActivity method
            $response = $this->adminController->addActivity($request);

            // Check if the activity was created successfully
            if ($response->status() === 200) {
                $responseData = json_decode($response->getContent(), true);
                if ($responseData['success']) {
                    $this->command->info("Field activity '{$activityName}' added successfully.");
                }
            } else {
                $this->command->error("Error adding field activity '{$activityName}'");
            }
        }
    }
}
