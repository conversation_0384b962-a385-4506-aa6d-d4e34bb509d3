<?php
// database/seeders/SeveritySeeder.php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SeveritySeeder extends Seeder
{
    public function run()
    {
        $severities = [
            ['name' => 'Fonctionnalité', 'value' => 10, 'is_default' => false],
            ['name' => 'Simple', 'value' => 20, 'is_default' => false],
            ['name' => 'Texte', 'value' => 30, 'is_default' => false],
            ['name' => 'Cosmétique', 'value' => 40, 'is_default' => false],
            ['name' => 'Mineur', 'value' => 50, 'is_default' => true],
            ['name' => 'Majeur', 'value' => 60, 'is_default' => false],
            ['name' => 'Critique', 'value' => 70, 'is_default' => false],
            ['name' => 'Bloquant', 'value' => 80, 'is_default' => false],
        ];

        DB::table('severities')->insert($severities);
    }
}