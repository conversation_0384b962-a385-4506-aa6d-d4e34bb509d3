<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        // 'user_id','paymentMethodId','is_default_card','brand','lastNumber','fingerprint','address'

        Schema::create('cards', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('user_id');
            $table->string('paymentMethodId')->unique();
            $table->boolean('is_default_card')->default(false);
            $table->string('brand');
            $table->integer('lastNumber');
            $table->string('fingerprint');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cards');
    }
};
