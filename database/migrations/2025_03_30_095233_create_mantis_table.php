<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('mantis', function (Blueprint $table) {
            $table->id();
            $table->string('email'); // Email de la personne qui a soumis l'issue
            $table->string('summary');
            $table->text('description');
            // $table->integer('category_id');
            // $table->integer('priority_id');
            // $table->integer('severity_id');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('mantis');
    }
};