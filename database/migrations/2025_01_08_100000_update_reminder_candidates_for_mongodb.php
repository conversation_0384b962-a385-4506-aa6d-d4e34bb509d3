<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\ReminderCandidate;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Migration pour MongoDB - mise à jour des documents existants avec Eloquent
     */
    public function up(): void
    {
        // Mettre à jour tous les documents existants pour ajouter les nouvelles colonnes
        $reminders = ReminderCandidate::all();

        foreach ($reminders as $reminder) {
            $updated = false;

            // Ajouter reminder_count si manquant
            if (!isset($reminder->reminder_count)) {
                $reminder->reminder_count = 0;
                $updated = true;
            }

            // Ajouter response_status si manquant
            if (!isset($reminder->response_status)) {
                $reminder->response_status = 'no_response';
                $updated = true;
            }

            // Ajouter last_reminder_sent_at si manquant (utiliser created_at)
            if (!isset($reminder->last_reminder_sent_at) && isset($reminder->created_at)) {
                $reminder->last_reminder_sent_at = $reminder->created_at;
                $updated = true;
            }

            if ($updated) {
                $reminder->save();
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Pour MongoDB, nous pouvons laisser les champs ou les supprimer
        // En général, on les laisse pour éviter la perte de données
    }
};
