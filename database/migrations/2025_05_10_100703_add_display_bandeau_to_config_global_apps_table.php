<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('config_global_apps', function (Blueprint $table) {
            $table->boolean('display_bandeau')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('config_global_apps', function (Blueprint $table) {
            $table->dropColumn('display_bandeau');
        });
    }
};
