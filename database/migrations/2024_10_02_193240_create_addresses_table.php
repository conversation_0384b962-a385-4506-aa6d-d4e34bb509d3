<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('addresses', function (Blueprint $table) {
            $table->id();
            // - lat
            // - log
            // - name
            // - place_id
            $table->string('user_id')->nullable();
            $table->double('lat')->nullable();
            $table->double('log')->nullable();
            $table->string('name')->nullable();
            $table->string('place_id')->nullable();

            $table->string('city')->nullable();
            $table->string('country')->nullable();
            $table->string('line1')->nullable()->comment('line 1 for adresse');
            $table->string('state')->nullable();
            $table->string('postal_code')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('addresses');
    }
};
