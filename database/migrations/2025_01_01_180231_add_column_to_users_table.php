<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('role_id')->nullable();
            $table->string('old_id')->nullable();
            $table->string('instance_of')->nullable();
            $table->string('generated_password')->nullable();
            $table->string('registered_at')->nullable();
            $table->string('reset_token')->nullable();
            $table->date('sended_email_update_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('role_id');
            $table->dropColumn('old_id');
            $table->dropColumn('instance_of');
            $table->dropColumn('generated_password');
            $table->dropColumn('registered_at');
            $table->dropColumn('reset_token');
            $table->dropColumn('sended_email_update_at');
        });
    }
};
