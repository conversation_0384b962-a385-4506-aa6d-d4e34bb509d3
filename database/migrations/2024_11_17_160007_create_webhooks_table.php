<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webhooks', function (Blueprint $table) {
            $table->id();  // Clé primaire auto-incrémentée
            $table->string('status');  // Statut du webhook
            $table->string('webhook_id')->unique();  // ID unique du webhook
            $table->string('secret');  // Secret du webhook
            $table->string('url');  // URL du webhook
            $table->timestamps();  // Création et mise à jour des timestamps
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webhooks');
    }
};
