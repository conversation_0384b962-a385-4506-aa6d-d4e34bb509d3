<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('files', function (Blueprint $table) {
            $table->id(); 
            // - id
            // - path
            // - full_path
            // - url
            // - type
            // - name
            // - original_name
            // - extension
            // - usage
            // - user_id
            $table->string('path');
            $table->string('full_path');
            $table->string('url');
            $table->string('type');
            $table->string('name');
            $table->string('original_name');
            $table->string('extension');
            $table->string('usage');
            $table->string('user_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('files');
    }
};
