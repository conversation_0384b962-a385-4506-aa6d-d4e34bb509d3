<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('packages', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // "24 HEURES", "1 SEMAINE", "1 MOIS"
            $table->decimal('price', 10, 2); // 99.00, 199.00, 599.00
            $table->string('currency', 3)->default('CHF');
            $table->boolean('tax_included')->default(false);
            $table->json('features'); // Stocke les fonctionnalités en JSON
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('packages');
    }
};