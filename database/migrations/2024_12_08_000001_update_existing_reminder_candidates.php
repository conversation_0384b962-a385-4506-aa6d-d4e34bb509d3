<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\ReminderCandidate;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Mettre à jour les reminder_candidates existants qui n'ont pas de last_reminder_sent_at
        ReminderCandidate::whereNull('last_reminder_sent_at')
            ->update([
                'last_reminder_sent_at' => \DB::raw('created_at')
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Pas besoin de rollback pour cette migration de données
    }
};
