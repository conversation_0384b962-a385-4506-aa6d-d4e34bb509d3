<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('civilities', function (Blueprint $table) {
            $table->id(); 
            $table->string('user_id');
            $table->string('first_name');
            $table->string('last_name');
            $table->string('sexe');
            $table->string('photo_file_id');
            $table->date('date_of_birth');
            $table->string('category');
            $table->boolean('vehicle');
            $table->string('permit_id');
            $table->string('residence_permit_id');
            $table->string('criminal_record');
            $table->string('country_of_residence_country_id');
            $table->string('commune');
            $table->string('latitude_longitude');
            $table->boolean('open_professions');
            $table->string('type_profession_id');
            $table->string('contract_type');
            $table->string('responsibility_candidate_id');
            $table->string('work_rate');
            $table->string('native_language');
            $table->string('fluent_languages');
            $table->string('intermediate_languages');
            $table->string('basic_languages');
            $table->string('profession_1');
            $table->string('profession_2');
            $table->string('profession_3');
            $table->string('profession_4');
            $table->string('profession_5');
            $table->string('duration_1');
            $table->string('duration_2');
            $table->string('duration_3');
            $table->string('duration_4');
            $table->string('duration_5');
            $table->string('cv_file_id');
            $table->string('work_certificates_file_id');
            $table->string('study_certificates_file_id');  
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('civilities');
    }
};
