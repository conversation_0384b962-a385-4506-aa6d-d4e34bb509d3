<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\ReminderCandidate;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Pour MongoDB, nous mettons à jour les documents existants avec les nouvelles colonnes
     */
    public function up(): void
    {
        // Mettre à jour tous les documents existants pour ajouter les nouvelles colonnes avec des valeurs par défaut
        ReminderCandidate::whereNull('reminder_count')
            ->update(['reminder_count' => 0]);

        ReminderCandidate::whereNull('response_status')
            ->update(['response_status' => 'no_response']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Pour MongoDB, nous pouvons supprimer les champs des documents
        ReminderCandidate::query()->unset(['reminder_count', 'response_status']);
    }
};
