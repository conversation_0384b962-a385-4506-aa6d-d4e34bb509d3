<?php

// database/migrations/[timestamp]_create_severities_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('severities', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->integer('value')->unique();
            $table->boolean('is_default')->default(false);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('severities');
    }
};