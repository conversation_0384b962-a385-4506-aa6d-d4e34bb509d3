<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('user_searches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->json('search_parameters'); // Pour stocker tous les paramètres de recherche
            $table->integer('results_count'); // Nombre de résultats trouvés
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_searches');
    }
};