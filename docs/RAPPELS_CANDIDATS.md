# Gestion des Rappels Candidats - Documentation

## Vue d'ensemble

Cette fonctionnalité permet aux administrateurs de suivre et gérer les rappels envoyés aux candidats, avec la possibilité d'envoyer des emails individuels ou groupés.

## Fonctionnalités implémentées

### 1. Page de suivi des rappels candidats
- **Route** : `/admin/reminder-candidates`
- **Vue** : `resources/views/admin/reminder-candidates/index.blade.php`
- **Contrôleur** : `App\Http\Controllers\ReminderCandidateController`

### 2. Tableau de suivi avec colonnes :
- Nom/Prénom du candidat
- Email
- Statut de réponse (Oui/Non/Pas de réponse)
- Date du dernier rappel
- Nombre de rappels envoyés
- Prochaine date de rappel
- Actions (Envoyer email, Voir détails)

### 3. Système de filtres
- **Par statut de réponse** : <PERSON><PERSON>, <PERSON>, Pa<PERSON> de réponse
- **Par date de dernier rappel** : Sélection de date
- **Recherche** : Par nom, prénom ou email du candidat

### 4. Statistiques affichées
- Nombre total de candidats avec rappels
- Répartition par statut (Oui/Non/Pas de réponse)
- Moyenne des rappels par candidat
- Candidats les plus anciens sans réponse

### 5. Système d'envoi d'emails
- **Emails individuels** : Modal avec sujet et message personnalisables
- **Emails groupés** : Sélection multiple avec envoi en lot
- **Template d'email** : `resources/views/admin/emails/custom-candidate.blade.php`
- **Classe Mail** : `App\Mail\CustomCandidateMail`

### 6. Actions disponibles
- Envoyer un email individuel
- Envoyer des emails groupés
- Mettre à jour le statut de réponse
- Exporter les données en CSV
- Sélection multiple avec checkboxes

## Structure des fichiers

### Modèles
- `app/Models/ReminderCandidate.php` - Modèle principal avec relations et méthodes
- `app/Models/User.php` - Relation ajoutée vers ReminderCandidate

### Contrôleurs
- `app/Http/Controllers/ReminderCandidateController.php` - Logique métier

### Vues
- `resources/views/admin/reminder-candidates/index.blade.php` - Interface principale
- `resources/views/admin/emails/custom-candidate.blade.php` - Template d'email

### JavaScript
- `resources/js/admin/reminder-candidates.js` - Interactivité côté client

### Routes
- Routes ajoutées dans `routes/web.php` sous le groupe admin

### Migrations
- `database/migrations/2025_01_08_000000_add_new_columns_to_reminder_candidates_table.php`

## Utilisation

### Accès à la fonctionnalité
1. Se connecter en tant qu'administrateur
2. Aller dans la sidebar admin
3. Cliquer sur "Rappels Candidats"

### Filtrer les candidats
1. Utiliser les filtres en haut de page :
   - Recherche par nom/email
   - Filtre par statut de réponse
   - Filtre par date de dernier rappel
2. Cliquer sur "Filtrer" ou "Réinitialiser"

### Envoyer un email individuel
1. Cliquer sur "Envoyer email" dans la ligne du candidat
2. Personnaliser le sujet et le message
3. Cliquer sur "Envoyer"

### Envoyer des emails groupés
1. Sélectionner les candidats avec les checkboxes
2. Cliquer sur "Envoyer email groupé"
3. Personnaliser le sujet et le message
4. Cliquer sur "Envoyer à tous"

### Mettre à jour le statut
1. Utiliser le menu déroulant dans la colonne "Actions"
2. Sélectionner : "Oui", "Non" ou "Pas de réponse"
3. Le statut est mis à jour automatiquement

### Exporter les données
1. Appliquer les filtres souhaités
2. Cliquer sur "Exporter CSV"
3. Le fichier sera téléchargé avec les données filtrées

## Configuration

### Variables d'environnement
Assurez-vous que les paramètres SMTP sont configurés dans `.env` :
```
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=your-email
MAIL_FROM_NAME="${APP_NAME}"
```

### Permissions
- Seuls les utilisateurs avec le rôle "admin" peuvent accéder à cette fonctionnalité
- Middleware : `['auth', 'role:admin']`

## API Endpoints

### GET `/admin/reminder-candidates`
Affiche la liste des rappels avec filtres et pagination

### POST `/admin/reminder-candidates/{reminder}/send-email`
Envoie un email individuel à un candidat

### POST `/admin/reminder-candidates/send-bulk-emails`
Envoie des emails groupés aux candidats sélectionnés

### POST `/admin/reminder-candidates/{reminder}/update-status`
Met à jour le statut de réponse d'un candidat

### GET `/admin/reminder-candidates/export`
Exporte les données en format CSV

## Modèle de données

### ReminderCandidate
```php
- user_id: string (référence vers User)
- search_work: boolean (nullable)
- last_reminder_sent_at: datetime
- reminder_count: integer (défaut: 0)
- response_status: string (défaut: 'no_response')
```

### Statuts de réponse
- `yes` : Le candidat a répondu positivement
- `no` : Le candidat a répondu négativement
- `no_response` : Pas de réponse du candidat

## Sécurité

- Protection CSRF sur tous les formulaires
- Validation des données d'entrée
- Autorisation basée sur les rôles
- Sanitisation des emails avant envoi

## Performance

- Pagination des résultats (15 par page)
- Eager loading des relations User et Civility
- Index sur les colonnes de recherche recommandé
- Cache des statistiques possible pour de gros volumes

## Maintenance

### Commande de rappel automatique
La commande existante `app:reminder-candidate` continue de fonctionner et crée automatiquement les entrées ReminderCandidate.

### Nettoyage des données
Considérer l'ajout d'une commande pour archiver les anciens rappels si nécessaire.

## Améliorations futures possibles

1. **Notifications en temps réel** : WebSockets pour les mises à jour
2. **Templates d'emails** : Système de templates personnalisables
3. **Planification d'envois** : Programmer des envois pour plus tard
4. **Rapports avancés** : Graphiques et analyses détaillées
5. **Intégration CRM** : Synchronisation avec des outils externes
6. **Historique des emails** : Traçabilité complète des communications
