# Utiliser l'image ubuntu comme base
FROM ubuntu:latest

# Mettre à jour les paquets et installer les dépendances requises
RUN apt-get update && apt-get install -y \
    ca-certificates \
    apt-transport-https \
    software-properties-common \
    lsb-release

# Ajouter le dépôt pour PHP 8.3 depuis le PPA Ondřej
RUN add-apt-repository ppa:ondrej/php -y

# Mettre à jour les paquets
RUN apt-get update
# Ajouter les sources PHP officielles pour ubuntu

# Installer Apache, PHP 8.3 et les dépendances nécessaires
RUN apt-get install -y \
    nano \
    php8.3 \
    php8.3-cli \
    php8.3-mbstring \
    php8.3-xml \
    php8.3-curl \
    php8.3-zip \
    php8.3-dev \
    php8.3-bcmath \
    apache2 \
    libapache2-mod-php8.3 \
    git \
    unzip \
    curl \
    composer \
    && apt-get clean

# Installer PECL et le pilote MongoDB pour PHP 8.3
RUN apt-get install -y php-pear autoconf automake libtool m4 libcurl4-openssl-dev pkg-config libssl-dev
RUN pecl install mongodb
RUN echo "extension=mongodb.so" > /etc/php/8.3/apache2/conf.d/mongodb.ini && echo "extension=mongodb.so" > /etc/php/8.3/cli/conf.d/mongodb.ini
# Activer le module rewrite d'Apache (nécessaire pour Laravel)
RUN a2enmod rewrite

# Copier les fichiers du projet path actuel dans le conteneur
RUN mkdir -p /var/www/html/FREHY/Cyclone-placement
COPY . /var/www/html/FREHY/Cyclone-placement

RUN rm /etc/apache2/sites-enabled/000-default.conf

# Configuration d'Apache pour pointer vers le dossier public de Laravel
RUN echo '<VirtualHost *:80> \n\
    ServerName localhost\n\
    # Point to the public directory of Laravel\n\
    DocumentRoot /var/www/html/FREHY/Cyclone-placement/public\n\
    <Directory /var/www/html/FREHY/Cyclone-placement/public>\n\
        Options Indexes FollowSymLinks\n\
        AllowOverride All\n\
        Require all granted\n\
    </Directory>\n\
    ErrorLog ${APACHE_LOG_DIR}/error.log\n\
    CustomLog ${APACHE_LOG_DIR}/access.log combined\n\
    # Enable rewrite engine for Laravel\'s routing\n\
    RewriteEngine on\n\
</VirtualHost>\n' > /etc/apache2/sites-enabled/default.conf


# Définir le répertoire de travail
WORKDIR /var/www/html/FREHY/Cyclone-placement

# Installer les dépendances Composer
RUN composer install

# Construire les assets frontend avec npm
RUN curl -sL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs
RUN npm install && npm run build


# Créer le fichier .env avec les variables d'environnement pour l'application Laravel
RUN echo "\
APP_NAME='Cyclone Placement Docker'\n\
APP_ENV=local\n\
APP_KEY=base64:+mC1EpnT/0PUiRVJ6m5qKCOV/QT8ovbGZUrYkbTarG0=\n\
APP_DEBUG=true\n\
APP_URL=http://localhost:80\n\
LOG_CHANNEL=stack\n\
LOG_DEPRECATIONS_CHANNEL=null\n\
LOG_LEVEL=debug\n\
DB_CONNECTION=mongodb\n\
DB_HOST=**********\n\
DB_PORT=27017\n\
DB_DATABASE=cyclone-placement\n\
BROADCAST_DRIVER=log\n\
CACHE_DRIVER=file\n\
FILESYSTEM_DISK=local\n\
QUEUE_CONNECTION=sync\n\
SESSION_DRIVER=file\n\
SESSION_LIFETIME=120\n\
MEMCACHED_HOST=127.0.0.1\n\
REDIS_HOST=127.0.0.1\n\
REDIS_PASSWORD=null\n\
REDIS_PORT=6379\n\
MAIL_MAILER=smtp\n\
MAIL_HOST=mail.webagence-rte.site\n\
MAIL_PORT=465\n\
MAIL_USERNAME=<EMAIL>\n\
MAIL_PASSWORD='3291treha@#Weba'\n\
MAIL_ENCRYPTION=ssl\n\
MAIL_FROM_ADDRESS='<EMAIL>'\n\
MAIL_FROM_NAME='Cyclone Placement'\n\
AWS_ACCESS_KEY_ID=\n\
AWS_SECRET_ACCESS_KEY=\n\
AWS_DEFAULT_REGION=us-east-1\n\
AWS_BUCKET=\n\
AWS_USE_PATH_STYLE_ENDPOINT=false\n\
PUSHER_APP_ID=\n\
PUSHER_APP_KEY=\n\
PUSHER_APP_SECRET=\n\
PUSHER_HOST=\n\
PUSHER_PORT=443\n\
PUSHER_SCHEME=https\n\
PUSHER_APP_CLUSTER=mt1\n\
VITE_PUSHER_APP_KEY='\${PUSHER_APP_KEY}'\n\
VITE_PUSHER_HOST='\${PUSHER_HOST}'\n\
VITE_PUSHER_PORT='\${PUSHER_PORT}'\n\
VITE_PUSHER_SCHEME='\${PUSHER_SCHEME}'\n\
VITE_PUSHER_APP_CLUSTER='\${PUSHER_APP_CLUSTER}'\n\
NOCAPTCHA_SECRET=6Lc3XHMqAAAAAOP1aX081SJnFykc29JMMUyTGt9X\n\
NOCAPTCHA_SITEKEY=6Lc3XHMqAAAAANRPcyqTFal4roL9uRl2VQFdaGqg\n\
ADMIN_MAIL='<EMAIL>'\n\
CASHIER_CURRENCY='chf'\n\
STRIPE_KEY='pk_test_51OYqNsFKK6JoGdxmMWzT2SX9IciQGeQSItvJ8TxyoaCpVdrMs5dnpSCy2sSaygudhqbsnkznIavIu2l1hzmYwzaz00zTk4g7hM'\n\
STRIPE_SECRET='sk_test_51OYqNsFKK6JoGdxm411OjVQKO6Lv3q2Ta6sg2ry9G5PNZXnEYzpNDE43lVXp0hVHesYlxC3gzF2VYd6Lv4ELB7Sb00smdPudEY'\n\
STRIPE_WEBHOOK_SECRET='whsec_eFg0JkzZjPIhNwfSzaESsQFYnKx9MPqm'\n\
" > /var/www/html/FREHY/Cyclone-placement/.env


# Génération de la clé de l'application Laravel
# RUN php artisan key:generate

#Giving autorisation final

# RUN chmod 774 -R ../Cyclone-placement/ && chown -R www-data:www-data ../Cyclone-placement/


RUN chmod -R 775 storage/ && chmod -R 775 bootstrap/cache && chown -R www-data:www-data ../Cyclone-placement/
# sudo chmod -R 775 storage/
# sudo chmod -R 775 bootstrap/cache
# sudo chown -R www-data:www-data ../Cyclone-placement/

RUN php artisan storage:link && apt -y install nano net-tools

# Exposer le port 80
EXPOSE 80

# Démarrer Apache
CMD ["apachectl", "-D", "FOREGROUND"]
