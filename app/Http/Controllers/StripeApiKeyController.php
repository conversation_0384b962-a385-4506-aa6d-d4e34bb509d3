<?php

namespace App\Http\Controllers;

use App\Models\ConfigGlobalApp;
use Illuminate\Http\Request;

class StripeApiKeyController extends Controller
{
    public function index()
    {
        // Récupérer la configuration Stripe
        $stripeConfig = ConfigGlobalApp::where('name', 'stripe_config')->first();

        // Décoder la valeur JSON
        if ($stripeConfig) {
            $stripeConfig->value = json_decode($stripeConfig->value, true);
        }

        // Passer les données à la vue
        return view('admin.stripe-api-key.index', compact('stripeConfig'));
    }

    public function update(Request $request)
    {
        // Valider les données du formulaire
        $request->validate([
            'key' => 'required|string', // La clé à modifier (ex: STRIPE_KEY)
            'value' => 'required|string', // La nouvelle valeur
        ]);

        // Récupérer la configuration Stripe
        $stripeConfig = ConfigGlobalApp::where('name', 'stripe_config')->first();

        if (!$stripeConfig) {
            return redirect()->back()->with('error', 'Configuration Stripe introuvable.');
        }

        // Décoder la valeur JSON
        $configData = json_decode($stripeConfig->value, true);

        // Mettre à jour la valeur spécifique
        $configData[$request->key] = $request->value;

        // Encoder en JSON et sauvegarder
        $stripeConfig->value = json_encode($configData);
        $stripeConfig->save();

        // Rediriger avec un message de succès
        return redirect()->back()->with('success', 'Configuration mise à jour avec succès.');
    }
}
