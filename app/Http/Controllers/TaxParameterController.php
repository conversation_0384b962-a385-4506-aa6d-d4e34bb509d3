<?php

namespace App\Http\Controllers;

use App\Models\ConfigGlobalApp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class TaxParameterController extends Controller
{
    public function index()
    {
        // Récupérer uniquement la configuration "default_tax_rates"
        $taxConfig = ConfigGlobalApp::where('name', 'default_tax_rates')->first();

        // Passer la configuration à la vue
        return view('admin.parametre-taxe.index', compact('taxConfig'));
    }

    public function update(Request $request)
    {
        // Valider les données de la requête
        $validator = Validator::make($request->all(), [
            'value' => 'required|string', // La valeur doit être une chaîne de caractères
        ]);

        // Si la validation échoue, rediriger avec les erreurs
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Récupérer ou créer la configuration "default_tax_rates"
        $taxConfig = ConfigGlobalApp::firstOrCreate(
            ['name' => 'default_tax_rates'],
            ['value' => 'txr_default'] // Valeur par défaut si la configuration n'existe pas
        );

        // Mettre à jour la valeur
        $taxConfig->value = $request->input('value');
        $taxConfig->save();

        // Rediriger avec un message de succès
        return redirect()->route('stripe-webhook.parametre-tax')
            ->with('success', 'Le taux de taxe a été mis à jour avec succès.');
    }
}
