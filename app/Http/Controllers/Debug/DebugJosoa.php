<?php

namespace App\Http\Controllers\Debug;

use App\Helpers\ExcelImportHelper;
use App\Helpers\UsedUpFunction;
use Illuminate\View\View;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Imports\LastUsersImport;
use App\Mail\UpdateUserAccount;
use App\Models\Profession;
use App\Models\Role;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Maatwebsite\Excel\Facades\Excel;

class DebugJosoa extends Controller
{

    public function getAllProfessionByActivitieId(Request $request)
    {
        // Récupération du tableau d'IDs envoyé via la requête
        $field_activity_ids = $request->query('ids', []);

        // Vérifier si on a bien un tableau
        if (!is_array($field_activity_ids)) {
            $field_activity_ids = explode(',', $field_activity_ids);
        }

        return Profession::whereIn("field_activity_id", $field_activity_ids)->get();
    }




    public function index(Request $request)
    {
        ExcelImportHelper::importFromFileUsers();

        // ATTENTION : Cette fonction est à utiliser avec précaution.
        ExcelImportHelper::sendEmail();
    }


    public static function haversineGreatCircleDistance($latitudeFrom = 46.193253, $longitudeFrom = 6.234158, $latitudeTo = 46.2333, $longitudeTo = 6.3167, $earthRadius = 6371)
    {
        // convert from degrees to radians
        $latFrom = deg2rad($latitudeFrom);
        $lonFrom = deg2rad($longitudeFrom);
        $latTo = deg2rad($latitudeTo);
        $lonTo = deg2rad($longitudeTo);

        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;

        $angle = 2 * asin(sqrt(pow(sin($latDelta / 2), 2) + cos($latFrom) * cos($latTo) * pow(sin($lonDelta / 2), 2)));
        return $angle * $earthRadius;
    }
}
