<?php

namespace App\Http\Controllers\Debug;
use Illuminate\View\View;
use Illuminate\Support\Collection;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Card;
use Stripe\StripeClient;

class DebugRino extends Controller
{
    public function index(Request $request): View
    {

        // $stripe = new StripeClient(env('STRIPE_SECRET'));


        // $taxRate = $stripe->taxRates->create([
        //     'display_name' => 'TVA Suisse',
        //     'description' => 'Taxe sur les ventes en Suisse',
        //     'jurisdiction' => 'CH', // Suisse
        //     'percentage' => 8.1, // Taux en pourcentage
        //     'inclusive' => false, // Exprime si c'est une taxe exclusive (hors taxes)
        // ]);

        dd('ici debug',\App\Services\ConfigsService::getMailConfig());

        /*
          id: "txr_1QlCZFFKK6JoGdxmT2VBHx0S"
            object: "tax_rate"
            active: true
            country: null
            created: 1737823845
            description: "Taxe sur les ventes en Suisse"
            display_name: "TVA Suisse"
            effective_percentage: 8.1
            flat_amount: null
            inclusive: false
            jurisdiction: "CH"
            jurisdiction_level: null
            livemode: false
            metadata: Stripe\StripeObject {#523 ▶}
            percentage: 8.1
            rate_type: "percentage"
            state: null
            tax_type: null
            }
        */

        // $otherCards=Card::where('user_id', '67320523d1b8b678bf0dfa5f')->where('paymentMethodId', '!=','pm_1QK0Y7FKK6JoGdxmML4lcZUh')->update(
        //     [
        //         'is_default_card'=>false
        //     ]
        //     );

        $stripe = new StripeClient(env('STRIPE_SECRET'));

        // $card=Card::where('user_id', '67320523d1b8b678bf0dfa5f')->where('paymentMethodId','pm_1QK0Y7FKK6JoGdxmML4lcZUh')->delete();

        $customerId='cus_RDBbWqHsvNREKj';
        $paymentMethods = $stripe->paymentMethods->all([
            'customer' => $customerId,
            'type' => 'card'
        ]);

        dd('ici paymentMethods', $paymentMethods);
        // try {
        //     $payment_method_id = 'pm_1QK0Y7FKK6JoGdxmML4lcZUh'; // Remplacez par l'ID de la méthode de paiement

        //     // Détache la méthode de paiement du client
        //     $detached_payment_method = $stripe->paymentMethods->detach($payment_method_id);

        //     if ($detached_payment_method->id === $payment_method_id) {
        //         dd("La méthode de paiement a été supprimée avec succès.");
        //     } else {
        //         dd("Échec de la suppression de la méthode de paiement.");
        //     }
        // } catch (\Stripe\Exception\ApiErrorException $e) {
        //     // Gestion des erreurs
        //     dd("Erreur : " . $e->getMessage());
        // }


        // // Remplacez par l'ID de l'abonnement que vous souhaitez consulter
        // $subscriptionId = 'sub_1QJxarFKK6JoGdxm9QExd4K1';

        // try {
        //     // Récupérer les factures associées à l'abonnement
        //     $invoices = $stripe->invoices->all([
        //         'subscription' => "sub_1QJxarFKK6JoGdxm9QExd4K1",
        //         'limit' => 10 // Limite le nombre de factures récupérées
        //     ]);

        //     // dd('duplicatedInvoices',$invoices);

        //     $duplicatedInvoices = new Collection();

        //     // Dupliquer la collection des factures 5 fois
        //     for ($i = 0; $i < 5; $i++) {
        //         $duplicatedInvoices = $duplicatedInvoices->concat($invoices);
        //     }

        //     dd('duplicatedInvoices',$duplicatedInvoices);

        // } catch (\Stripe\Exception\ApiErrorException $e) {
        //     dd('Erreur : ' . $e->getMessage());
        // }

    }
}
