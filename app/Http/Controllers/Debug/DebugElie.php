<?php

namespace App\Http\Controllers\Debug;

use App\Helpers\ExcelImportHelper;
use Illuminate\View\View;
use Illuminate\Support\Collection;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Card;
use Stripe\StripeClient;

class DebugElie extends Controller
{


 public function importFromFileUsers()
 {
    ExcelImportHelper::importFromFileUsers();

    // return redirect()->back()->with('success', 'Importation réussie.');
 }

  public function oldUserImport()
  {
    ExcelImportHelper::initialize();

    // return redirect()->back()->with('success', 'Importation réussie.');
  }

  public function sendEmailOldUser()
  {
    // dd('sendEmailOldUser');
    ExcelImportHelper::sendEmail();

    // return redirect()->back()->with('success', 'Email envoyé.');
  }

}
