<?php

namespace App\Http\Controllers;

use App\Helpers\FileHelper;
use App\Models\Address;
use App\Models\Civility;
use App\Models\Country;
use App\Models\FieldActivity;
use App\Models\File;
use App\Models\Formation;
use App\Models\Language;
use App\Models\Permit;
use App\Models\Phone;
use App\Models\Profession;
use App\Models\Region;
use App\Models\ReminderCandidate;
use App\Models\ResidencePermit;
use App\Models\ResponsibilityCandidate;
use App\Models\Role;
use App\Models\TypeProfession;
use App\Models\User;
use App\Models\UserFieldActivity;
use App\Models\UserFormation;
use App\Models\UserPermit;
use App\Models\UserProfession;
use App\Models\UserTypeProfession;
use App\Providers\RouteServiceProvider;
use Illuminate\Validation\Rules;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator as FacadesValidator;
use Illuminate\Support\Facades\DB;

class CandidateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('candidate.index');
    }
    /**
     * Show the form for creating a new resource.
     */
    public function registerIndex()
    {
        $data = [
            'permits' => Permit::all(),
            'residencePermits' => ResidencePermit::all(),
            'countries' => Country::all(),
            'fieldActivities' => FieldActivity::all(),
            'typeProfessions' => TypeProfession::all(),
            'responsibilities' => ResponsibilityCandidate::all(),
            'languages' => Language::all(),
            'formations' => Formation::all(),
            'professions' => Profession::all(),
        ];

        
        return view('candidate.register', $data);
    }

    /**
     * Show step 1 of registration
     */
    public function registerStep1()
    {
        return view('candidate.register-step1');
    }

    /**
     * Show step 2 of registration
     */
    public function registerStep2()
    {
        // Vérifier que l'étape 1 a été complétée
        if (!session('registration_data.email')) {
            return redirect()->route('candidate.registerStep1');
        }
        return view('candidate.register-step2');
    }

    /**
     * Show step 3 of registration
     */
    public function registerStep3()
    {
        // Vérifier que les étapes précédentes ont été complétées
        if (!session('registration_data.first_name')) {
            return redirect()->route('candidate.registerStep2');
        }
        
        $data = [
            'residencePermits' => ResidencePermit::all(),
            'countries' => Country::all(),
        ];
        
        return view('candidate.register-step3', $data);
    }

    /**
     * Show step 4 of registration
     */
    public function registerStep4()
    {
        // Vérifier que les étapes précédentes ont été complétées
        if (!session('registration_data.residence')) {
            return redirect()->route('candidate.registerStep3');
        }
        
        $data = [
            'permits' => Permit::all(),
        ];
        
        return view('candidate.register-step4', $data);
    }

    /**
     * Show step 5 of registration
     */
    public function registerStep5()
    {
        // Vérifier que les étapes précédentes ont été complétées
        if (!session('registration_data.vehicle')) {
            return redirect()->route('candidate.registerStep4');
        }
        
        $data = [
            'fieldActivities' => FieldActivity::all(),
            'professions' => Profession::all(),
            'formations' => Formation::all(),
            'typeProfessions' => TypeProfession::all(),
        ];
        
        return view('candidate.register-step5', $data);
    }

    /**
     * Show step 6 of registration
     */
    public function registerStep6()
    {
        // Vérifier que les étapes précédentes ont été complétées
        if (!session('registration_data.activity_fields')) {
            return redirect()->route('candidate.registerStep5');
        }
        
        $data = [
            'responsibilities' => ResponsibilityCandidate::all(),
        ];
        
        return view('candidate.register-step6', $data);
    }

    /**
     * Show step 7 of registration
     */
    public function registerStep7()
    {
        // Vérifier que les étapes précédentes ont été complétées
        if (!session('registration_data.availability')) {
            return redirect()->route('candidate.registerStep6');
        }
        
        $data = [
            'formations' => Formation::all(),
        ];
        
        return view('candidate.register-step7', $data);
    }

    public function getAllRegionOrCantonByCountryId(Request $request)
    {
        $field_region_id = $request->query('ids');
        return Region::where("country_id", $field_region_id)->get();
    }

    public function getAllProfessionByActivitieId(Request $request)
    {
        // Récupération du tableau d'IDs envoyé via la requête
        $field_activity_ids = $request->query('ids', []);

        // Vérifier si on a bien un tableau
        if (!is_array($field_activity_ids)) {
            $field_activity_ids = explode(',', $field_activity_ids);
        }

        // Récupérer les professions liées ET celles sans domaine d'activité
        return Profession::where(function($query) use ($field_activity_ids) {
            $query->whereIn('field_activity_id', $field_activity_ids)
                  ->orWhereNull('field_activity_id');
        })->get();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function registerStore(Request $request)
    {
        $step = $request->input('step', 1);
        
        // Validation selon l'étape
        $validationRules = $this->getValidationRules($step);
        $validationMessages = $this->getValidationMessages($step);
        
        $validator = FacadesValidator::make($request->all(), $validationRules, $validationMessages);
        
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        
        // Sauvegarder les données en session
        $this->saveStepData($request, $step);
        
        // Si c'est la dernière étape, créer l'utilisateur
        if ($step == 7) {
            return $this->createUser($request);
        }
        
        // Messages de succès pour chaque étape
        $successMessages = [
            1 => 'Informations de compte enregistrées avec succès !',
            2 => 'Informations personnelles enregistrées avec succès !',
            3 => 'Informations légales enregistrées avec succès !',
            4 => 'Informations de transport enregistrées avec succès !',
            5 => 'Informations professionnelles enregistrées avec succès !',
            6 => 'Informations de disponibilité enregistrées avec succès !',
        ];
        
        // Rediriger vers l'étape suivante avec un message de succès
        return redirect()->route("candidate.registerStep" . ($step + 1))
                        ->with('success', $successMessages[$step]);
    }

    /**
     * Get validation rules for specific step
     */
    private function getValidationRules($step)
    {
        $rules = [];
        
        switch ($step) {
            case 1:
                $rules = [
                    'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:' . User::class],
                    'password' => ['required', 'string', 'min:8', 'confirmed', Rules\Password::defaults()],
                    'password_confirmation' => 'required|string|min:8|same:password',
                    'profile_picture' => 'nullable|file|image|mimes:jpeg,png,jpg,gif|max:2048',
                ];
                break;
                
            case 2:
                $rules = [
                    'first_name' => 'required|string|max:255',
                    'last_name' => 'required|string|max:255',
                    'date_of_birth' => 'required|date|before:today',
                    'category' => 'required|string|in:current_profiles,retired,migrants,students',
                    'phone' => 'required|string|max:21',
                ];
                break;
                
            case 3:
                $rules = [
                    'residence' => [
                        'required',
                        'string',
                        function ($attribute, $value, $fail) {
                            if ($value !== 'suisse' && !ResidencePermit::find($value)) {
                                $fail('Le champ ' . $attribute . ' doit être un identifiant de pays valide ou "suisse".');
                            }
                        },
                    ],
                    'name' => 'required|string|max:255',
                    'country_of_residence' => 'required|string|exists:countries,id',
                    'commune' => 'required|string|max:255',
                    'criminal_record' => 'required|string|in:yes,no,skip',
                ];
                break;
                
            case 4:
                $rules = [
                    'vehicle' => 'required|in:yes,no',
                    'permits' => 'nullable|array',
                    'permits.*' => 'string|exists:permits,id',
                ];
                break;
                
            case 5:
                $rules = [
                    'activity_fields' => 'required|array',
                    'activity_fields.*' => 'required|string|exists:field_activities,id',
                    'professions_list' => 'required|array',
                    'professions_list.*' => 'required|string|exists:professions,id',
                    'formations' => 'required|array',
                    'formations.*' => 'required|string',
                    'job_types' => 'required|array',
                    'job_types.*' => 'required|string|exists:type_professions,id',
                    'contract_type' => 'required|array',
                    'contract_type.*' => 'required|string|in:call,cdi,cdd',
                ];
                break;
                
            case 6:
                $rules = [
                    'availability' => 'required|string|exists:responsibility_candidates,id',
                    'work_rate' => 'required|string',
                    'native_language' => 'nullable|array',
                    'native_language.*' => 'nullable|string',
                    'fluent_languages' => 'nullable|array',
                    'fluent_languages.*' => 'nullable|string',
                    'intermediate_languages' => 'nullable|array',
                    'intermediate_languages.*' => 'nullable|string',
                    'basic_languages' => 'nullable|array',
                    'basic_languages.*' => 'nullable|string',
                ];
                break;
                
            case 7:
                $rules = [
                    'formations_final' => 'required|array',
                    'formations_final.*' => 'required|string',
                    'cv' => 'nullable|file|mimes:pdf,doc,docx|max:2048',
                    'certificates' => 'nullable|array',
                    'certificates.*' => 'file|mimes:pdf,doc,docx|max:2048',
                    'diplomas' => 'nullable|array',
                    'diplomas.*' => 'file|mimes:pdf,doc,docx|max:2048',
                    'g-recaptcha-response' => ['required', 'captcha'],
                    'terms' => 'required|accepted',
                ];
                break;
        }
        
        return $rules;
    }

    /**
     * Get validation messages for specific step
     */
    private function getValidationMessages($step)
    {
        $messages = [
            'email.required' => 'L\'adresse e-mail est obligatoire.',
            'email.email' => 'Le format de l\'adresse e-mail est invalide.',
            'email.unique' => 'Cette adresse e-mail est déjà utilisée.',
            'password.required' => 'Le mot de passe est obligatoire.',
            'password.min' => 'Le mot de passe doit contenir au moins 8 caractères.',
            'password.confirmed' => 'Les mots de passe ne correspondent pas.',
            'password_confirmation.required' => 'La confirmation du mot de passe est obligatoire.',
            'password_confirmation.same' => 'La confirmation du mot de passe doit correspondre.',
            'first_name.required' => 'Le prénom est obligatoire.',
            'first_name.max' => 'Le prénom ne doit pas dépasser 255 caractères.',
            'last_name.required' => 'Le nom de famille est obligatoire.',
            'last_name.max' => 'Le nom de famille ne doit pas dépasser 255 caractères.',
            'date_of_birth.required' => 'La date de naissance est obligatoire.',
            'date_of_birth.before' => 'La date de naissance doit être antérieure à aujourd\'hui.',
            'category.required' => 'La catégorie est obligatoire.',
            'category.in' => 'La catégorie sélectionnée est invalide.',
            'phone.required' => 'Le numéro de téléphone est obligatoire.',
            'phone.max' => 'Le numéro de téléphone ne doit pas dépasser 21 caractères.',
            'vehicle.required' => 'Le statut de titulaire de véhicule est obligatoire.',
            'vehicle.in' => 'La valeur du statut de véhicule est invalide.',
            // 'permits.*.required' => 'Le permis de conduire est obligatoire.',
            'permits.*.exists' => 'Le permis sélectionné est invalide.',
            'residence.required' => 'Le permis de séjour est obligatoire.',
            'residence.exists' => 'Le permis de séjour sélectionné est invalide.',
            'criminal_record.in' => 'La valeur du casier judiciaire est invalide.',
            'country_of_residence.required' => 'Le pays de résidence est obligatoire.',
            'country_of_residence.exists' => 'Le pays sélectionné est invalide.',
            'commune.required' => 'La commune est obligatoire.',
            'commune.max' => 'La commune ne doit pas dépasser 255 caractères.',
            'address.required' => 'L\'adresse est obligatoire.',
            'address.max' => 'L\'adresse ne doit pas dépasser 255 caractères.',
            'latitude_longitude.required' => 'Veuillez sélectionner une adresse sélectionnée.',
            'latitude_longitude.regex' => 'Le format des coordonnées géographiques est invalide.',
            'activity_fields.required' => 'Le champ Domaine(s) d\'activité est obligatoire.',
            'activity_fields.*.exists' => 'Domaine(s) d\'activité  sélectionnée est invalide.',
            'professions_list.required' => 'Selectionnez d\'abord le domaine d\'activités puis choisissez la profession recherchée correspondant.',
            'professions_list.*.exists' => 'La profession recherchée est invalide.',
            'open_professions.required' => 'Le choix d\'ouverture à toutes les professions est obligatoire.',
            'job_types.required' => 'Le type de poste est obligatoire.',
            'job_types.*.exists' => 'Le type de poste sélectionné est invalide.',
            'contract_type.required' => 'Le type de contrat est obligatoire.',
            'contract_type.*.in' => 'Le type de contrat sélectionné est invalide.',
            'availability.required' => 'La disponibilité est obligatoire.',
            'work_rate.required' => 'Le taux de travail est obligatoire.',
            'formations.required' => 'La formation est obligatoire.',
            'formations.*.exists' => 'La formation sélectionnée est invalide.',
            'profession_1.max' => 'La profession 1 ne doit pas dépasser 255 caractères.',
            'cv.mimes' => 'Le CV doit être au format pdf, doc ou docx.',
            'cv.max' => 'Le CV ne doit pas dépasser 2048 Ko.',
            'terms.required' => 'Vous devez accepter les conditions d\'utilisation.',
            'terms.accepted' => 'Vous devez accepter les conditions d\'utilisation.',
        ];
        
        return $messages;
    }

    /**
     * Save step data to session
     */
    private function saveStepData(Request $request, $step)
    {
        $data = $request->except(['_token', 'step']);
        
        // Gérer les fichiers uploadés
        if ($step == 1 && $request->hasFile('profile_picture')) {
            $data['profile_picture'] = $request->file('profile_picture')->store('profile_pictures', 'public');
        }
        
        if ($step == 7) {
            if ($request->hasFile('cv')) {
                $data['cv'] = $request->file('cv')->store('cvs', 'public');
            }
            
            if ($request->hasFile('certificates')) {
                $certificates = [];
                foreach ($request->file('certificates') as $file) {
                    $certificates[] = $file->store('certificates', 'public');
                }
                $data['certificates'] = $certificates;
            }
            
            if ($request->hasFile('diplomas')) {
                $diplomas = [];
                foreach ($request->file('diplomas') as $file) {
                    $diplomas[] = $file->store('diplomas', 'public');
                }
                $data['diplomas'] = $diplomas;
            }
        }
        
        // Fusionner avec les données existantes
        $existingData = session('registration_data', []);
        
        // Gérer les tableaux correctement
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                // Pour les tableaux, fusionner avec les valeurs existantes
                $existingArray = $existingData[$key] ?? [];
                $existingData[$key] = array_merge($existingArray, $value);
            } else {
                $existingData[$key] = $value;
            }
        }
        
        session(['registration_data' => $existingData]);
    }

    /**
     * Create user from all collected data
     */
    private function createUser(Request $request)
    {
        $data = session('registration_data');
        
        if (!$data) {
            return redirect()->route('candidate.registerStep1')->withErrors(['error' => 'Données d\'inscription manquantes']);
        }
        
        try {
            DB::beginTransaction();
            
            // Créer l'utilisateur
            $user = User::create([
                'email' => $data['email'],
                'password' => Hash::make($data['password']),
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'date_of_birth' => $data['date_of_birth'],
                'category' => $data['category'],
                'phone' => $data['phone'],
                'profile_picture' => $data['profile_picture'] ?? null,
                'residence' => $data['residence'],
                'name' => $data['name'],
                'country_of_residence' => $data['country_of_residence'],
                'commune' => $data['commune'],
                'criminal_record' => $data['criminal_record'],
                'vehicle' => $data['vehicle'],
                'availability' => $data['availability'],
                'work_rate' => $data['work_rate'],
                'cv' => $data['cv'] ?? null,
                'role_id' => 2, // Rôle candidat
            ]);
            
            // Attacher les permis
            if (isset($data['permits']) && is_array($data['permits'])) {
                $user->permits()->attach($data['permits']);
            }
            
            // Attacher les domaines d'activité
            if (isset($data['activity_fields']) && is_array($data['activity_fields'])) {
                $user->fieldActivities()->attach($data['activity_fields']);
            }
            
            // Attacher les professions
            if (isset($data['professions_list']) && is_array($data['professions_list'])) {
                $user->professions()->attach($data['professions_list']);
            }
            
            // Attacher les formations
            if (isset($data['formations']) && is_array($data['formations'])) {
                $user->formations()->attach($data['formations']);
            }
            
            // Attacher les types de postes
            if (isset($data['job_types']) && is_array($data['job_types'])) {
                $user->typeProfessions()->attach($data['job_types']);
            }
            
            // Sauvegarder les langues
            $this->saveLanguages($user, $data);
            
            // Sauvegarder les certificats
            $this->saveCertificates($user, $data);
            
            DB::commit();
            
            // Nettoyer la session
            session()->forget('registration_data');
            
            // Connecter l'utilisateur
            Auth::login($user);
            
            return redirect()->route('candidate.dashboard')->with('success', 'Inscription réussie !');
            
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Erreur lors de l\'inscription: ' . $e->getMessage()]);
        }
    }

    /**
     * Save user languages
     */
    private function saveLanguages($user, $data)
    {
        $languages = [];
        
        if (isset($data['native_language'])) {
            foreach ($data['native_language'] as $lang) {
                $languages[] = ['language' => $lang, 'level' => 'native'];
            }
        }
        
        if (isset($data['fluent_languages'])) {
            foreach ($data['fluent_languages'] as $lang) {
                $languages[] = ['language' => $lang, 'level' => 'fluent'];
            }
        }
        
        if (isset($data['intermediate_languages'])) {
            foreach ($data['intermediate_languages'] as $lang) {
                $languages[] = ['language' => $lang, 'level' => 'intermediate'];
            }
        }
        
        if (isset($data['basic_languages'])) {
            foreach ($data['basic_languages'] as $lang) {
                $languages[] = ['language' => $lang, 'level' => 'basic'];
            }
        }
        
        foreach ($languages as $language) {
            $user->languages()->create($language);
        }
    }

    /**
     * Save user certificates
     */
    private function saveCertificates($user, $data)
    {
        if (isset($data['certificates'])) {
            foreach ($data['certificates'] as $certificate) {
                $user->files()->create([
                    'file_path' => $certificate,
                    'file_type' => 'certificate'
                ]);
            }
        }
        
        if (isset($data['diplomas'])) {
            foreach ($data['diplomas'] as $diploma) {
                $user->files()->create([
                    'file_path' => $diploma,
                    'file_type' => 'diploma'
                ]);
            }
        }
    }

    /**
     * Display the candidate dashboard.
     */
    public function dashboard()
    {
        // dd("Hello");
        $permits = Permit::getAllActive();
        $residencePermits = ResidencePermit::getAllActive();
        $countries = Country::getAllActive();
        $fieldActivities = FieldActivity::getAllActive();
        $typeProfessions = TypeProfession::getAllActive();
        $professions = Profession::getAllActive();
        $responsibilities = ResponsibilityCandidate::getAllActive();
        $languages = Language::getAllActive();
        $formations = Formation::getAllActive();

        $user = Auth::user();

        // Manually retrieve related data
        $civility = Civility::where('user_id', $user->id)->first();
        $phones = Phone::where('user_id', $user->id)->get();
        $userFieldActivities = UserFieldActivity::where('user_id', $user->id)->pluck('field_activity_id')->toArray();
        $userProfessions = UserProfession::where('user_id', $user->id)->pluck('profession_id')->toArray();
        $userTypeProfessions = UserTypeProfession::where('user_id', $user->id)->pluck('type_profession_id')->toArray();
        $userFormations = UserFormation::where('user_id', $user->id)->pluck('formation_id')->toArray();
        $userPermits = UserPermit::where('user_id', $user->id)->pluck('permit_id')->toArray();

        $civility->photo = File::where('id', $civility->photo_file_id)?->first();
        $civility->cv = File::where('id', $civility->cv_file_id)?->first();
        $civility->work_certificates = File::where('id', $civility->work_certificates_file_id)?->first();
        $civility->study_certificates = File::where('id', $civility->study_certificates_file_id)?->first();
        // Ajout : charger tous les fichiers de certificat
        $civility->certificate_files = File::where('user_id', $user->id)
            ->where('usage', 'certificate')
            ->get();

        // Fetch language preferences
        $nativeLanguages = $civility?->native_language ?? [];
        $fluentLanguages = $civility?->fluent_languages ?? [];
        $intermediateLanguages = $civility?->intermediate_languages ?? [];
        $basicLanguages = $civility?->basic_languages ?? [];

        // dd($userPermits);

        return view('candidate.dashboard', [
            'permits' => $permits,
            'residencePermits' => $residencePermits,
            'countries' => $countries,
            'fieldActivities' => $fieldActivities,
            'typeProfessions' => $typeProfessions,
            'professions' => $professions,
            'responsibilities' => $responsibilities,
            'languages' => $languages,
            'formations' => $formations,
            'user' => $user,
            'civility' => $civility,
            'phones' => $phones,
            'userFieldActivities' => $userFieldActivities,
            'userProfessions' => $userProfessions,
            'userTypeProfessions' => $userTypeProfessions,
            'userFormations' => $userFormations,
            'userPermits' => $userPermits,
            'nativeLanguages' => $nativeLanguages,
            'fluentLanguages' => $fluentLanguages,
            'intermediateLanguages' => $intermediateLanguages,
            'basicLanguages' => $basicLanguages,
        ]);
    }

    /**
     * Update the candidate's profile.
     */
    public function profileUpdate(Request $request)
    {
        $user = Auth::user();

        $validation = FacadesValidator::make($request->all(), [
            // 'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:users,email,' . $user->id],
            // 'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            // 'g-recaptcha-response' => ['required', 'captcha'],
            'profile_picture' => 'nullable',
            'visibility' => 'required|in:0,1',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'date_of_birth' => 'required|date|before:today',
            'category' => 'required|string|in:current_profiles,retired,migrants,students',
            'phone' => 'required|string|max:21',
            'vehicle' => 'required|in:yes,no',
            'permits' => 'nullable|array',
            'permits.*' => 'required|string|exists:permits,id',
            // 'residence' => 'required|string|exists:residence_permits,id',
            'residence' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    // Vérifie si la valeur n'est pas 'suisse' et n'existe pas dans la table countries
                    if ($value !== 'suisse' && !ResidencePermit::find($value)) {
                        $fail('Le champ ' . $attribute . ' doit être un identifiant de pays valide ou "suisse".');
                    }
                },
            ],
            'criminal_record' => 'nullable|string|in:yes,no,skip',
            'country_of_residence' => 'required|string|exists:countries,id',
            'commune' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            // latitude_longitude lat,lng
            'latitude_longitude' => 'required|string|regex:/^(\-?\d+(\.\d+)?),\s*(\-?\d+(\.\d+)?)$/',
            'activity_fields' => 'required|array',
            'activity_fields.*' => 'required|string|exists:field_activities,id',
            'desired_professions' => 'required|array',
            'desired_professions.*' => 'required|string|exists:professions,id',
            'open_professions' => 'required|in:yes,no',
            'job_types' => 'required|array',
            'job_types.*' => 'required|string|exists:type_professions,id',
            'contract_type' => 'required|array',
            'contract_type.*' => 'required|string|in:call,cdi,cdd',
            'availability' => 'required|string|exists:responsibility_candidates,id',
            'work_rate' => 'required|string',
            'native_language' => 'nullable|array',
            'native_language.*' => 'nullable|string|exists:languages,id',
            'fluent_languages' => 'nullable|array',
            'fluent_languages.*' => 'nullable|string|exists:languages,id',
            'intermediate_languages' => 'nullable|array',
            'intermediate_languages.*' => 'nullable|string|exists:languages,id',
            'basic_languages' => 'nullable|array',
            'basic_languages.*' => 'nullable|string|exists:languages,id',
            'formations' => 'required|array',
            'formations.*' => 'required|string|exists:formations,id',
            'profession_1' => 'nullable|string|max:255',
            'profession_2' => 'nullable|string|max:255',
            'profession_3' => 'nullable|string|max:255',
            'profession_4' => 'nullable|string|max:255',
            'profession_5' => 'nullable|string|max:255',
            'duration_1' => 'nullable|string',
            'duration_2' => 'nullable|string',
            'duration_3' => 'nullable|string',
            'duration_4' => 'nullable|string',
            'duration_5' => 'nullable|string',
            'cv' => 'nullable|file|mimes:pdf,doc,docx|max:2048',
            'work_certificates' => 'nullable|file|mimes:pdf,doc,docx|max:2048',
            'study_certificates' => 'nullable|file|mimes:pdf,doc,docx|max:2048',
        ], [
            'g-recaptcha-response.required' => 'Le captcha est obligatoire.',
            'g-recaptcha-response.captcha' => 'Le captcha est invalide.',
            'name.required' => 'Le nom est obligatoire.',
            'name.max' => 'Le nom ne doit pas dépasser 255 caractères.',
            'g-recaptcha-response.required' => 'Le captcha est obligatoire.',
            'g-recaptcha-response.captcha' => 'Le captcha est invalide.',
            'email.required' => 'L\'adresse e-mail est obligatoire.',
            'email.email' => 'Le format de l\'adresse e-mail est invalide.',
            'email.unique' => 'Cette adresse e-mail est déjà utilisée.',
            'password.required' => 'Le mot de passe est obligatoire.',
            'password.min' => 'Le mot de passe doit contenir au moins 8 caractères.',
            'password.confirmed' => 'Les mots de passe ne correspondent pas.',
            'password_confirmation.required' => 'La confirmation du mot de passe est obligatoire.',
            'password_confirmation.same' => 'La confirmation du mot de passe doit correspondre.',
            // 'profile_picture.image' => 'La photo de profil doit être une image.',
            // 'profile_picture.mimes' => 'La photo de profil doit être au format jpeg, png, jpg, ou gif.',
            'first_name.required' => 'Le prénom est obligatoire.',
            'last_name.required' => 'Le nom de famille est obligatoire.',
            'date_of_birth.required' => 'La date de naissance est obligatoire.',
            'date_of_birth.before' => 'La date de naissance doit être antérieure à aujourd\'hui.',
            'category.required' => 'La catégorie est obligatoire.',
            'category.in' => 'La catégorie sélectionnée est invalide.',
            'phone.required' => 'Le numéro de téléphone est obligatoire.',
            'vehicle.required' => 'Le statut de titulaire de véhicule est obligatoire.',
            'permits.*.required' => 'Le permis de conduire est obligatoire.',
            'permits.*.exists' => 'Le permis sélectionné est invalide.',
            'residence.required' => 'Le permis de séjour est obligatoire.',
            'residence.exists' => 'Le permis de séjour sélectionné est invalide.',
            'criminal_record.in' => 'La valeur du casier judiciaire est invalide.',
            'country_of_residence.required' => 'Le pays de résidence est obligatoire.',
            'country_of_residence.exists' => 'Le pays sélectionné est invalide.',
            'activity_fields.required' => 'Le champ d\'activité est obligatoire.',
            'activity_fields.*.exists' => 'L\'activité sélectionnée est invalide.',
            'desired_professions.required' => 'La profession recherchée est obligatoire.',
            'desired_professions.*.exists' => 'La profession recherchée est invalide.',
            'open_professions.required' => 'Le choix d\'ouverture à toutes les professions est obligatoire.',
            'job_types.required' => 'Le type de poste est obligatoire.',
            'job_types.*.exists' => 'Le type de poste sélectionné est invalide.',
            'contract_type.required' => 'Le type de contrat est obligatoire.',
            'availability.required' => 'La disponibilité est obligatoire.',
            'work_rate.required' => 'Le taux de travail est obligatoire.',
            'formations.required' => 'La formation est obligatoire.',
            'profession_1.max' => 'La profession 1 ne doit pas dépasser 255 caractères.',
            'cv.mimes' => 'Le CV doit être au format pdf, doc ou docx.',
            'terms.required' => 'Vous devez accepter les conditions d\'utilisation.',
            'commune.required' => 'La commune est obligatoire.',
            'address.required' => 'L\'adresse est obligatoire.',
            'latitude_longitude.required' => 'Tu dois sélectionner un adresse proposée.',
        ]);



        if ($validation->fails()) {
            // dd('error',$validation->errors(), $request->all());

            return redirect()->back()->withErrors($validation)->withInput();
        }

        // Update user information
        // $user->email = $request->email;
        // $user->save();

        // Update phone
        $phone = Phone::where('user_id', $user->id)->first();
        if ($phone) {
            $phone->number = $request->phone;
            $phone->save();
        } else {
            $phone = new Phone();
            $phone->number = $request->phone;
            $phone->user_id = $user->id;
            $phone->save();
        }

        // Update field permits
        UserPermit::where('user_id', $user->id)->delete();
        if($request->permits){
            foreach ($request->permits as $permitId) {
                UserPermit::create([
                    'user_id' => $user->id,
                    'permit_id' => $permitId,
                ]);
            }
        }

        // Update field activities
        UserFieldActivity::where('user_id', $user->id)->delete();
        foreach ($request->activity_fields as $fieldActivityId) {
            UserFieldActivity::create([
                'user_id' => $user->id,
                'field_activity_id' => $fieldActivityId,
            ]);
        }

        // Update desired professions
        UserProfession::where('user_id', $user->id)->delete();
        foreach ($request->desired_professions as $professionId) {
            UserProfession::create([
                'user_id' => $user->id,
                'profession_id' => $professionId,
            ]);
        }

        // Update formations
        UserFormation::where('user_id', $user->id)->delete();
        foreach ($request->formations as $formationId) {
            UserFormation::create([
                'user_id' => $user->id,
                'formation_id' => $formationId,
            ]);
        }

        // Update type professions
        UserTypeProfession::where('user_id', $user->id)->delete();
        foreach ($request->job_types as $type_profession) {
            $userProfession = new UserTypeProfession();
            $userProfession->user_id = $user->id;
            $userProfession->type_profession_id = $type_profession;
            $userProfession->save();
        }

        // Update address
        $address = Address::where('user_id', $user->id)->first();
        if (!$address) {
            $address = new Address();
            $address->user_id = $user->id;
        }

        $latitudeLongitude = explode(',', $request->latitude_longitude);
        $latitude = $latitudeLongitude[0] ?? 0;
        $longitude = $latitudeLongitude[1] ?? 0;

        $address->name = $request->address;
        $address->lat = $latitude;
        $address->log = $longitude;
        $address->save();

        // Update civility
        $civility = Civility::where('user_id', $user->id)->first();
        if (!$civility) {
            $civility = new Civility();
            $civility->user_id = $user->id;
        }
        $civility->first_name = $request->first_name;
        $civility->last_name = $request->last_name;
        $civility->date_of_birth = $request->date_of_birth;
        $civility->category = $request->category;
        $civility->vehicle = $request->vehicle == 'yes' ? true : false;
        $civility->residence_permit_id = $request->residence;
        $civility->criminal_record = $request->criminal_record;
        $civility->country_of_residence_country_id = $request->country_of_residence;
        $civility->commune = $request->commune;
        $civility->open_professions = $request->open_professions == 'yes' ? true : false;
        $civility->contract_type = $request->contract_type;
        $civility->responsibility_candidate_id = $request->availability;
        $civility->work_rate = $request->work_rate;
        $civility->native_language = $request->native_language;
        $civility->fluent_languages = $request->fluent_languages;
        $civility->intermediate_languages = $request->intermediate_languages;
        $civility->basic_languages = $request->basic_languages;
        $civility->profession_1 = $request->profession_1;
        $civility->profession_2 = $request->profession_2;
        $civility->profession_3 = $request->profession_3;
        $civility->profession_4 = $request->profession_4;
        $civility->profession_5 = $request->profession_5;
        $civility->duration_1 = $request->duration_1;
        $civility->duration_2 = $request->duration_2;
        $civility->duration_3 = $request->duration_3;
        $civility->duration_4 = $request->duration_4;
        $civility->duration_5 = $request->duration_5;
        $civility->visibility = $request->visibility;
        $civility->save();

        // Handle profile picture
        if ($request->hasFile('profile_picture')) {
            // remove old profile picture
            if ($civility->photo_file_id) {
                $oldPhoto = File::where('id', $civility->photo_file_id)->first();
                if ($oldPhoto) {
                    // remove old file
                    try {
                        unlink( $oldPhoto->full_path);
                    } catch (\Throwable $th) {
                    }
                    $oldPhoto->delete();
                }
            }
            $profile = $request->file('profile_picture');
            $photoFile = FileHelper::store($profile, 'profile_picture', $user->id);
            $civility->photo_file_id = $photoFile->id;
            $civility->save();
        }

        // Handle CV
        if ($request->hasFile('cv')) {
            // remove old cv
            if ($civility->cv_file_id) {
                $oldCv = File::where('id', $civility->cv_file_id)->first();
                if ($oldCv) {
                    // remove old file
                    try {
                        unlink( $oldCv->full_path);
                    } catch (\Throwable $th) {
                    }
                    $oldCv->delete();
                }
            }
            $cv = $request->file('cv');
            $cvFile = FileHelper::store($cv, 'cv', $user->id);
            $civility->cv_file_id = $cvFile->id;
            $civility->save();
        }

        // Handle work certificates
        if ($request->hasFile('work_certificates')) {
            // remove old work certificates
            if ($civility->work_certificates_file_id) {
                $oldWorkCertificates = File::where('id', $civility->work_certificates_file_id)->first();
                if ($oldWorkCertificates) {
                    // remove old file
                    try {
                        unlink( $oldWorkCertificates->full_path);
                    } catch (\Throwable $th) {
                    }
                    $oldWorkCertificates->delete();
                }
            }
            $workCertificates = $request->file('work_certificates');
            $workCertificatesFile = FileHelper::store($workCertificates, 'work_certificates', $user->id);
            $civility->work_certificates_file_id = $workCertificatesFile->id;
            $civility->save();
        }

        // Handle study certificates
        if ($request->hasFile('study_certificates')) {
            if ($civility->study_certificates_file_id) {
                $oldStudyCertificates = File::where('id', $civility->study_certificates_file_id)->first();
                if ($oldStudyCertificates) {
                    // remove old file
                    try {
                        unlink( $oldStudyCertificates->full_path);
                    } catch (\Throwable $th) {
                    }
                    $oldStudyCertificates->delete();
                }
            }
            $studyCertificates = $request->file('study_certificates');
            $studyCertificatesFile = FileHelper::store($studyCertificates, 'study_certificates', $user->id);
            $civility->study_certificates_file_id = $studyCertificatesFile->id;
            $civility->save();
        }

        // Gérer l'upload de nouveaux certificats (multi-upload)
        if ($request->hasFile('certificates')) {
            // On n'efface plus les anciens certificats, on ajoute simplement les nouveaux
            foreach ($request->file('certificates') as $certificateFile) {
                \App\Helpers\FileHelper::store($certificateFile, 'certificate', $user->id);
            }
        }

        return redirect()->route('candidate.dashboard')->with('status', 'Profil mis à jour avec succès.');
    }

    /**
     * Delete the candidate's profile.
     */
    public function profileDeleteIndex()
    {
        $user = Auth::user();

        return view('candidate.delete-profile', [
            'user' => $user,
        ]);
    }

    /**
     * Delete the candidate's profile.
     */
    public function profileDeletePost(Request $request)
    {
        $user = Auth::user();

        $validation = FacadesValidator::make($request->all(), [
            'password' => 'required|string',
        ], [
            'password.required' => 'Le mot de passe est obligatoire.',
        ]);

        if ($validation->fails()) {
            return redirect()->back()->withErrors($validation)->withInput();
        }

        // Check password
        if (!Hash::check($request->password, $user->password)) {
            return redirect()->back()->withErrors(['password' => 'Mot de passe incorrect.'])->withInput();
        }

        $user->email = $user->email . '_deleted_' . time();
        $user->save();
        $user->delete();

        // Log out user
        Auth::logout();

        return redirect()->route('home')->with('status', 'Votre compte a été supprimé avec succès.');
    }

    public function messages()
    {
        return view('messages', [
            'user_message' => null,
            'conversation' => null,
        ]);
    }

    public function updatePasswordIndex()
    {
        return view('candidate.update-password');
    }

    public function updatePasswordPut(Request $request)
    {
        $user = Auth::user();

        $validation = FacadesValidator::make($request->all(), [
            'current_password' => 'required|string',
            'password' => ['required', 'string', 'min:8', 'confirmed', Rules\Password::defaults()],
            'password_confirmation' => 'required|string|min:8|same:password',
        ], [
            'current_password.required' => 'Le mot de passe actuel est obligatoire.',
            'password.required' => 'Le nouveau mot de passe est obligatoire.',
            'password.min' => 'Le nouveau mot de passe doit contenir au moins 8 caractères.',
            'password.confirmed' => 'Les mots de passe ne correspondent pas.',
            'password_confirmation.required' => 'La confirmation du mot de passe est obligatoire.',
            'password_confirmation.same' => 'La confirmation du mot de passe doit correspondre.',
        ]);

        if ($validation->fails()) {
            return redirect()->back()->withErrors($validation)->withInput();
        }

        // Check password
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->back()->withErrors(['current_password' => 'Mot de passe actuel incorrect.'])->withInput();
        }

        $user->password = Hash::make($request->password);
        $user->save();

        return redirect()->route('candidate.update-password.index')->with('status', 'Mot de passe mis à jour avec succès.');
    }

    public function confirmCandidate($reminder_candidate_id, $value)
    {
        $validation = FacadesValidator::make([
            'reminder_candidate_id' => $reminder_candidate_id,
            'value' => $value,
        ], [
            'reminder_candidate_id' => 'required|exists:reminder_candidates,id',
            'value' => 'required|in:yes,no',
        ]);

        if ($validation->fails()) {
            return abort(404);
        }

        $reminder_candidate = ReminderCandidate::find($reminder_candidate_id);

        // Permettre la réponse même si le candidat a déjà répondu (pour changer d'avis)
        if ($value == 'no') {
            // Si le candidat refuse, on masque son profil et on arrête les rappels
            $user = User::find($reminder_candidate->user_id);
            $civility = Civility::where('user_id', $user->id)->first();

            if ($civility) {
                $civility->visibility = '0';
                $civility->save();
            }

            $reminder_candidate->search_work = false;
        } else {
            // Si le candidat accepte, on garde son profil visible et on continue les rappels
            $user = User::find($reminder_candidate->user_id);
            $civility = Civility::where('user_id', $user->id)->first();

            if ($civility) {
                $civility->visibility = '1'; // S'assurer que le profil est visible
                $civility->save();
            }

            $reminder_candidate->search_work = true;
        }

        // Réinitialiser la date du dernier rappel pour recommencer le cycle
        $reminder_candidate->last_reminder_sent_at = now();
        $reminder_candidate->save();

        return view('mail.response-reminder-candidate');
    }
}
