<?php

namespace App\Http\Controllers;

use App\Models\Package;
use Illuminate\Http\Request;

class PackageController extends Controller
{
    public function index()
    {
        $packages = Package::all(); // Récupère tous les packages
        return view('welcome', compact('packages'));
    }

    /**
     * Show the form for creating a new package.
     */
    public function create()
    {
        return view('admin.packages.create');
    }

    /**
     * Store a newly created package in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'features' => 'required|array',
            'features.*' => 'string',
        ]);

        // Formatage des données
        $packageData = [
            'name' => $validated['name'],
            'price' => $validated['price'],
            'features' => $validated['features'],
        ];

        // Création du package
        $package = Package::create($packageData);

        return redirect()->route('admin.packages.show')
            ->with('success', 'Package créé avec succès');
    }

    /**
     * Display the specified package.
     */
    public function show()
    {
        $packages = Package::all(); // Récupère tous les packages
        return view('admin.packages.index', compact('packages'));
    }

    /**
     * Show the form for editing the specified package.
     */
    public function edit(Package $package)
    {
        return view('admin.packages.edit', compact('package'));
    }

    public function update(Request $request, Package $package)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'features' => 'required|array',
            'features.*' => 'string',
        ]);

        // Formatage des données
        $packageData = [
            'name' => $validated['name'],
            'price' => $validated['price'],
            'features' => $validated['features'],
        ];

        $package->update($packageData);

        return redirect()->route('admin.packages.show')
            ->with('success', 'Package mis à jour avec succès');
    }
    /**
     * Remove the specified package from storage.
     */
    public function destroy(Package $package)
    {
        $package->delete();

        return redirect()->route('admin.packages.show') // Changé de 'show' à 'index'
            ->with('success', 'Package supprimé avec succès');
    }
}
