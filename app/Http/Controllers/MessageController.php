<?php

namespace App\Http\Controllers;

use App\Models\Message;
use App\Models\Conversation;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MessageController extends Controller
{
    public function sendMessage(Request $request)
    {
        $request->validate([
            'conversation_id' => 'nullable|exists:conversations,id',
            'candidate_id' => 'nullable|exists:users,id',
            'content' => 'required|string',
        ]);

        if ($request->conversation_id) {
            $message = Message::create([
                'conversation_id' => $request->conversation_id,
                'sender_id' => Auth::id(),
                'is_read' => false,
                'content' => $request->content,
            ]);
        } else {
            if ($request->candidate_id) {
                $candidate = User::find($request->candidate_id);

                $conversation = Conversation::where('user_one_id', Auth::id())
                    ->where('user_two_id', $candidate->id)
                    ->first();

                if (!$conversation) {
                    $conversation = Conversation::where('user_one_id', $candidate->id)
                        ->where('user_two_id', Auth::id())
                        ->first();
                } 

                if ($candidate->getRoleSlugAttribute() === 'recruter' || Auth::user()->getRoleSlugAttribute() === 'candidate' || $conversation) {
                    return abort(404);
                }

                $conversation = Conversation::create([
                    'user_one_id' => Auth::id(),
                    'user_two_id' => $request->candidate_id,
                ]);

                $message = Message::create([
                    'conversation_id' => $conversation->id,
                    'sender_id' => Auth::id(),
                    'is_read' => false,
                    'content' => $request->content,
                ]);
            } else {
                return abort(404);
            }
        }

        return response()->json($message, 201);
    }

    public function getMessages($conversationId)
    {
        $conversation = Conversation::findOrFail($conversationId);

        // Récupérer tous les messages de la conversation
        $messages = Message::where('conversation_id', $conversation->id)
            ->orderBy('created_at', 'asc')
            ->get();

        return response()->json($messages);
    }

    public function markAsRead($conversationId)
    {
        $conversation = Conversation::findOrFail($conversationId);  
        
        $messages = Message::where('conversation_id', $conversation->id)
            ->where('is_read', false)
            ->update(['is_read' => true]);

        return response()->json(['status' => 'Messages marked as read']);
    }
}
