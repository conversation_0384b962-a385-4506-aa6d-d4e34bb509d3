<?php
namespace App\Http\Controllers;

use App\Mail\ContactMail;
use App\Models\ProposalsContact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Services\ConfigsService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class ContactController extends Controller
{
    public function index()
    {
        return view('contact');
    }

    public function store(Request $request)
    {
        // Validation des champs du formulaire
        $request->validate([
            // 'g-recaptcha-response' => ['required', 'captcha'],
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'field_activities' => 'nullable|string|max:255',
            'professions' => 'nullable|string|max:255',
            'subject' => 'nullable|string|max:255', // Ajout du champ sujet
            'other' => ['nullable', 'string', 'max:2000', function ($attribute, $value, $fail) use ($request) {
                if (is_null($request->field_activities) && is_null($request->professions) && is_null($request->subject)) {
                    if (is_null($value) || trim($value) === '') {
                        $fail('Ce champ est obligatoire.');
                    }
                }
            }],
            'attachment' => 'nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx,txt|max:2048',
        ], [
            'g-recaptcha-response.required' => 'Veuillez cocher la case "Je ne suis pas un robot"',
            'g-recaptcha-response.captcha' => 'Erreur de validation du captcha, veuillez réessayer',
            'name.required' => 'Veuillez renseigner votre nom',
            'name.string' => 'Votre nom doit être une chaîne de caractères',
            'name.max' => 'Votre nom ne doit pas dépasser 255 caractères',
            'email.required' => 'Veuillez renseigner votre adresse email',
            'email.email' => 'Veuillez renseigner une adresse email valide',
            'email.max' => 'Votre adresse email ne doit pas dépasser 255 caractères',
            'field_activities.string' => 'Votre secteur d\'activité doit être une chaîne de caractères',
            'field_activities.max' => 'Votre secteur d\'activité ne doit pas dépasser 255 caractères',
            'professions.string' => 'Votre profession doit être une chaîne de caractères',
            'professions.max' => 'Votre profession ne doit pas dépasser 255 caractères',
            'subject.string' => 'Le sujet doit être une chaîne de caractères',
            'subject.max' => 'Le sujet ne doit pas dépasser 255 caractères',
            'other.string' => 'Votre message doit être une chaîne de caractères',
            'other.max' => 'Votre message ne doit pas dépasser 2000 caractères',
            'attachment.file' => 'Le fichier doit être un fichier valide.',
            'attachment.mimes' => 'Le fichier doit être de type : jpg, jpeg, png, pdf, doc, docx, txt.',
            'attachment.max' => 'Le fichier ne doit pas dépasser 2 Mo.',
        ]);

        // Déterminer si l'utilisateur est un recruteur ou un candidat
        $isRecruter = \App\Helpers\UsedUpFunction::isRecruter();
        // dd($isRecruter);

        // Sauvegarder les données dans la table proposals_contact
        $proposalsContact = new ProposalsContact();
        $proposalsContact->name = $request->name;
        $proposalsContact->email = $request->email;
        $proposalsContact->field_activities = $request->field_activities;
        $proposalsContact->professions = $request->professions;
        $proposalsContact->subject = $request->subject; // Sauvegarder le sujet
        $proposalsContact->other = $request->other;
        $proposalsContact->save();

        // Récupérer l'adresse e-mail de l'administrateur
        $adminMail = ConfigsService::getAdminMailConfig();

        if (!$adminMail) {
            return redirect()->back()->withErrors(['error' => 'L\'adresse e-mail de l\'administrateur n\'est pas configurée.']);
        }

        try {
            // Envoyer l'e-mail à l'administrateur
            $mail = new ContactMail($request->all(), $isRecruter, $request->subject); // Passer l'état de l'utilisateur

            // Ajouter la pièce jointe si un fichier est uploadé
            if ($request->hasFile('attachment')) {
                $file = $request->file('attachment');
                $filePath = $file->getRealPath();
                $mail->attach($filePath, [
                    'as' => $file->getClientOriginalName(),
                    'mime' => $file->getClientMimeType(),
                ]);
            }

            Mail::to($adminMail)->send($mail);

            return redirect()->back()->with('status', "Votre message a bien été envoyé !");
        } catch (\Exception $e) {
            return redirect()->back()->withErrors(['error' => 'Une erreur est survenue lors de l\'envoi de votre message. Veuillez réessayer plus tard.']);
        }
    }
}