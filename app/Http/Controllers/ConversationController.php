<?php

namespace App\Http\Controllers;

use App\Models\Conversation;
use App\Models\Message;
use App\Models\User;
use Dotenv\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator as FacadesValidator;

class ConversationController extends Controller
{
    public function createOrGetConversation(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $userId = Auth::id();
        $otherUserId = $request->user_id;

        // Vérifier si une conversation existe déjà entre les deux utilisateurs
        $conversation = Conversation::where(function ($query) use ($userId, $otherUserId) {
            $query->where('user_one_id', $userId)->where('user_two_id', $otherUserId);
        })->orWhere(function ($query) use ($userId, $otherUserId) {
            $query->where('user_one_id', $otherUserId)->where('user_two_id', $userId);
        })->first();

        // Si la conversation n'existe pas, on la crée
        if (!$conversation) {
            $conversation = Conversation::create([
                'user_one_id' => $userId,
                'user_two_id' => $otherUserId,
            ]);
        }

        return response()->json($conversation, 201);
    }

    public function getUserConversations()
    {
        $userId = Auth::id();

        // Récupérer toutes les conversations de l'utilisateur
        $conversations = Conversation::where('user_one_id', $userId)
            ->orWhere('user_two_id', $userId)
            ->get();

        foreach ($conversations as $conversation) {
            // Récupérer l'utilisateur opposé à la conversation
            $conversation->user = $conversation->user_one_id === $userId
                ? User::find($conversation->user_two_id)
                : User::find($conversation->user_one_id);
            $conversation->user->photo = $conversation->user?->civility()?->photo ?? '';
            // Ajouter l'attribut real_name avec le nom complet calculé
            $conversation->user->real_name = $conversation->user->fullname();
            // Récupérer le dernier message de la conversation
            $conversation->last_message = $conversation->last_message();
        }

        return response()->json($conversations);
    }


    public function getUserMessages(Request $request, $user_id)
    {
        $validator = FacadesValidator::make([
            'user_id' => $user_id,
        ], [
            'user_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $user_message = User::find($request->user_id);

        $conversation = Conversation::where('user_one_id', Auth::id())
            ->where('user_two_id', $user_message->id)
            ->first();

        if (!$conversation) {
            $conversation = Conversation::where('user_one_id', $user_message->id)
                ->where('user_two_id', Auth::id())
                ->first();
        }

        return view('messages', compact('user_message', 'conversation'));
    }

    public function messagesCount()
    {
        $userId = Auth::id();

        // Récupérer toutes les conversations de l'utilisateur
        $conversations = Conversation::where('user_two_id', $userId)
            ->orWhere('user_one_id', $userId)
            ->get();

        $count = 0;
        foreach ($conversations as $conversation) {
            $count += Message::where('conversation_id', $conversation->id)
                ->where('is_read', false)
                ->count();
        }

        return response()->json([
            'count' => $count,
        ]);
    }
}
