<?php

namespace App\Http\Controllers;

use App\Models\ConfigGlobalApp;
use Illuminate\Http\Request;

class ParametreAdminMailController extends Controller
{
    public function index()
    {
        // Récupérer la configuration de l'email admin
        $adminMailConfig = ConfigGlobalApp::where('name', 'admin_mail')->first();

        // Passer les données à la vue
        return view('admin.admin-mail.index', compact('adminMailConfig'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'value' => 'required|email',
        ]);

        $adminMailConfig = ConfigGlobalApp::where('name', 'admin_mail')->first();

        if ($adminMailConfig) {
            $adminMailConfig->update(['value' => $request->value]);

            return redirect()->back()->with('success', 'Email admin mis à jour avec succès.');
        }

        return redirect()->back()->with('error', 'Aucun paramètre d\'email admin trouvé.');
    }
}
