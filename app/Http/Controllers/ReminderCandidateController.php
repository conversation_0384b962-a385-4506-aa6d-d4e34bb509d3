<?php

namespace App\Http\Controllers;

use App\Models\ReminderCandidate;
use App\Models\User;
use App\Models\Role;
use App\Mail\CustomCandidateMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ReminderCandidateController extends Controller
{
    /**
     * Afficher la liste des rappels candidats
     */
    public function index(Request $request)
    {
        $query = ReminderCandidate::with(['user', 'user.civility']);

        // Filtrage par statut de réponse
        if ($request->filled('response_status')) {
            $query->byResponseStatus($request->response_status);
        }

        // Filtrage par date de dernier rappel
        if ($request->filled('last_reminder_date')) {
            $date = $request->last_reminder_date;
            $query->whereDate('last_reminder_sent_at', $date);
        }

        // Recherche par nom/email
        if ($request->filled('search')) {
            $query->searchByCandidate($request->search);
        }

        // Tri par défaut : les plus anciens sans réponse en premier
        $query->orderBy('last_reminder_sent_at', 'asc');

        $reminders = $query->paginate(15);

        // Statistiques
        $stats = $this->getStatistics();

        return view('admin.reminder-candidates.index', compact('reminders', 'stats'));
    }

    /**
     * Envoyer un email individuel
     */
    public function sendIndividualEmail(Request $request, ReminderCandidate $reminder)
    {
        $request->validate([
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        try {
            Mail::to($reminder->user->email)->send(
                new CustomCandidateMail(
                    $reminder->user,
                    $request->subject,
                    $request->message
                )
            );

            // Incrémenter le compteur de rappels
            $reminder->incrementReminderCount();

            return response()->json([
                'success' => true,
                'message' => 'Email envoyé avec succès à ' . $reminder->user->email
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'envoi de l\'email : ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Envoyer des emails groupés
     */
    public function sendBulkEmails(Request $request)
    {
        $request->validate([
            'reminder_ids' => 'required|array|min:1',
            'reminder_ids.*' => 'exists:reminder_candidates,_id',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        $reminders = ReminderCandidate::with('user')
            ->whereIn('_id', $request->reminder_ids)
            ->get();

        $successCount = 0;
        $errors = [];

        foreach ($reminders as $reminder) {
            try {
                Mail::to($reminder->user->email)->send(
                    new CustomCandidateMail(
                        $reminder->user,
                        $request->subject,
                        $request->message
                    )
                );

                // Incrémenter le compteur de rappels
                $reminder->incrementReminderCount();
                $successCount++;
            } catch (\Exception $e) {
                $errors[] = "Erreur pour {$reminder->user->email}: " . $e->getMessage();
            }
        }

        return response()->json([
            'success' => true,
            'message' => "{$successCount} emails envoyés avec succès",
            'errors' => $errors
        ]);
    }

    /**
     * Mettre à jour le statut de réponse d'un candidat
     */
    public function updateResponseStatus(Request $request, ReminderCandidate $reminder)
    {
        $request->validate([
            'response_status' => 'required|in:yes,no,no_response'
        ]);

        $reminder->update([
            'response_status' => $request->response_status
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Statut mis à jour avec succès'
        ]);
    }

    /**
     * Obtenir les statistiques des rappels
     */
    private function getStatistics()
    {
        $total = ReminderCandidate::count();

        // Pour MongoDB, nous comptons manuellement par statut
        $yesCount = ReminderCandidate::where('response_status', 'yes')->count();
        $noCount = ReminderCandidate::where('response_status', 'no')->count();
        $noResponseCount = ReminderCandidate::where('response_status', 'no_response')->count();

        // Calculer la moyenne des rappels
        $averageReminders = ReminderCandidate::avg('reminder_count') ?? 0;

        // Obtenir les plus anciens sans réponse
        $oldestWithoutResponse = ReminderCandidate::with('user')
            ->where('response_status', 'no_response')
            ->orderBy('last_reminder_sent_at', 'asc')
            ->limit(5)
            ->get();

        return [
            'total' => $total,
            'yes_count' => $yesCount,
            'no_count' => $noCount,
            'no_response_count' => $noResponseCount,
            'average_reminders' => round($averageReminders, 1),
            'oldest_without_response' => $oldestWithoutResponse
        ];
    }

    /**
     * Exporter les données en CSV
     */
    public function exportCsv(Request $request)
    {
        $query = ReminderCandidate::with(['user', 'user.civility']);

        // Appliquer les mêmes filtres que l'index
        if ($request->filled('response_status')) {
            $query->byResponseStatus($request->response_status);
        }

        if ($request->filled('last_reminder_date')) {
            $date = $request->last_reminder_date;
            $query->whereDate('last_reminder_sent_at', $date);
        }

        if ($request->filled('search')) {
            $query->searchByCandidate($request->search);
        }

        $reminders = $query->get();

        $filename = 'rappels_candidats_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($reminders) {
            $file = fopen('php://output', 'w');
            
            // En-têtes CSV
            fputcsv($file, [
                'Nom',
                'Prénom', 
                'Email',
                'Statut de réponse',
                'Date du dernier rappel',
                'Nombre de rappels',
                'Prochaine date de rappel'
            ]);

            // Données
            foreach ($reminders as $reminder) {
                $civility = $reminder->user->civility;
                fputcsv($file, [
                    $civility->last_name ?? '',
                    $civility->first_name ?? '',
                    $reminder->user->email,
                    $reminder->formatted_response_status,
                    $reminder->last_reminder_sent_at ? $reminder->last_reminder_sent_at->format('d/m/Y H:i') : '',
                    $reminder->reminder_count ?? 0,
                    $reminder->next_reminder_date ? $reminder->next_reminder_date->format('d/m/Y') : ''
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
