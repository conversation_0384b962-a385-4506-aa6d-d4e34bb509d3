<?php

namespace App\Http\Controllers;

use App\Models\ConfigGlobalApp;
use Illuminate\Http\Request;

class ParametreCaptchaController extends Controller
{
    public function index()
    {
        // Récupérer la configuration de Captcha
        $captchaConfig = ConfigGlobalApp::where('name', 'captcha_config')->first();

        // Décoder la valeur JSON
        if ($captchaConfig) {
            $captchaConfig->value = json_decode($captchaConfig->value, true);
        }

        // Passer les données à la vue
        return view('admin.parametre-captcha.index', compact('captchaConfig'));
    }

    public function update(Request $request)
    {
        // Valider les données du formulaire
        $request->validate([
            'key' => 'required|string',
            'value' => 'required|string',
        ]);

        // Récupérer la configuration de Captcha
        $captchaConfig = ConfigGlobalApp::where('name', 'captcha_config')->first();

        if (!$captchaConfig) {
            return redirect()->route('parametre-captcha')->with('error', 'Configuration Captcha non trouvée.');
        }

        // Décoder la valeur JSON
        $configValues = json_decode($captchaConfig->value, true);

        // Mettre à jour la valeur spécifique
        $key = $request->input('key');
        $configValues[$key] = $request->input('value');

        // Encoder à nouveau en JSON et mettre à jour la base de données
        $captchaConfig->value = json_encode($configValues);
        $captchaConfig->save();

        // Rediriger vers la route spécifiée avec un message de succès
        return redirect()->route('parametre-captcha')->with('success', 'Paramètre mis à jour avec succès.');
    }
}
