<?php

namespace App\Http\Controllers;

use App\Models\ConfigGlobalApp;
use App\Models\Mantis;
use App\Models\Priority;
use App\Models\Severity;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class MantisController extends Controller
{

    private $apiUrl = "https://bugtracker.cyclone-placement.ch/api/rest/issues";
    private $apiKey = "docuEFy3WtEAaa6wbmfXBt1mVwAcJV94"; // À sécuriser via .env


    /**
     * Récupérer la liste des catégories depuis l'API Mantis.
     */
    public function getCategories()
    {
        $client = new Client();
        $url = "https://bugtracker.cyclone-placement.ch/api/rest/projects";
        $apiKey = "docuEFy3WtEAaa6wbmfXBt1mVwAcJV94"; // Remplacez par votre clé API sécurisée

        try {
            $response = $client->request('GET', $url, [
                'headers' => [
                    'Authorization' => $apiKey,
                    'Content-Type' => 'application/json',
                ],
            ]);

            $data = json_decode($response->getBody(), true);

            // Extraire les catégories
            $categories = [];
            foreach ($data['projects'] as $project) {
                if (isset($project['categories'])) {
                    foreach ($project['categories'] as $category) {
                        $categories[] = [
                            'id' => $category['id'],
                            'name' => $category['name'],
                        ];
                    }
                }
            }

            return response()->json(['categories' => $categories]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Impossible de récupérer les catégories'], 500);
        }
    }

    /**
     * Récupérer une catégorie spécifique depuis l'API Mantis par son ID.
     */
    public function getCategoryById($categoryId)
    {
        $client = new Client();
        $url = "https://bugtracker.cyclone-placement.ch/api/rest/projects";
        $apiKey = "docuEFy3WtEAaa6wbmfXBt1mVwAcJV94"; // Remplacez par votre clé API sécurisée

        try {
            $response = $client->request('GET', $url, [
                'headers' => [
                    'Authorization' => $apiKey,
                    'Content-Type' => 'application/json',
                ],
            ]);

            $data = json_decode($response->getBody(), true);

            // Rechercher la catégorie par ID
            foreach ($data['projects'] as $project) {
                if (isset($project['categories'])) {
                    foreach ($project['categories'] as $category) {
                        if ($category['id'] == $categoryId) {
                            return response()->json([
                                'category' => [
                                    'id' => $category['id'],
                                    'name' => $category['name'],
                                    'project_id' => $project['id'],
                                    'project_name' => $project['name']
                                ]
                            ]);
                        }
                    }
                }
            }

            return response()->json(['error' => 'Catégorie non trouvée'], 404);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Impossible de récupérer la catégorie: ' . $e->getMessage()], 500);
        }
    }

    public function getListPrioritiesAndListSeverities()
    {
        $priorities = Priority::all();
        $severities = Severity::all();

        // dd($priorities, $severities);

        return view('layouts.guest', compact('priorities', 'severities'));
    }

    /**
     * Ajouter une anomalie à Mantis Bug Tracker
     */
    public function addIssue(Request $request)
    {
        // Validation des données
        $validatedData = $request->validate([
            'summary' => 'required|string|max:255',
            'description' => 'required|string',
            // 'category_id' => 'required|integer',
            // 'priority_id' => 'required|integer',
            // 'severity_id' => 'required|integer',
            'email' => 'required|email|max:255',
        ]);

        //try {
            // Commenté : récupération des noms et logique liée à la catégorie, priorité, sévérité
            // $priority = Priority::find($validatedData['priority_id']);
            // $severity = Severity::find($validatedData['severity_id']);

            // Création dans la base de données locale (MongoDB)
            $mantisHistory = Mantis::create([
                'email' => $validatedData['email'],
                'summary' => $validatedData['summary'],
                'description' => $validatedData['description'],
                // 'category_id' => $validatedData['category_id'],
                // 'priority_id' => $validatedData['priority_id'],
                // 'severity_id' => $validatedData['severity_id'],
            ]);

            // Commenté : Envoi à l'API Mantis et récupération des noms
            // $client = new Client();
            // $data = [ ... ];
            // $response = $client->request('POST', $this->apiUrl, [...]);
            // $result = json_decode($response->getBody(), true);
            // $priorityName = Priority::where("value", $validatedData['priority_id'])->first();
            // $severityName = Severity::where("value", $validatedData['severity_id'])->first();
            // $response = $this->getCategoryById($request->input('category_id'));
            // $categoryName = $response->getData()->category->name;
            // $categoryName = $response->original['category']['name'];

            // Envoi de l'email de notification (on ne passe que les champs restants)
            $emailData = [
                'summary' => $validatedData['summary'],
                'description' => $validatedData['description'],
                // 'category_id' => $categoryName,
                // 'priority' => $priorityName->name,
                // 'severity' => $severityName->name,
                'user_email' => $validatedData['email'],
                'date' => now()->format('d/m/Y H:i'),
            ];

            // Récupérer la configuration de l'email admin
            $adminMailConfig = ConfigGlobalApp::where('name', 'admin_mail')->first();

            Mail::to($adminMailConfig->value)->send(new \App\Mail\IssueNotification($emailData));

            return response()->json([
                'success' => true,
                'message' => 'Anomalie ajoutée avec succès !',
                // 'data' => $result,
                'local_id' => $mantisHistory->id
            ], 201);
      /*   } catch (\Exception $e) {
            \Log::error('Erreur addIssue: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du traitement',
                'error' => $e->getMessage()
            ], 500);
        } */
    }
}
