<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use Illuminate\View\View;

class PasswordResetLinkController extends Controller
{
    /**
     * Display the password reset link request view.
     */
    public function create(): View
    {
        return view('auth.forgot-password');
    }

    /**
     * Handle an incoming password reset link request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'email' => ['required', 'email', 'exists:users,email'],
        ], [
            'email.required' => 'Veuillez renseigner votre adresse email.',
            'email.email' => 'Veuillez renseigner une adresse email valide.',
            'email.exists' => 'Aucun compte n\'a été trouvé avec cette adresse email.',
        ]);

        // Generate the reset token
        $status = Password::broker()->sendResetLink(
            $request->only('email'),
            function ($user, $token) use ($request) {
                // Generate the reset URL
                $resetUrl = url(route('password.reset', [
                    'token' => $token,
                    'email' => $request->email,
                ], false));

                // Send the custom email
                \Mail::to($user->email)->send(new \App\Mail\CustomPasswordReset($resetUrl));
            }
        );

        return $status === Password::RESET_LINK_SENT
            ? back()->with('status', __($status))
            : back()->withInput($request->only('email'))
            ->withErrors(['email' => __($status)]);
    }
}
