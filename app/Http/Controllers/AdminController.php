<?php

namespace App\Http\Controllers;

use App\Models\FieldActivity;
use App\Models\Mantis;
use App\Models\Profession;
use App\Models\Role;
use App\Models\User;
use GuzzleHttp\Client;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use MongoDB\BSON\Regex;
use App\Models\File;
use App\Models\UserFieldActivity;
use App\Models\UserProfession;
use App\Models\UserTypeProfession;
use App\Models\UserFormation;
use App\Models\UserPermit;
use App\Models\Permit;
use App\Models\ResidencePermit;
use App\Models\Country;
use App\Models\TypeProfession;
use App\Models\ResponsibilityCandidate;
use App\Models\Language;
use App\Models\Formation;

class AdminController extends Controller
{
    //index
    public function index()
    {
        $role_id = Role::where('slug', 'recruter')->first()->id;
        $recruiterCount = User::where('role_id', $role_id)->count();
        $role_id = Role::where('slug', 'candidate')->first()->id;
        $candidateCount = User::where('role_id', $role_id)->count();
        return view('admin.index', [
            'recruiterCount' => $recruiterCount,
            'candidateCount' => $candidateCount
        ]);
    }
    //showRecruiter
    public function showRecruiter($id)
    {
        $user = User::find($id);
        $recruiter = $user->civility();
        $fullName = $user->fullname();
        $country = $user->getCountryOfResidence();
        $phoneNumber = $user->getPhoneNumber();
        return view('admin.recruters.show', [
            'recruiter' => $recruiter,
            'fullName' => $fullName,
            'country' => $country,
            'phoneNumber' => $phoneNumber,
            'user' => $user
        ]);
    }

    public function showCandidate($id)
    {
        $user = User::find($id);

        // Récupérer les données de base
        $civility = $user->civility();
        $fullName = $user->fullname();
        $country = $user->getCountryOfResidence();
        $phoneNumber = $user->getPhoneNumber();
        $permis = $user->getResidencePermit();
        $profession = $user->getProffession();
        $responsiblityCandidat = $user->getResponsiblityCandidat();

        // Récupérer les langues
        $natifLanguage = $user->getNatifLanguage();
        $fluentteLanguage = $user->getFluentteLanguage();
        $intermediateLanguages = $user->getIntermediateLanguages();
        $basicLanguages = $user->getBasicLanguages();

        // Récupérer les fichiers
        $photo = File::where('id', $civility->photo_file_id)->first();
        $cv = File::where('id', $civility->cv_file_id)->first();
        $workCertificates = File::where('id', $civility->work_certificates_file_id)->first();
        $studyCertificates = File::where('id', $civility->study_certificates_file_id)->first();

        // Récupérer les activités et professions
        $userFieldActivities = UserFieldActivity::where('user_id', $user->id)->pluck('field_activity_id')->toArray();
        $userProfessions = UserProfession::where('user_id', $user->id)->pluck('profession_id')->toArray();
        $userTypeProfessions = UserTypeProfession::where('user_id', $user->id)->pluck('type_profession_id')->toArray();
        $userFormations = UserFormation::where('user_id', $user->id)->pluck('formation_id')->toArray();
        $userPermits = UserPermit::where('user_id', $user->id)->pluck('permit_id')->toArray();

        // Récupérer les listes de référence
        $permits = Permit::getAllActive();
        $residencePermits = ResidencePermit::getAllActive();
        $countries = Country::getAllActive();
        $fieldActivities = FieldActivity::getAllActive();
        $typeProfessions = TypeProfession::getAllActive();
        $professions = Profession::getAllActive();
        $responsibilities = ResponsibilityCandidate::getAllActive();
        $languages = Language::getAllActive();
        $formations = Formation::getAllActive();

        return view('admin.candidates.show', [
            'user' => $user,
            'civility' => $civility,
            'fullName' => $fullName,
            'country' => $country,
            'phoneNumber' => $phoneNumber,
            'permis' => $permis,
            'profession' => $profession,
            'responsiblityCandidat' => $responsiblityCandidat,
            'natifLanguage' => $natifLanguage,
            'fluentteLanguage' => $fluentteLanguage,
            'intermediateLanguages' => $intermediateLanguages,
            'basicLanguages' => $basicLanguages,
            'photo' => $photo,
            'cv' => $cv,
            'workCertificates' => $workCertificates,
            'studyCertificates' => $studyCertificates,
            'userFieldActivities' => $userFieldActivities,
            'userProfessions' => $userProfessions,
            'userTypeProfessions' => $userTypeProfessions,
            'userFormations' => $userFormations,
            'userPermits' => $userPermits,
            'permits' => $permits,
            'residencePermits' => $residencePermits,
            'countries' => $countries,
            'fieldActivities' => $fieldActivities,
            'typeProfessions' => $typeProfessions,
            'professions' => $professions,
            'responsibilities' => $responsibilities,
            'languages' => $languages,
            'formations' => $formations
        ]);
    }



    // Get all recruiters
    public function getAllRecruiter($f = null)
    {
        $role_id = Role::where('slug', 'recruter')->first()->id;
        $query = User::where('role_id', $role_id)
            ->where('email', 'not like', '%_deleted%');

        if ($f == 'inactive') {
            $query->where('is_suspend', true);
        } elseif ($f == 'active') {
            $query->where('is_suspend', false);
        }

        $recruiters = $query->paginate(10);
        return view('admin.recruters.index', ['recruiters' => $recruiters]);
    }

    public function searchRecruiter(Request $request)
    {
        // Valider la requête
        $validated = $request->validate([
            'query' => 'required|string|max:255',
        ]);

        $query = $validated['query'];

        // Récupérer l'ID du rôle "recruter"
        $role = \App\Models\Role::where('slug', 'recruter')->first();

        if (!$role) {
            return redirect()->back()->withErrors(['error' => 'Le rôle "recruter" est introuvable.']);
        }

        $role_id = $role->id;

        // Rechercher les recruteurs par email, nom ou prénom
        $recruiters = \App\Models\User::where('role_id', $role_id)
            ->where('email', 'not like', '%_deleted%')
            ->where(function ($q) use ($query) {
                $q->where('email', 'like', "%{$query}%")
                    ->orWhereHas('civilityRelation', function ($subQuery) use ($query) {
                        $subQuery->where('first_name', 'like', "%{$query}%")
                                 ->orWhere('company_name', 'like', "%{$query}%");
                    });
            })
            ->paginate(10);

        // Passer les résultats paginés à la vue
        return view('admin.recruters.index', [
            'recruiters' => $recruiters,
            'query' => $query,
        ]);
    }


    public function searchCandidat(Request $request)
    {
        // Validation de la requête
        $validated = $request->validate([
            'query' => 'required|string|max:255',
        ]);

        $query = $validated['query'];

        // Récupérer l'ID du rôle "candidate"
        $role = \App\Models\Role::where('slug', 'candidate')->first();

        if (!$role) {
            return redirect()->back()->withErrors(['error' => 'Le rôle "candidate" est introuvable.']);
        }

        $role_id = $role->_id;

        // Recherche dans les utilisateurs avec le rôle "candidate"
        $candidatesQuery = \App\Models\User::where('role_id', $role_id)
            ->where('email', 'not like', '%_deleted%')
            ->where(function ($q) use ($query) {
                $q->where('email', 'like', "%{$query}%")
                    ->orWhereHas('civilityRelation', function ($subQuery) use ($query) {
                        $subQuery->where('first_name', 'like', "%{$query}%")
                            ->orWhere('last_name', 'like', "%{$query}%");
                    });
            });

        $selectedContractTypes = [];
        if ($request->has('contract_type') && !empty($request->contract_type)) {
            $selectedContractTypes = $request->contract_type;
            $candidatesQuery->whereHas('civilityRelation', function ($q) use ($selectedContractTypes) {
                $q->whereIn('contract_type', $selectedContractTypes);
            });
        };

        $candidates = $candidatesQuery->paginate(10);


        // Retourner la vue avec les données
        return view('admin.candidates.index', [
            'candidates' => $candidates,
            'query' => $query,
            'selectedContractTypes' => $selectedContractTypes,

        ]);
    }


    public function addActivity(Request $request)
    {
        // Valider les données entrantes
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
        ]);

        // Génération d'un slug unique
        $slug = Str::slug($validatedData['name']);
        $originalSlug = $slug;
        $counter = 1;
        while (FieldActivity::where('slug', operator: $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Créer une nouvelle activité
        $activity = FieldActivity::create([
            'name' => $validatedData['name'],
            'slug' => $slug,
            'is_active' => true,
        ]);

        // Retourner une réponse JSON en cas de succès
        return response()->json([
            'success' => true,
            'message' => 'Activité "' . $activity->name . '" ajoutée avec succès !',
        ]);
    }


    // Méthode pour afficher toutes les activités avec pagination
    public function showActivities($f = null)
    {
        // Définir la requête de base
        $query = FieldActivity::query();

        // Appliquer des filtres selon la valeur de $f
        switch ($f) {
            case 'active':
                $query->where('is_active', true);
                break;
            case 'inactive':
                $query->where('is_active', false);
                break;
        }

        // Récupérer les résultats paginés
        $activities = $query->paginate(10);

        // Retourner la vue avec les activités
        return view('admin.activities.index', ['activities' => $activities]);
    }


    /**
     * Retourne l'ID d'une activité à partir de son nom
     *
     * @param string $name Le nom de l'activité
     * @return string|null L'ID de l'activité ou null si non trouvée
     */
    public function getActivityIdByName(string $name): ?string
    {
        $activity = FieldActivity::where('name', $name)->first();

        return $activity ? $activity->_id : null;
    }


    // Méthode pour modifier une activité
    public function updateActivity(Request $request, $id)
    {
        // Valider les données entrantes
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
        ]);

        // Rechercher l'activité par son identifiant
        $activity = FieldActivity::findOrFail($id);

        // Générer un slug unique uniquement si le nom a changé
        if ($activity->name !== $validatedData['name']) {
            $slug = Str::slug($validatedData['name']);
            $originalSlug = $slug;
            $counter = 1;
            while (FieldActivity::where('slug', $slug)->where('id', '!=', $id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }
            $activity->slug = $slug;
        }

        // Mettre à jour le nom de l'activité et d'autres champs si nécessaire
        $activity->name = $validatedData['name'];
        $activity->save();

        // Retourner une réponse JSON en cas de succès
        return response()->json([
            'success' => true,
            'message' => 'Activité "' . $activity->name . '" modifiée avec succès !',
        ]);
    }

    // Méthode pour supprimer une activité
    public function deleteActivity($id)
    {
        // Récupération de l'activité existante
        $activity = FieldActivity::findOrFail($id);

        // Suppression de l'activité
        $activity->delete();

        // Rediriger ou retourner une réponse après la suppression
        return redirect()->back()->with('success', 'Activité "' . $activity->name . '" supprimée avec succès !');
    }


    public function searchActivity(Request $request)
    {
        $validated = $request->validate([
            'query' => 'required|string|max:255',
        ]);

        $query = $validated['query'];
        $normalizedQuery = \App\Models\FieldActivity::normalizeString($query);

        $activities = \App\Models\FieldActivity::where(function($q) use ($normalizedQuery, $query) {
            $q->where('normalizedName', 'like', '%' . $normalizedQuery . '%')
              ->orWhere('name', 'like', '%' . $query . '%');
        })->paginate(10);

        return view('admin.activities.index', compact('activities', 'query'));
    }


    public function enableDisableActivity($activityId)
    {
        $activity = FieldActivity::find($activityId);
        if ($activity) {
            $activity->is_active = !$activity->is_active; // Inverse l'état
            $activity->save();
            return response()->json(['is_active' => $activity->is_active]); // Retourne le nouvel état
        }

        return response()->json(['error' => 'Activity not found'], 404); // Retourne un message d'erreur si l'activité n'existe pas
    }


    // Get all recruiters
    public function getAllCandidats(Request $request, $f = null)
    {

        // Récupérer l'ID du rôle "candidate"
        $role_id = Role::where('slug', 'candidate')->first()->id;

        // Initialiser la requête en excluant les emails contenant '_deleted'
        $query = User::where('role_id', $role_id)
            ->where('email', 'not like', '%_deleted%');

        // Filtrer par statut (activé/désactivé)
        if ($f == 'inactive') {
            $query->where('is_suspend', true); // Candidats désactivés
        } elseif ($f == 'active') {
            $query->where('is_suspend', false); // Candidats activés
        }


        // Filtrer par type de contrat
        $selectedContractTypes = [];
        if ($request->has('contract_type')) {
            $selectedContractTypes = $request->contract_type;
            $query->whereHas('civilityRelation', function ($q) use ($selectedContractTypes) {
                $q->whereIn('contract_type', $selectedContractTypes);
            });
        }

        // Paginer les résultats
        $candidates = $query->paginate(10);

        // Retourner la vue avec les données
        return view('admin.candidates.index', [
            'candidates' => $candidates,
            'selectedContractTypes' => $selectedContractTypes, // Passer les types de contrat sélectionnés à la vue
        ]);
    }

    // proffessionIndex
    public function proffessionIndex($f = null)
    {
        if ($f == 'inactive') {
            $professions = \App\Models\Profession::where('is_active', false)->paginate(10);
        } elseif ($f == 'active') {
            $professions = \App\Models\Profession::where('is_active', true)->paginate(10);
        } else {
            $professions = \App\Models\Profession::paginate(10);
        }

        // Récupération des domaines d'activité actifs
        $fieldActivities = \App\Models\FieldActivity::getAllActive();

        return view('admin.professions.index', [
            'professions' => $professions,
            'fieldActivities' => $fieldActivities
        ]);
    }

    // searchProfession
    // public function proffessionSearch(Request $request)
    // {
    //     $professions = \App\Models\Profession::where('name', 'like', '%' . $request->search . '%')->get();
    //     return response()->json($professions);
    // }

    public function professionSearch(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $name = $validated['name'];

        // Normalise la recherche côté PHP
        $normalizedName = \App\Models\Profession::normalizeString($name);


        // Cherche dans normalizedName avec regex insensible à la casse
        $professions = \App\Models\Profession::where('normalizedName', 'regexp', new \MongoDB\BSON\Regex($normalizedName, 'i'))
        ->paginate(10);
        // dd($professions);

        $fieldActivities = \App\Models\FieldActivity::getAllActive();

        return view('admin.professions.index', compact('professions', 'name', 'fieldActivities'));
    }




    // updateProfession
    public function proffessionUpdate(Request $request, $id)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            // 'field_activity_id' => 'required|exists:field_activities,_id'
        ]);

        $profession = \App\Models\Profession::findOrFail($id);

        $profession->update([
            'name' => $validatedData['name'],
            'slug' => Str::slug($validatedData['name']),
            'field_activity_id' => $request->field_activity_id
        ]);

        return redirect()->route('admin.profession')
            ->with('success', 'Profession mise à jour avec succès.');
    }

    public function proffessionStore(Request $request)
    {
        // dd($request->all());
        try {
            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                // 'field_activity_id' => [
                //     'required',
                //     Rule::exists('field_activities', '_id')->where('is_active', true)
                // ]
            ]);

            $slug = Str::slug($validatedData['name']);

            if (Profession::where('slug', $slug)->exists()) {
                throw new \Exception('Une profession avec ce nom existe déjà.');
            }

            Profession::create([
                'name' => $validatedData['name'],
                'slug' => $slug,
                'is_active' => true,
                'field_activity_id' => $request->field_activity_id,
            ]);

            return redirect()->back()->with('success', 'Profession ajoutée avec succès.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', $e->getMessage());
        }
    }



    // proffessionToggleIsActive
    public function proffessionToggleIsActive($id)
    {
        $profession = \App\Models\Profession::findOrFail($id);
        $before = $profession->is_active;
        if ($profession->is_active == true) {
            $profession->is_active = false;
        } else {
            $profession->is_active = true;
        }
        $after = $profession->is_active;
        $profession->save();
        return redirect()->route('admin.profession')->with('success', 'Profession mise à jour avec succès');
    }

    // proffessionDelete
    public function proffessionDelete($id)
    {
        $profession = \App\Models\Profession::findOrFail($id);
        $profession->delete();
        return redirect()->route('admin.profession')->with('success', 'Profession supprimée avec succès');
    }

    public function showSidebar()
    {
        $role_id = \App\Models\Role::where('slug', 'recruter')->first()->id;
        $recruiterCount = \App\Models\User::where('role_id', $role_id)->count();

        return view('side-bar', compact('recruiterCount'));
    }

    public function suspendRecruiter($id)
    {
        $recruiter = User::find($id);
        $recruiter->is_suspend = true;
        $recruiter->save();
        return redirect()->back()->with('success', 'Recruteur suspendu avec succès');
    }

    public function unsuspendRecruiter($id)
    {
        $recruiter = User::find($id);
        $recruiter->is_suspend = false;
        $recruiter->save();
        return redirect()->back()->with('success', 'Recruteur réactivé avec succès');
    }

    public function getCategoryById($categoryId)
    {
        $client = new Client();
        $url = "https://bugtracker.cyclone-placement.ch/api/rest/projects";
        $apiKey = "docuEFy3WtEAaa6wbmfXBt1mVwAcJV94"; // Remplacez par votre clé API sécurisée

        try {
            $response = $client->request('GET', $url, [
                'headers' => [
                    'Authorization' => $apiKey,
                    'Content-Type' => 'application/json',
                ],
            ]);

            $data = json_decode($response->getBody(), true);

            // Rechercher la catégorie par ID
            foreach ($data['projects'] as $project) {
                if (isset($project['categories'])) {
                    foreach ($project['categories'] as $category) {
                        if ($category['id'] == $categoryId) {
                            return response()->json([
                                'category' => [
                                    'id' => $category['id'],
                                    'name' => $category['name'],
                                    'project_id' => $project['id'],
                                    'project_name' => $project['name']
                                ]
                            ]);
                        }
                    }
                }
            }

            return response()->json(['error' => 'Catégorie non trouvée'], 404);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Impossible de récupérer la catégorie: ' . $e->getMessage()], 500);
        }
    }

    public function getAllAvis()
    {
        $avis = Mantis::orderBy('created_at', 'desc')->paginate(10); // 10 éléments par page

        // Récupérer les noms des catégories pour chaque avis
        foreach ($avis as $item) {
            $categoryResponse = $this->getCategoryById($item->category_id);
            $categoryData = json_decode($categoryResponse->getContent(), true);
            $item->category_name = $categoryData['category']['name'] ?? 'Catégorie inconnue';
        }

        return view('admin.avis.index', compact('avis'));
    }
}
