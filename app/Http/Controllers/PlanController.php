<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Stripe\Stripe;
use Stripe\Product;
use Stripe\Price;


class PlanController extends Controller
{
    public function show($plan, Request $request)
    {
        return view('recruter.payement-form', ['plan' => $plan]);
    }


    public function createProduct(Request $request)
    {
        // Valider les données
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
            'billing_period' => 'required|string|in:day,week,month,year',
        ]);


          // Récupérer la clé secrète Stripe depuis la configuration
          $stripeSecret = Config::get('stripe.secret');

          if (!$stripeSecret) {
              return response()->json([
                  'message' => 'La clé secrète Stripe n\'est pas configurée.',
              ], 500);
          }
  
          // Définir la clé API Stripe
          Stripe::setApiKey($stripeSecret);

        try {
            $product = Product::create([
                'name' => $validatedData['name'],
                'description' => $validatedData['description'] ?? '',
            ]);

            $price = Price::create([
                'unit_amount' => $validatedData['amount'] * 100,
                'currency' => $validatedData['currency'],
                'recurring' => ['interval' => $validatedData['billing_period']],
                'product' => $product->id,
            ]);

            return response()->json([
                'message' => 'Produit créé avec succès',
                'product' => $product,
                'price' => $price,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Erreur lors de la création du produit',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function listProduct()
    {
        $products = Product::all(); // Récupère tous les produits

        // Retourne la vue avec les produits
        return view('admin.stripe-product.index', compact('products'));
    }



}
