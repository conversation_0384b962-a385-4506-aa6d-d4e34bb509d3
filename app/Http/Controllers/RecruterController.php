<?php

namespace App\Http\Controllers;

use App\Helpers\FileHelper;
use App\Helpers\UsedUpFunction;
use App\Models\Address;
use App\Models\Civility;
use App\Models\Country;
use App\Models\FieldActivity;
use App\Models\File;
use App\Models\Formation;
use App\Models\Language;
use App\Models\Like;
use App\Models\Permit;
use App\Models\Phone;
use App\Models\Plan;
use App\Models\Profession;
use App\Models\ResidencePermit;
use App\Models\ResponsibilityCandidate;
use App\Models\Role;
use App\Models\Subscription;
use App\Models\TypeProfession;
use App\Models\User;
use App\Models\UserFieldActivity;
use App\Models\UserFormation;
use App\Models\UserPermit;
use App\Models\UserProfession;
use App\Models\UserSearch;
use App\Models\UserTypeProfession;
use Dotenv\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator as FacadesValidator;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Str;

class RecruterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function dashboardIndex()
    {

        $user = Auth::user();

        // Manually retrieve related data
        $civility = Civility::where('user_id', $user->id)->first();
        $phones = Phone::where('user_id', $user->id)->get();
        $countries = Country::getAllActive();

        $file_photo = File::where('id', $civility?->photo_file_id)?->first();

        if ($file_photo) {
            $civility->photo = $file_photo;
        } else {
            // $civility->photo = "photo";

        }

        // $civility->photo = File::where('id', $civility?->photo_file_id)?->first();

        return view('recruter.dashboard', [
            'user' => $user,
            'civility' => $civility,
            'countries' => $countries,
            'phones' => $phones
        ]);
    }

    /**
     * Update the candidate's profile.
     */
    public function profileUpdate(Request $request)
    {
        $user = Auth::user();

        $validation = FacadesValidator::make($request->all(), [
            // 'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            // 'g-recaptcha-response' => ['required', 'captcha'],
            'company_name' => 'required|string|max:255',
            'phone' => 'required|string|max:21',
            'website' => 'nullable|string|max:255',
            'country_of_residence' => 'required|string|exists:countries,id',
            'commune' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'latitude_longitude' => 'required|string|regex:/^(\-?\d+(\.\d+)?),\s*(\-?\d+(\.\d+)?)$/',
        ], [
            'g-recaptcha-response.required' => 'Veuillez confirmer que vous n\'êtes pas un robot.',
            'g-recaptcha-response.captcha' => 'Erreur de validation du captcha, veuillez réessayer.',
            'profile_picture.image' => 'Le fichier doit être une image.',
            'profile_picture.mimes' => 'Le fichier doit être une image de type: jpeg, png, jpg, gif.',
            'profile_picture.max' => 'Le fichier ne doit pas dépasser 2 Mo.',
            'company_name.required' => 'Le nom de l\'entreprise est requis.',
            'company_name.string' => 'Le nom de l\'entreprise doit être une chaîne de caractères.',
            'company_name.max' => 'Le nom de l\'entreprise ne doit pas dépasser 255 caractères.',
            'phone.required' => 'Le numéro de téléphone est requis.',
            'phone.string' => 'Le numéro de téléphone doit être une chaîne de caractères.',
            'phone.max' => 'Le numéro de téléphone ne doit pas dépasser 21 caractères.',
            'website' => 'Le site web est requis.',
            'website.string' => 'Le site web doit être une chaîne de caractères.',
            'website.max' => 'Le site web ne doit pas dépasser 255 caractères.',
            'address.required' => 'L\'adresse est requise.',
            'latitude_longitude.required' => 'Tu dois sélectionner un adresse proposée.',
            'country_of_residence.required' => 'Le pays de résidence est obligatoire.',
            'country_of_residence.exists' => 'Le pays sélectionné est invalide.',
            'commune.required' => 'La commune est obligatoire.',

        ]);

        // dd($request->country_of_residence);

        if ($validation->fails()) {
            return redirect()->back()->withErrors($validation)->withInput();
        }

        // Update phone
        $phone = Phone::where('user_id', $user->id)->first();
        if ($phone) {
            $phone->number = $request->phone;
            $phone->save();
        } else {
            $phone = new Phone();
            $phone->number = $request->phone;
            $phone->user_id = $user->id;
            $phone->save();
        }

        // Update address
        $address = Address::where('user_id', $user->id)->first();
        if (!$address) {
            $address = new Address();
            $address->user_id = $user->id;
        }

        $latitudeLongitude = explode(',', $request->latitude_longitude);
        $latitude = $latitudeLongitude[0] ?? 0;
        $longitude = $latitudeLongitude[1] ?? 0;

        $address->name = $request->address;
        $address->lat = $latitude;
        $address->log = $longitude;
        $address->save();

        // Update civility
        $civility = Civility::where('user_id', $user->id)->first();
        if (!$civility) {
            $civility = new Civility();
            $civility->user_id = $user->id;
        }
        $civility->company_name = $request->company_name;
        $civility->first_name = $request->first_name;
        $civility->country_of_residence_country_id = $request->country_of_residence;
        $civility->commune = $request->commune;
        $civility->website = $request->website;
        $civility->save();


        // Handle profile picture
        if ($request->hasFile('profile_picture')) {
            // remove old profile picture
            if ($civility->photo_file_id) {
                $oldPhoto = File::where('id', $civility->photo_file_id)->first();
                if ($oldPhoto) {
                    // remove old file
                    try {
                        unlink($oldPhoto->full_path);
                    } catch (\Throwable $th) {
                    }
                    $oldPhoto->delete();
                }
            }
            $profile = $request->file('profile_picture');
            $photoFile = FileHelper::store($profile, 'profile_picture', $user->id);
            $civility->photo_file_id = $photoFile->id;
            $civility->save();
        }


        return redirect()->route('recruter.dashboard')->with('status', 'Profil mis à jour avec succès.');
    }

    public function packageIndex()
    {
        $plans = Plan::all();
        // $decode=$plans[0]['description_html'];
        // dd('ici',$plans[0]['description_html'],$decode,json_decode($decode));

        $subscriptionTemp = Subscription::where('user_id', '=', auth()->user()->id)
            ->where('stripe_status', 'active')->first();

        if (isset($subscriptionTemp)) {
            $subscriptionIsActive = true;
            $planSlugActive = Plan::find($subscriptionTemp->plan_id)->slug;
        } else {
            $subscriptionIsActive = false;
            $planSlugActive = null;
            // dd('ici ouverture',$subscriptionIsActive);
        }

        return view('recruter.packages', compact('plans', 'subscriptionIsActive', 'planSlugActive'));
    }

    private function convertToArray($value)
    {
        if (is_array($value)) {
            return $value;
        }

        if ($value === "" || $value === null) {
            return [];
        }

        return is_string($value) ? explode(',', $value) : [$value];
    }

    private function saveUserSearch($searchParameters, $resultsCount)
    {
        $userId = auth()->id();

        UserSearch::updateOrCreate(
            [
                'user_id' => $userId
            ],
            [
                'search_parameters' => $searchParameters,
                'results_count' => $resultsCount
            ]
        );
    }

    public function candidateSelectedIndex(Request $request)
    {

        $searchHistory = UserSearch::where("user_id", auth()->id())->first();
        $searchParameters = $searchHistory ? $searchHistory->search_parameters : [];

        // Fonction helper pour gérer la priorité des paramètres
        $getParam = function ($key, $default = null) use ($request, $searchParameters) {
            // Si le paramètre est présent dans l'URL (même null), on l'utilise
            if ($request->has($key)) {
                return $request->query($key);
            }
            // Sinon, on utilise l'historique si disponible
            return $searchParameters[$key] ?? $default;
        };

        // Récupération des paramètres
        $sortedBy = $getParam('sortedBy', 'random');
        $activity_fields = $getParam('activity_fields', '');
        $desired_professions = $getParam('desired_professions', '');
        $availability = $getParam('availability', '');
        $category = $getParam('category');
        $location = $getParam('location');
        $distance = $getParam('distance');
        $residence = $getParam('residence');
        $criminal_record = $getParam('criminal_record');
        $contract_type = $getParam('contract_type', '');
        $work_rate = $getParam('work_rate');
        $native_language = $getParam('native_language', '');
        $fluent_languages = $getParam('fluent_languages', '');
        $intermediate_languages = $getParam('intermediate_languages', '');
        $basic_languages = $getParam('basic_languages', '');
        $selectedPermits = $getParam('permits', '');
        $country_of_residence = $getParam('country_of_residence');
        $commune = $getParam('commune');

        // Conversion des chaînes en tableaux avec gestion de null
        $convertToArray = function ($value) {
            if ($value === null) return [];
            return $value === "" ? [] : (is_array($value) ? $value : explode(',', $value));
        };

        $activity_fields = $convertToArray($activity_fields);
        $desired_professions = $convertToArray($desired_professions);
        $native_language = $convertToArray($native_language);
        $fluent_languages = $convertToArray($fluent_languages);
        $intermediate_languages = $convertToArray($intermediate_languages);
        $basic_languages = $convertToArray($basic_languages);
        $selectedPermits = $convertToArray($selectedPermits);
        $contract_type = $convertToArray($contract_type);

        // Récupération de la liste des candidats
        $candidates = $this->getListCandidate();

        // Exclure les candidats supprimés (sécurité supplémentaire)
        $candidates = $candidates->where('email', 'not like', '%_deleted_%');

        // Tri des candidats
        if ($sortedBy == 'created_at_asc') {
            $candidates = $candidates->orderBy('created_at', 'asc');
        } elseif ($sortedBy == 'created_at_desc') {
            $candidates = $candidates->orderBy('created_at', 'desc');
        } elseif ($sortedBy == 'liked') {
            $likesCandidateIds = Like::where('user_id', auth()->user()->id)->get()->pluck('candidate_id');
            $candidates = $candidates->whereIn('id', $likesCandidateIds);
        }

        $userIds = null;

        // Filtrage par domaine d'activité
        if (!empty($activity_fields)) {
            $userIds = UserFieldActivity::whereIn('field_activity_id', $activity_fields)->pluck('user_id');
        }

        // Filtrage par profession recherchée
        if (!empty($desired_professions)) {
            $query = UserProfession::whereIn('profession_id', $desired_professions);

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->pluck('user_id');
        }

        // Filtrage par catégorie
        if ($category) {
            $query = Civility::where('category', $category);

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->pluck('user_id');
        }

        // Filtrage par disponibilité
        if ($availability) {
            $query = Civility::where('responsibility_candidate_id', $availability);

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->pluck('user_id');
        }

        // Filtrage par localisation
        if ($location) {
            $query = Civility::where('commune', $location);

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->pluck('user_id');
        }

        // Filtrage par pays de résidence
        if ($country_of_residence) {
            $query = Civility::where('country_of_residence_country_id', $country_of_residence);

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->pluck('user_id');
        }

        // Filtrage par commune/canton
        if ($commune) {
            $query = Civility::where('commune', $commune);

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->pluck('user_id');
        }

        // Filtrage par distance (cas spécial car nécessite un traitement différent)
        if ($distance) {
            $userAddress = auth()->user()->getAddress();
            if (isset($userAddress?->id)) {
                $centerLat = $userAddress->lat;
                $centerLng = $userAddress->log;
                $_users = [];

                $candidatesToFilter = $candidates;
                if ($userIds !== null) {
                    $candidatesToFilter = $candidatesToFilter->whereIn('id', $userIds);
                }

                foreach ($candidatesToFilter->get() as $_user) {
                    $_address = $_user->getAddress();
                    if (isset($_address)) {
                        $_users[] = [
                            'id' => $_user->id,
                            'lat' => $_address->lat,
                            'log' => $_address->log,
                        ];
                    }
                }

                $filteredUsers = UsedUpFunction::filterUsersByRadius($_users, $centerLat, $centerLng, $distance);
                $filteredUserIds = array_column($filteredUsers, 'id');

                if ($userIds !== null) {
                    $userIds = array_intersect($userIds->toArray(), $filteredUserIds);
                } else {
                    $userIds = collect($filteredUserIds);
                }
            }
        }

        // Filtrage par résidence
        if ($residence) {
            $query = Civility::where('residence_permit_id', $residence);

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->pluck('user_id');
        }

        // Filtrage par casier judiciaire
        if ($criminal_record) {
            $query = Civility::where('criminal_record', $criminal_record);

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->pluck('user_id');
        }

        // Filtrage par type de contrat
        if (!empty($contract_type)) {
            $query = Civility::whereIn('contract_type', $contract_type);

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->pluck('user_id');
        }

        // Filtrage par taux d'activité
        if ($work_rate) {
            $query = Civility::where('work_rate', $work_rate);

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->pluck('user_id');
        }

        // Filtrage par langue maternelle
        if (!empty($native_language)) {
            $query = Civility::query();

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->get()->filter(function ($civility) use ($native_language) {
                if ($civility->native_language) {
                    return count(array_intersect($civility->native_language, $native_language)) > 0;
                }
                return false;
            })->pluck('user_id');
        }

        // Filtrage par langues parlées couramment
        if (!empty($fluent_languages)) {
            $query = Civility::query();

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->get()->filter(function ($civility) use ($fluent_languages) {
                if ($civility->fluent_languages) {
                    return count(array_intersect($civility->fluent_languages, $fluent_languages)) > 0;
                }
                return false;
            })->pluck('user_id');
        }

        // Filtrage par langues avec notion intermédiaire
        if (!empty($intermediate_languages)) {
            $query = Civility::query();

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->get()->filter(function ($civility) use ($intermediate_languages) {
                if ($civility->intermediate_languages) {
                    return count(array_intersect($civility->intermediate_languages, $intermediate_languages)) > 0;
                }
                return false;
            })->pluck('user_id');
        }

        // Filtrage par langues avec notion de base
        if (!empty($basic_languages)) {
            $query = Civility::query();

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->get()->filter(function ($civility) use ($basic_languages) {
                if ($civility->basic_languages) {
                    return count(array_intersect($civility->basic_languages, $basic_languages)) > 0;
                }
                return false;
            })->pluck('user_id');
        }

        // Filtrage par permis
        if (!empty($selectedPermits)) {
            $query = UserPermit::whereIn('permit_id', $selectedPermits);

            if ($userIds !== null) {
                $query->whereIn('user_id', $userIds);
            }

            $userIds = $query->pluck('user_id');
        }

        // Appliquer les filtres
        if ($userIds !== null) {
            $candidates = $candidates->whereIn('id', $userIds);
        }

        // Pour random : on récupère, on mélange, puis on pagine manuellement
        if ($sortedBy == 'random') {
            $allCandidates = $candidates->get()->shuffle();
            $perPage = 10;
            $page = request()->get('page', 1);
            $paginatedCandidates = new \Illuminate\Pagination\LengthAwarePaginator(
                $allCandidates->forPage($page, $perPage),
                $allCandidates->count(),
                $perPage,
                $page,
                ['path' => request()->url(), 'query' => request()->query()]
            );
        } else {
            $paginatedCandidates = $candidates->paginate(10);
        }

        // Préparer les paramètres de recherche pour la sauvegarde
        $searchParameters = [
            'activity_fields' => $activity_fields,
            'desired_professions' => $desired_professions,
            'category' => $category,
            'location' => $location,
            'distance' => $distance,
            'availability' => $availability,
            'residence' => $residence,
            'criminal_record' => $criminal_record,
            'contract_type' => $contract_type,
            'work_rate' => $work_rate,
            'native_language' => $native_language,
            'fluent_languages' => $fluent_languages,
            'intermediate_languages' => $intermediate_languages,
            'basic_languages' => $basic_languages,
            'permits' => $selectedPermits,
            'country_of_residence' => $country_of_residence,
            'commune' => $commune,
            // 'sortedBy' => $sortedBy
        ];

        // Récupération des données pour les filtres
        $fieldActivities = FieldActivity::getAllActive();
        $permits = Permit::getAllActive();
        $residencePermits = ResidencePermit::getAllActive();
        $countries = Country::getAllActive();
        $typeProfessions = TypeProfession::getAllActive();
        $professions = Profession::getAllActive();
        $responsibilities = ResponsibilityCandidate::getAllActive();
        $languages = Language::getAllActive();
        $formations = Formation::getAllActive();

        $connectedRecruiter = $this->getConnectRecruiter();
        $recruiterAddress = $connectedRecruiter->getAddress();

        // Calcul des distances pour chaque candidat de la page actuelle
        $candidatesWithDistance = [];
        foreach ($paginatedCandidates->items() as $candidate) {
            // Sauter les candidats supprimés (sécurité supplémentaire)
            if (Str::contains($candidate->email, '_deleted_')) {
                continue;
            }
            $candidateAddress = $candidate->getAddress();
            $distanceBetween = isset($recruiterAddress) && isset($candidateAddress)
                ? $this->haversineGreatCircleDistance(
                    $recruiterAddress->lat,
                    $recruiterAddress->log,
                    $candidateAddress->lat,
                    $candidateAddress->log
                )
                : null;

            $candidatesWithDistance[] = [
                'candidate' => $candidate,
                'distance' => $distanceBetween,
            ];
        }

        // Sauvegarder la recherche
        $this->saveUserSearch($searchParameters, count($candidatesWithDistance));
        $searchHistory = UserSearch::where("user_id", auth()->id())->first();

        // Récupérer les paramètres depuis l'historique si disponible
        $searchParams = $searchHistory ? $searchHistory->search_parameters : [];


        // Retourner la vue avec les données
        $viewData = [
            'sortedBy' => $sortedBy,
            'candidates' => $paginatedCandidates,
            'candidatesWithDistance' => $candidatesWithDistance,
            'fieldActivities' => $fieldActivities,
            'professions' => $professions,
            'responsibilities' => $responsibilities,
            'residencePermits' => $residencePermits,
            'languages' => $languages,
            'countries' => $countries,
            'typeProfessions' => $typeProfessions,
            'formations' => $formations,
            'permits' => $permits,
            'activity_fields' => $searchParams["activity_fields"],
            'desired_professions' => $searchParams["desired_professions"] ?? [],
            'category' => $category,
            'location' => $location,
            'distance' => $distance,
            'residence' => $residence,
            'criminal_record' => $criminal_record,
            'contract_type' => $contract_type,
            'work_rate' => $work_rate,
            'availability' => $availability,
            'native_language' => $native_language,
            'fluent_languages' => $fluent_languages,
            'intermediate_languages' => $intermediate_languages,
            'basic_languages' => $basic_languages,
            'selectedPermits' => $selectedPermits,
            'country_of_residence' => $country_of_residence,
            'commune' => $commune,
        ];
        return view('recruter.candidate-selected', $viewData);
    }



    public static function haversineGreatCircleDistance($latitudeFrom, $longitudeFrom, $latitudeTo, $longitudeTo, $earthRadius = 6371)
    {
        // convert from degrees to radians
        $latFrom = deg2rad($latitudeFrom);
        $lonFrom = deg2rad($longitudeFrom);
        $latTo = deg2rad($latitudeTo);
        $lonTo = deg2rad($longitudeTo);

        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;

        $angle = 2 * asin(sqrt(pow(sin($latDelta / 2), 2) + cos($latFrom) * cos($latTo) * pow(sin($lonDelta / 2), 2)));
        return $angle * $earthRadius;
    }

    public function candidateSelectedShow($id)
    {
        $candidate = User::where('id', $id)
            ->where('role_id', Role::where('name', 'candidate')->first()->id)
            ->where('email', 'not like', '%_deleted_%')
            ->first();

        if (!$candidate) {
            return abort(404);
        }

        $civility = Civility::where('user_id', $candidate->id)->first();

        // Récupérer les fichiers (CV, certificats de travail, diplômes)
        $civility->cv = File::where('id', $civility->cv_file_id)->first();
        // Correction : utiliser $civility->user_id pour récupérer les certificats
        $civility->certificate_files = File::where('user_id', $civility->user_id)
            ->where('usage', 'certificate')
            ->get();

            // dd($candidate);
        $phones = Phone::where('user_id', $candidate->id)->get();
        $userFieldActivities = UserFieldActivity::where('user_id', $candidate->id)->pluck('field_activity_id')->toArray();
        $userProfessions = UserProfession::where('user_id', $candidate->id)->pluck('profession_id')->toArray();
        $userTypeProfessions = UserTypeProfession::where('user_id', $candidate->id)->pluck('type_profession_id')->toArray();
        $userFormations = UserFormation::where('user_id', $candidate->id)->pluck('formation_id')->toArray();
        $userPermits = UserPermit::where('user_id', $candidate->id)->pluck('permit_id')->toArray();

        $fieldActivities = FieldActivity::whereIn('id', $userFieldActivities)->get();
        $professions = Profession::whereIn('id', $userProfessions)->get();
        $formations = Formation::whereIn('id', $userFormations)->get();
        $permits = Permit::whereIn('id', $userPermits)->get();
        $typeProfessions = TypeProfession::whereIn('id', $userTypeProfessions)->get();

        $countryOfResidence = Country::where('id', $civility->country_of_residence_country_id)->first();
        $residencePermit = ResidencePermit::where('id', $civility->residence_permit_id)->first();
        $responsibilityCandidate = ResponsibilityCandidate::where('id', $civility->responsibility_candidate_id)->first();

        return view('recruter.candidate-selected-show', [
            'candidate' => $candidate,
            'phones' => $phones,
            'fieldActivities' => $fieldActivities,
            'professions' => $professions,
            'typeProfessions' => $typeProfessions,
            'formations' => $formations,
            'responsibilityCandidate' => $responsibilityCandidate,
            'residencePermit' => $residencePermit,
            'countryOfResidence' => $countryOfResidence,
            'permits' => $permits,
            'civility' => $civility, // Ajouter civility pour accéder aux fichiers date_of_birth
        ]);
    }

    public function candidateLiked(Request $request, $candidate_id)
    {
        $validation  = FacadesValidator::make([
            'candidate_id' => $candidate_id
        ], [
            'candidate_id' => 'required|exists:users,id'
        ]);

        if ($validation->fails()) {
            return response()->json([
                'message' => 'Invalid data',
                'errors' => $validation->errors()
            ], 400);
        }

        $isLiked = Like::where('user_id', auth()->user()->id)
            ->where('candidate_id', $candidate_id)
            ?->exists();

        if ($isLiked) {
            Like::where('user_id', auth()->user()->id)
                ->where('candidate_id', $candidate_id)
                ->delete();

            return response()->json([
                'message' => 'Candidate unliked',
                'liked' => false
            ]);
        } else {

            $like = new Like();
            $like->user_id = auth()->user()->id;
            $like->candidate_id = $candidate_id;
            $like->save();

            return response()->json([
                'message' => 'Candidate liked',
                'liked' => true
            ]);
        }
    }

    public function candidatePDF(Request $request, $candidate_id)
    {
        try {
            // Vérification de l'existence du candidat
            $candidate = User::with(['civility', 'phones'])
                ->where('id', $candidate_id)
                ->where('role_id', Role::where('name', 'candidate')->first()->id)
                ->firstOrFail();
    
            // Chargement des données de référence en une seule requête
            $references = [
                'permits' => Permit::getAllActive(),
                'residencePermits' => ResidencePermit::getAllActive(),
                'countries' => Country::getAllActive(),
                'fieldActivities' => FieldActivity::getAllActive(),
                'typeProfessions' => TypeProfession::getAllActive(),
                'professions' => Profession::getAllActive(),
                'responsibilities' => ResponsibilityCandidate::getAllActive(),
                'languages' => Language::getAllActive(),
                'formations' => Formation::getAllActive(),
            ];
    
            // Récupération des données spécifiques du candidat
            $civility = $candidate->civility ?? new Civility();
            $phones = $candidate->phones;
    
            // Chargement des relations avec eager loading
            $userRelations = [
                'userFieldActivities' => UserFieldActivity::where('user_id', $candidate->id)->pluck('field_activity_id'),
                'userProfessions' => UserProfession::where('user_id', $candidate->id)->pluck('profession_id'),
                'userFormations' => UserFormation::where('user_id', $candidate->id)->pluck('formation_id'),
                'userPermits' => UserPermit::where('user_id', $candidate->id)->pluck('permit_id'),
            ];
    
            $addressName = Address::getNameByUserId($candidate->id);
    
            // Gestion des fichiers
            $fileTypes = ['photo', 'cv', 'work_certificates', 'study_certificates'];
            foreach ($fileTypes as $type) {
                $fileId = $civility->{"{$type}_file_id"};
                $civility->$type = $fileId ? File::find($fileId) : null;
            }
    
            // Préparation des images en base64 avec gestion d'erreur
            $images = [
                'logo' => $this->prepareImage(public_path('images/logo.png')),
                'profile' => $civility->photo && $civility->photo->path 
                    ? $this->prepareImage(public_path($civility->photo->path), public_path('images/user.png'))
                    : $this->prepareImage(public_path('images/user.png'))
            ];
    
            // Récupération des données complémentaires
            $additionalData = [
                'countryOfResidence' => Country::find($civility->country_of_residence_country_id),
                'residencePermit' => ResidencePermit::find($civility->residence_permit_id),
                'typeProfession' => TypeProfession::find($civility->type_profession_id),
                'responsibilityCandidate' => ResponsibilityCandidate::find($civility->responsibility_candidate_id),
            ];
    
            // Gestion des langues
            $languageLevels = [
                'nativeLanguages' => $civility->native_language ?? [],
                'fluentLanguages' => $civility->fluent_languages ?? [],
                'intermediateLanguages' => $civility->intermediate_languages ?? [],
                'basicLanguages' => $civility->basic_languages ?? [],
            ];
    
            $languages = [];
            foreach ($languageLevels as $level => $ids) {
                $languages[$level] = Language::whereIn('id', $ids)->get();
            }
    
            // Préparation des données pour la vue
            $pdfData = [
                'candidate' => $candidate,
                'civility' => $civility,
                'phones' => $phones,
                'fieldActivities' => $references['fieldActivities']->whereIn('id', $userRelations['userFieldActivities']),
                'professions' => $references['professions']->whereIn('id', $userRelations['userProfessions']),
                'formations' => $references['formations']->whereIn('id', $userRelations['userFormations']),
                'permits' => $references['permits']->whereIn('id', $userRelations['userPermits']),
                'addressName' => $addressName,
                'logoBase64' => $images['logo'],
                'photoBase64' => $images['profile'],
            ] + $additionalData + $languages;
    
            // Configuration de DomPDF
            $pdfOptions = [
                'isRemoteEnabled' => true,
                'enable_html5_parser' => true,
                'defaultFont' => 'dejavu sans',
                'isPhpEnabled' => true,
                'dpi' => 150,
                'fontHeightRatio' => 0.9,
                'debugKeepTemp' => true,
                'tempDir' => storage_path('app/dompdf/temp'),
            ];
    
            // Génération du PDF
            $pdf = \PDF::loadView('recruter.candidate-pdf', $pdfData)
                ->setOptions($pdfOptions);
    
            return $pdf->download("profil-candidat-{$candidate_id}.pdf");
    
        } catch (\Exception $e) {
            \Log::error("PDF Generation Error: " . $e->getMessage());
            return back()->withError('Une erreur est survenue lors de la génération du PDF.');
        }
    }
    
    /**
     * Préparation d'une image en base64 avec fallback
     */
    protected function prepareImage(string $path, ?string $fallbackPath = null): ?string
    {
        try {
            if (file_exists($path)) {
                $mimeType = mime_content_type($path);
                if (in_array($mimeType, ['image/png', 'image/jpeg', 'image/gif'])) {
                    return 'data:' . $mimeType . ';base64,' . base64_encode(file_get_contents($path));
                }
            }
            
            if ($fallbackPath && file_exists($fallbackPath)) {
                $mimeType = mime_content_type($fallbackPath);
                if (in_array($mimeType, ['image/png', 'image/jpeg', 'image/gif'])) {
                    return 'data:' . $mimeType . ';base64,' . base64_encode(file_get_contents($fallbackPath));
                }
            }
            
            return null;
        } catch (\Exception $e) {
            \Log::error("Image preparation error: " . $e->getMessage());
            return null;
        }
    }

    public function getListCandidate()
    {
        $role = Role::where('name', 'candidate')->first();
        $candidates = User::where('role_id', $role->id)
            ->where('email_verified_at', '!=', null)
            ->where('email', 'not like', '%_deleted_%');

        $userIdsVisibility = Civility::where('visibility', '1')->pluck('user_id');
        $candidates = $candidates->whereIn('id', $userIdsVisibility);

        return $candidates;
    }

    public function getConnectRecruiter()
    {
        $user = Auth::user();
        $role = Role::where('name', 'recruter')->first();
        $recruter = User::where('role_id', $role->id)
            ->where('_id', $user->id)->first();

        return $recruter;
    }

    public function listCandidate(Request $request)
    {
        $candidates = $this->getListCandidate();
        return response()->json([
            'candidates' => $candidates->get()
        ]);
    }

    /**
     * Reset the user's search history
     */
    public function resetSearch()
    {
        UserSearch::where('user_id', auth()->id())->delete();

        return redirect()->route('recruter.candidate-selected')
            ->with('status', 'La recherche a été réinitialisée avec succès.');
    }
}
