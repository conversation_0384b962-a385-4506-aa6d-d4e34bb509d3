<?php

namespace App\Http\Controllers;

use App\Models\ConfigGlobalApp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PublicationDateController extends Controller
{
    public function index()
    {
        // Récupérer toutes les configurations
        $configs = ConfigGlobalApp::all();

        // Passer les données à la vue
        return view('admin.publication-date.index', compact('configs'));
    }

    // public function store(Request $request)
    // {
    //     // Validation des données
    //     $validator = Validator::make($request->all(), [
    //         'date' => 'required|date', // La date doit être au format valide
    //         'months' => 'required|integer|min:30|max:180', // Validation pour le nombre de jours
    //     ]);

    //     // Vérifier si une date de publication existe déjà
    //     $existingDatePublish = ConfigGlobalApp::where('name', 'date_publish')->exists();
    //     if ($existingDatePublish) {
    //         return redirect()->back()
    //             ->with('error', 'Vous ne pouvez pas ajouter deux dates de publication.')
    //             ->withInput();
    //     }

    //     // Si la validation échoue, rediriger avec les erreurs
    //     if ($validator->fails()) {
    //         return redirect()->back()
    //             ->withErrors($validator)
    //             ->withInput();
    //     }

    //     // Récupérer la date depuis la requête
    //     $date = $request->input('date');

    //     // Convertir la date en timestamp en millisecondes
    //     $timestamp = strtotime($date) * 1000; // Convertir en millisecondes
    //     $mongoDate = new \MongoDB\BSON\UTCDateTime($timestamp); // Créer un objet UTCDateTime

    //     // Ajouter la première ligne : date_publish
    //     ConfigGlobalApp::create([
    //         'name' => 'date_publish',
    //         'value' => $mongoDate, // Stocker en tant que timestamp MongoDB
    //         'comment' => 'value is stored as MongoDB timestamp (UTCDateTime)',
    //     ]);

    //     // Récupérer la valeur du champ "months" (nombre de jours)
    //     $days = $request->input('months');

    //     // Ajouter la deuxième ligne : day_free_after_publish
    //     ConfigGlobalApp::create([
    //         'name' => 'day_free_after_publish',
    //         'value' => (int) $days, // Caster en entier (Int32)
    //         'comment' => 'value must be in string, need cast if put/get value INT',
    //     ]);

    //     // Rediriger avec un message de succès
    //     return redirect()->route('stripe-webhook.date-publication')
    //         ->with('success', 'Configuration enregistrée avec succès.');
    // }

    // Méthode pour afficher le formulaire d'édition
    public function edit($name)
    {
        // Récupérer la configuration à modifier en fonction du nom
        $datePublish = ConfigGlobalApp::where('name', $name)->firstOrFail();

        // Convertir la date en format lisible
        $date = $datePublish->value->toDateTime()->format('Y-m-d H:i');

        // Récupérer la durée en jours
        $dayFreeAfterPublish = ConfigGlobalApp::where('name', 'day_free_after_publish')->first();
        $days = $dayFreeAfterPublish->value;

        // Convertir les jours en mois pour l'affichage dans le select
        $months = $days / 30;

        // Retourner les données au format JSON
        return response()->json([
            'datePublish' => $datePublish,
            'date' => $date,
            'months' => $months,
        ]);
    }

    // Méthode pour mettre à jour les informations
    public function update(Request $request)
    {
        $validated = $request->validate([
            'date' => 'nullable|date',
            'enddate' => 'nullable|date',
            'display_bandeau' => 'nullable|boolean',
        ]);

        // Mettre à jour la date de publication
        if ($request->filled('date')) {
            $timestamp = strtotime($request->input('date')) * 1000;
            ConfigGlobalApp::where('name', 'date_publish')->update([
                'value' => new \MongoDB\BSON\UTCDateTime($timestamp)
            ]);
        }

        // Mettre à jour la date de fin
        if ($request->filled('enddate')) {
            $timestamp = strtotime($request->input('enddate')) * 1000;
            ConfigGlobalApp::where('name', 'day_free_after_publish')->update([
                'value' => new \MongoDB\BSON\UTCDateTime($timestamp)
            ]);
        }


        // Mettre à jour l'affichage du bandeau

        if($request->input('display_bandeau')=="0"){
            $displayBandeauValue = false;
        }else{
            $displayBandeauValue = true;
        }
        // $displayBandeauValue = $request->has('display_bandeau') ? true : false;
        ConfigGlobalApp::where('name', 'display_bandeau')->update([
            'value' => $displayBandeauValue
        ]);

        return redirect()->route('stripe-webhook.date-publication')
            ->with('success', 'Configuration mise à jour avec succès.');
    }

    // public function destroy()
    // {
    //     // Supprimer toutes les entrées de la table
    //     ConfigGlobalApp::query()->delete();

    //     // Rediriger avec un message de succès
    //     return redirect()->route('stripe-webhook.date-publication')
    //         ->with('success', 'Toutes les dates de publication ont été supprimées avec succès.');
    // }
}
