<?php

namespace App\Http\Controllers;

use App\Models\ConfigGlobalApp;
use Illuminate\Http\Request;

class ParametreSmtpController extends Controller
{
    public function index()
    {
        // Récupérer la configuration de messagerie
        $mailConfig = ConfigGlobalApp::where('name', 'mail_config')->first();

        // Décoder la valeur JSON
        if ($mailConfig) {
            $mailConfig->value = json_decode($mailConfig->value, true);
        }

        // Passer les données à la vue
        return view('admin.parametre-smtp.index', compact('mailConfig'));
    }

    // Méthode pour mettre à jour la configuration SMTP
    public function update(Request $request)
    {
        // Valider les données du formulaire
        $request->validate([
            'key' => 'required|string', // La clé à modifier (ex: MAIL_HOST)
            'value' => 'required|string', // La nouvelle valeur
        ]);

        // Récupérer la configuration SMTP
        $mailConfig = ConfigGlobalApp::where('name', 'mail_config')->first();

        if (!$mailConfig) {
            return redirect()->back()->with('error', 'Configuration SMTP introuvable.');
        }

        // Décoder la valeur JSON
        $configData = json_decode($mailConfig->value, true);

        // Mettre à jour la valeur spécifique
        $configData[$request->key] = $request->value;

        // Encoder en JSON et sauvegarder
        $mailConfig->value = json_encode($configData);
        $mailConfig->save();

        // Rediriger avec un message de succès
        return redirect()->back()->with('success', 'Configuration SMTP mise à jour avec succès.');
    }
}
