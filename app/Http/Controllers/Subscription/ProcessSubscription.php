<?php

namespace App\Http\Controllers\Subscription;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Address;
use App\Models\Card;
use App\Models\Civility;
use App\Models\ConfigGlobalApp;
use App\Models\Plan;
use App\Models\Subscription;
use Exception;
use Illuminate\Support\Facades\Config;
use Laravel\Cashier\Exceptions\IncompletePayment;

use Stripe\StripeClient;

class ProcessSubscription extends Controller
{
    public function getSetupIntent(Request $request)
    {
        // try {
            $addresseUser = Address::where("user_id", auth()->user()->id)->first();
            $civilityUser = Civility::where("user_id", auth()->user()->id)->first();
            $nameUser = '';
            if (isset($civilityUser->first_name))
                $nameUser = $nameUser . $civilityUser->first_name;
            if (isset($civilityUser->last_name))
                $nameUser = $nameUser . ' ' . $civilityUser->last_name;

            // dd('addresseUser',$addresseUser);
            if (isset($addresseUser)) {
                $options = [
                    'email' => auth()->user()->email,
                    'preferred_locales' => ['fr-FR'],
                    'name' => $nameUser,
                    'address' => [
                        'city' => $addresseUser->city,
                        'country' => $addresseUser->country,
                        'line1' => $addresseUser->address,
                        'state' => $addresseUser->state,
                        'postal_code' => $addresseUser->zip_code,
                    ],
                ];
                // Check if the customer is already created in stripe
                // If not, create it
               
                if (!auth()->user()->hasStripeId())
                    $customerStripeCreate = auth()->user()->createAsStripeCustomer($options);
                else
                    // Update
                    $customerStripeCreate = auth()->user()->updateStripeCustomer($options);


                $intent = auth()->user()->createSetupIntent();
                return response()->json([
                    'client_secret' => $intent->client_secret,
                ], 200);
            } else {
                return response()->json([
                    'result' => 'Addresse not exist for payement',
                ], 500);
            }
        // } catch (\Throwable $th) {
        //     dd($th);
        //     return response()->json([], 500);
        // }
    }
    public function updateAddresse(Request $request)
    {

        try {
            $addresseUser = Address::where('user_id', auth()->user()->id)->first();
            if (isset($addresseUser)) {
                $addresseUser->update([
                    'city' => $request->city,
                    'country' => $request->country,
                    'line1' => $request->line1,
                    'state' => $request->state,
                    'postal_code' => $request->postal_code,
                ]);
                return response()->json([
                    'result' => 'update',
                ], 200);
            } else {
                // Create one line for addresseUser
                // 'city','country','line1','state','postal_code'
                $addresseUser = new Address();
                $addresseUser->user_id = auth()->user()->id;
                $addresseUser->city = $request->city;
                $addresseUser->country = $request->country;
                $addresseUser->line1 = $request->line1;
                $addresseUser->state = $request->state;
                $addresseUser->postal_code = $request->postal_code;
                $addresseUser->save();
                return response()->json([
                    'result' => 'created',
                ], 200);
            }
        } catch (\Throwable $th) {
            // dd($th->getMessage());
            return response()->json(['result' => $th->getMessage()], 500);
        }
    }

    public function initiateSubscription(Request $request)
    {

        // Get default_tax_rates value id by database with create config global apps table
        $configGlobal = ConfigGlobalApp::where('name', 'default_tax_rates')->first();
        if (isset($configGlobal))
            $default_tax_rates = $configGlobal->value;
        else
            $default_tax_rates = 'txr_1QlCZFFKK6JoGdxmT2VBHx0S';
        // Set static tax_id_rate because, we don't found in database local variable


        if ($request->exist_card) {
            try {
                $priceTemp = Plan::where('slug', $request->planSlug)->first();

                // Récupérer la clé Stripe depuis la configuration
                $stripeSecretKey = Config::get('stripe.secret');

                if (empty($stripeSecretKey)) {
                    return redirect()->back()->with('error', 'La clé API Stripe n\'est pas configurée.');
                }

                // Initialiser Stripe avec la clé secrète
                $stripe = new StripeClient($stripeSecretKey);
                $customerId = auth()->user()->stripe_id;
                $stripe->paymentMethods->attach($request->payment_method, ['customer' => $customerId]);
                // Set the attached payment method as the default for the customer
                $stripe->customers->update(
                    $customerId,
                    ['invoice_settings' => ['default_payment_method' => $request->payment_method]]
                );

                // Check on the first if the client has a line subscription active(or not) actual.
                // If true, update subscription api strie
                // If false create new subscription
                $subscriptionUser = Subscription::where('user_id', auth()->user()->id)->first();

                if (isset($subscriptionUser)) {
                    $subscriptionData =  $stripe->subscriptionItems->all([
                        'limit' => 1,
                        'subscription' => $subscriptionUser->stripe_id,
                    ]);

                    $subscriptionItem = $subscriptionData->data[0];
                    // We have on product, and take it

                    $subscription = $stripe->subscriptions->update(
                        $subscriptionUser->stripe_id,
                        // subIdBefore
                        [
                            'items' => [
                                [
                                    'id' => $subscriptionItem->id,
                                    // 'price' => $stripeProductId['default_price'],
                                    'deleted' => true,
                                ],
                                ['price' => $priceTemp->stripe_price_id],
                            ],

                            'metadata' => [
                                'customer_id' => $customerId,
                                'stripe_price' => $priceTemp->stripe_price_id,
                            ],

                            'default_tax_rates' => [
                                $default_tax_rates, // Remplacez par l'ID du tax rate créé
                            ],

                        ]
                    );
                } else {
                    // Now create the subscription with the attached payment method
                    $subscription = $stripe->subscriptions->create([
                        'customer' => $customerId,
                        'items' => [
                            [
                                'price' => $priceTemp->stripe_price_id,
                            ],
                        ],
                        'automatic_tax' => [
                            'enabled' => false,
                        ],

                        'default_tax_rates' => [
                            $default_tax_rates, // Remplacez par l'ID du tax rate créé
                        ],
                        // txr_1QlCZFFKK6JoGdxmT2VBHx0S

                        'metadata' => [
                            'customer_id' => $customerId,
                            'stripe_price' => $priceTemp->stripe_price_id,
                        ],

                    ]);
                }


                // subscription.latest_invoice.payment_intent
                $invoice = $stripe->invoices->retrieve($subscription->latest_invoice, []);
                // dd('ici invoice paymentIntent',$subscription->latest_invoice,$invoice->payment_intent,$invoice);
                if (isset($invoice->payment_intent))
                    $paymentIntent = $stripe->paymentIntents->retrieve($invoice->payment_intent, []);
                else
                    $paymentIntent = null;

                // NOTEZ BIEN
                // paymentIntent is null when is for updating subscription, in this case, test invoice->paid if true



                if (isset($paymentIntent) && $paymentIntent->status == 'requires_action') {
                    // Manage 3DS
                    return response()->json([
                        'status' => 'requires_action',
                        'client_secret' => $paymentIntent->client_secret,
                        'subscription_id' => $subscription->id,
                        'code' => 200
                    ], 200);
                } else if ($invoice->paid == true) {
                    return response()->json([
                        'status' => 'success',
                        'subscription_id' => $subscription->id,
                    ], 200);
                }
            } catch (\Throwable $th) {
                dd('ici error, th', $th);
                return response()->json([
                    'status' => 'error',
                    'result' => 'exist_card',
                ], 500);
            }
        } else {
            try {

                $priceTemp = Plan::where('slug', $request->planSlug)->first();
                // dd('ici', $request->payment_method,$priceTemp->name, $priceTemp->stripe_price_id);
                // dd("priceTemp",$priceTemp,auth()->user()->id,$request->payment_method,auth()->user()->stripe_id);

                // Récupérer la clé Stripe depuis la configuration
                $stripeSecretKey = Config::get('stripe.secret');

                if (empty($stripeSecretKey)) {
                    return redirect()->back()->with('error', 'La clé API Stripe n\'est pas configurée.');
                }

                // Initialiser Stripe avec la clé secrète
                $stripe = new StripeClient($stripeSecretKey);
                $customerId = auth()->user()->stripe_id;
                $stripe->paymentMethods->attach($request->payment_method, ['customer' => $customerId]);
                // Set the attached payment method as the default for the customer
                $stripe->customers->update(
                    $customerId,
                    ['invoice_settings' => ['default_payment_method' => $request->payment_method]]
                );


                // Check on the first if the client has a line subscription active(or not) actual.
                // If true, update subscription api strie
                // If false create new subscription
                $subscriptionUser = Subscription::where('user_id', auth()->user()->id)->first();


                if (isset($subscriptionUser)) {
                    $subscriptionData =  $stripe->subscriptionItems->all([
                        'limit' => 1,
                        'subscription' => $subscriptionUser->stripe_id,
                    ]);

                    $subscriptionItem = $subscriptionData->data[0];
                    // We have on product, and take it


                    $subscription = $stripe->subscriptions->update(
                        $subscriptionUser->stripe_id,
                        // subIdBefore
                        [
                            'items' => [
                                [
                                    'id' => $subscriptionItem->id,
                                    // 'price' => $stripeProductId['default_price'],
                                    'deleted' => true,
                                ],
                                ['price' => $priceTemp->stripe_price_id],
                            ],

                            'default_tax_rates' => [
                                $default_tax_rates, // Remplacez par l'ID du tax rate créé
                            ],

                            'metadata' => [
                                'customer_id' => $customerId,
                                'stripe_price' => $priceTemp->stripe_price_id,
                            ],

                        ]
                    );
                } else {
                    // Now create the subscription with the attached payment method
                    $subscription = $stripe->subscriptions->create([
                        'customer' => $customerId,
                        'items' => [
                            [
                                'price' => $priceTemp->stripe_price_id,
                            ],
                        ],
                        'automatic_tax' => [
                            'enabled' => false,
                        ],

                        // Before it's true, because, we depend of the a customer adress to calculate taxe.
                        // Now, it just static 8.1% defined in txr_1QlCZFFKK6JoGdxmT2VBHx0S

                        'default_tax_rates' => [
                            $default_tax_rates, // Remplacez par l'ID du tax rate créé
                        ],

                        'metadata' => [
                            'customer_id' => $customerId,
                            'stripe_price' => $priceTemp->stripe_price_id,
                        ],

                    ]);
                }

                // subscription.latest_invoice.payment_intent
                $invoice = $stripe->invoices->retrieve($subscription->latest_invoice, []);
                // dd('ici invoice paymentIntent',$subscription->latest_invoice,$invoice->payment_intent,$invoice);
                if (isset($invoice->payment_intent))
                    $paymentIntent = $stripe->paymentIntents->retrieve($invoice->payment_intent, []);
                else
                    $paymentIntent = null;

                // NOTEZ BIEN
                // paymentIntent is null when is for updating subscription, in this case, test invoice->paid if true



                if (isset($paymentIntent) && $paymentIntent->status == 'requires_action') {
                    // Manage 3DS
                    return response()->json([
                        'status' => 'requires_action',
                        'client_secret' => $paymentIntent->client_secret,
                        'subscription_id' => $subscription->id,
                        'code' => 200
                    ], 200);
                } else if ($invoice->paid == true) {
                    return response()->json([
                        'status' => 'success',
                        'subscription_id' => $subscription->id,
                    ], 200);
                }
            } catch (Exception $e) {
                dd('ici error, th', $e);
                return response()->json([
                    'status' => 'error',
                    'result' => 'new_card',
                ], 500);
            }
        }
    }

    public function subscriptionFinish(Request $request)
    {
        if ($request->exist_card) {
            // Récupérer la clé Stripe depuis la configuration
            $stripeSecretKey = Config::get('stripe.secret');

            if (empty($stripeSecretKey)) {
                return redirect()->back()->with('error', 'La clé API Stripe n\'est pas configurée.');
            }

            // Initialiser Stripe avec la clé secrète
            $stripe = new StripeClient($stripeSecretKey);
            $user_id = auth()->user()->id;

            $subscription = Subscription::where('user_id', $user_id)->first();
            // c'est seulement sur exist_card qu'il se met en haut

            // incomplete, incomplete_expired, trialing, active, past_due, canceled, unpaid
            // subscription status

            $plan = Plan::where('slug', $request->planSlug)->first();

            if (isset($subscription)) {
                // Update subscription
                $subscription->update([
                    'plan_id' => $plan->id,
                    'stripe_id' => $request->subscription_id,
                    'stripe_status' => 'active',
                    'quantity' => 1,
                    'trial_ends_at' => null,
                    'ends_at' => null
                ]);
            } else {
                $subscription = new Subscription();
                $subscription->user_id = $user_id;
                $subscription->plan_id = $plan->id;
                $subscription->stripe_id = $request->subscription_id;
                $subscription->stripe_status = 'active';
                $subscription->quantity = 1;
                $subscription->trial_ends_at = null;
                $subscription->ends_at = null;
                $subscription->save();
            }

            $customerId = auth()->user()->stripe_id;
            $subscriptionBdd = $subscription;


            // Delete other card with the same fingersprint in stripe api database
            $paymentMethod = $stripe->paymentMethods->retrieve(
                $request->payment_method,
                []
            );
            // $paymentMethod->card->fingerprint
            // ID du client

            // Empreinte digitale de la carte que vous voulez conserver
            $fingerprintToKeep = $paymentMethod->card->fingerprint;

            // Récupérer toutes les méthodes de paiement du client
            $paymentMethods = $stripe->paymentMethods->all([
                'customer' => $customerId,
                'type' => 'card'
            ]);

            foreach ($paymentMethods->data as $paymentMethodTemp) {
                // Vérifier l'empreinte digitale de chaque carte
                if ($paymentMethodTemp->card->fingerprint == $fingerprintToKeep) {
                    // Si l'empreinte est différente, supprimez la méthode de paiement
                    if ($paymentMethodTemp->id != $paymentMethod->id)
                        $stripe->paymentMethods->detach($paymentMethodTemp->id);
                }
            }

            $subscription = $stripe->subscriptions->update(
                $subscriptionBdd->stripe_id,
                [
                    'cancel_at_period_end' => false,
                ]
            );
            // Disable cancel_at_period_end when it is active for a subscription_id giving


            return response()->json([
                'status' => 'success',
                'code' => 200
            ], 200);
        } else {
            $user_id = auth()->user()->id;
            // Récupérer la clé Stripe depuis la configuration
            $stripeSecretKey = Config::get('stripe.secret');

            if (empty($stripeSecretKey)) {
                return redirect()->back()->with('error', 'La clé API Stripe n\'est pas configurée.');
            }

            // Initialiser Stripe avec la clé secrète
            $stripe = new StripeClient($stripeSecretKey);
            $subscription = Subscription::where('user_id', $user_id)->first();

            $plan = Plan::where('slug', $request->planSlug)->first();

            if (isset($subscription)) {
                // Update subscription
                $subscription->update([
                    'plan_id' => $plan->id,
                    'stripe_id' => $request->subscription_id,
                    'stripe_status' => 'active',
                    'quantity' => 1,
                    'trial_ends_at' => null,
                    'ends_at' => null
                ]);
            } else {
                $subscription = new Subscription();
                $subscription->user_id = $user_id;
                $subscription->plan_id = $plan->id;
                $subscription->stripe_id = $request->subscription_id;
                $subscription->stripe_status = 'active';
                $subscription->quantity = 1;
                $subscription->trial_ends_at = null;
                $subscription->ends_at = null;
                $subscription->save();
            }

            $subscriptionBdd = $subscription;

            $paymentMethodId = $request->payment_method;
            // $userSubscription = Subscription::where('user_id', $user->id)->first();

            // $subscription_id = $request->subscription_id;
            // $plan = Plan::find($request->plan);
            $paymentMethod = $stripe->paymentMethods->retrieve(
                $paymentMethodId,
                []
            );


            $cardsExisteInDb = Card::where('fingerprint', $paymentMethod->card->fingerprint)->where('user_id', $user_id)->first();

            if (!isset($cardsExisteInDb)) {

                $cardsExisteInDb = new Card();
                $cardsExisteInDb->name = $request->cardName;
                $cardsExisteInDb->user_id = $user_id;
                $cardsExisteInDb->paymentMethodId = $paymentMethodId;
                $cardsExisteInDb->is_default_card = true;
                $cardsExisteInDb->brand = $paymentMethod->card->brand;
                $cardsExisteInDb->lastNumber = $paymentMethod->card->last4;
                $cardsExisteInDb->fingerprint = $paymentMethod->card->fingerprint;

                $cardsExisteInDb->save();

                // Set false is_default_card for other cards different of paymentMethodId
                $otherCards = Card::where('user_id', $user_id)->where('paymentMethodId', '!=', $paymentMethodId)->update(
                    [
                        'is_default_card' => false
                    ]
                );
            }
            $customerId = auth()->user()->stripe_id;


            // Delete other card with the same fingersprint in stripe api database
            // $paymentMethod already declared in top
            // $paymentMethod->card->fingerprint

            // Empreinte digitale de la carte que vous voulez conserver
            $fingerprintToKeep = $paymentMethod->card->fingerprint;

            // Récupérer toutes les méthodes de paiement du client
            $paymentMethods = $stripe->paymentMethods->all([
                'customer' => $customerId,
                'type' => 'card'
            ]);

            foreach ($paymentMethods->data as $paymentMethodTemp) {
                // Vérifier l'empreinte digitale de chaque carte
                if ($paymentMethodTemp->card->fingerprint == $fingerprintToKeep) {
                    // Si l'empreinte est différente, supprimez la méthode de paiement
                    if ($paymentMethodTemp->id != $paymentMethod->id)
                        $stripe->paymentMethods->detach($paymentMethodTemp->id);
                }
            }


            $subscription = $stripe->subscriptions->update(
                $subscriptionBdd->stripe_id,
                [
                    'cancel_at_period_end' => false,
                ]
            );
            // Disable cancel_at_period_end when it is active for a subscription_id giving

            return response()->json([
                'status' => 'success',
                'code' => 200
            ], 200);
        }
    }
}
