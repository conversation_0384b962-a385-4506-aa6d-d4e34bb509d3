<?php

namespace App\Http\Controllers\Subscription;

use App\Http\Controllers\Controller;
use App\Models\Card;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Stripe\StripeClient;

class CardController extends Controller
{
    public function show($planSlug, $new_card = false)
    {
        $cards = Card::where("user_id", auth()->user()->id)->get();

        // dd('cards',$cards);
        $cards = collect($cards);
        [$defaultCards, $otherCards] = $cards->partition(function ($card) {
            return $card->is_default_card;
        });
        $cards = $defaultCards->merge($otherCards);
        $customerId = auth()->user()->stripe_id;


        return view("recruter.payement.card", compact('cards', 'customerId'));
    }
    public function deleteCard(Request $request)
    {

        // Récupérer la clé Stripe depuis la configuration
        $stripeSecret = Config::get('stripe.secret');

        if (!$stripeSecret) {
            return response()->json([
                'status' => 'error',
                'result' => 'La clé secrète Stripe n\'est pas configurée.',
            ], 500);
        }

        $stripe = new StripeClient($stripeSecret);

        try {
            $subscriptionBdd = Subscription::where('user_id', auth()->user()->id)->first();
            if (isset($request->paymentMethodId) && isset($subscriptionBdd)) {
                // Détache la méthode de paiement du client
                $detached_payment_method = $stripe->paymentMethods->detach($request->paymentMethodId);

                if ($detached_payment_method->id === $request->paymentMethodId) {
                    // NEED TO TO, CHECK IF IS LAST CARD, IF TRUE, STOP SUBSCRIPTION ON LAST CYCLE TODO TO DO
                    $cards = Card::where('user_id', auth()->user()->id)->get();
                    if (count($cards) == 1) {
                        $subscription = $stripe->subscriptions->update(
                            $subscriptionBdd->stripe_id,
                            [
                                'cancel_at_period_end' => true,
                            ]
                        );
                    }

                    // Delete the card for the database local
                    $card = Card::where('user_id', auth()->user()->id)->where('paymentMethodId', $request->paymentMethodId)->delete();


                    // return response()->json([
                    //     'status' => 'success',
                    //     'result' => 'paymentMethodId deleting with success'
                    // ], 200);

                    // Set other card default
                    // NEED TO DO

                    return redirect()->back()->with('status', 'La carte a été supprimée avec succès');
                } else {
                    return response()->json([
                        'status' => 'error',
                        'result' => 'Error during a deleting card'
                    ], 500);
                }
            } else {
                return response()->json([
                    'status' => 'error',
                    'result' => 'paymentMethodId not passing or subscriptionId not found for deleting card'
                ], 500);
            }
        } catch (\Stripe\Exception\ApiErrorException $e) {
            // Gestion des erreurs
            // dd("Erreur : " . $e->getMessage());

            return response()->json([
                'status' => 'error' . $e->getMessage(),
                'result' => 'Error during a deleting card'
            ], 500);
        }
    }
    public function setCardDefault($customerId, $paymentMethodId)
    {
        try {
            if (isset($customerId) && isset($paymentMethodId)) {
                // Récupérer la clé Stripe depuis la configuration
                $stripeSecret = Config::get('stripe.secret');

                if (!$stripeSecret) {
                    return response()->json([
                        'status' => 'error',
                        'result' => 'La clé secrète Stripe n\'est pas configurée.',
                    ], 500);
                }

                $stripe = new StripeClient($stripeSecret);

                // Étape 1 : Attacher la méthode de paiement au client (si ce n'est pas déjà fait)
                // $stripe->paymentMethods->attach($request->paymentMethodId, ['customer' => $request->customerId]);

                // Étape 2 : Définir cette méthode de paiement comme méthode par défaut
                $updated_customer = $stripe->customers->update($customerId, [
                    'invoice_settings' => [
                        'default_payment_method' => $paymentMethodId,
                    ],
                ]);

                // Set paymentMethodId as default card on database local

                $card = Card::where('user_id', auth()->user()->id)
                    ->where('paymentMethodId', $paymentMethodId)
                    ->update(['is_default_card' => true]);

                // Set 'is_default_card'=>false for other card in database
                $card = Card::where('user_id', auth()->user()->id)
                    ->where('paymentMethodId', '!=', $paymentMethodId)
                    ->update(['is_default_card' => false]);


                // return response()->json([
                //     'status' => 'success',
                //     'result' => 'The card is setting default card for customer'
                // ], 200);
                return redirect()->back()->with('status', 'La carte de paiement par défaut a été définie avec succès.');
            } else {
                return response()->json([
                    'status' => 'error',
                    'result' => 'Parameter missing, you need to pass customerId and paymentMethodId'
                ], 500);
            }
        } catch (\Stripe\Exception\ApiErrorException $e) {
            return response()->json([
                'status' => 'error' . $e->getMessage(),
                'result' => 'Error during a setting card default'
            ], 500);
        }
    }
}
