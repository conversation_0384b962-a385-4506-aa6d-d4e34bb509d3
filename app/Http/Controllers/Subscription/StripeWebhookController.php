<?php

namespace App\Http\Controllers\Subscription;

use Symfony\Component\HttpFoundation\Response;

use App\Models\Subscription;
use App\Models\User;
use Illuminate\Support\Carbon;

use Exception;
use <PERSON><PERSON>\Cashier\Http\Controllers\WebhookController as CashierController;

class StripeWebhookController extends CashierController
{

    // A LINK FOR WEBHOOK https://domain.com/stripe/webhook

    const STATUS_ACTIVE = 'active';
    const STATUS_CANCELED = 'canceled';
    const STATUS_INCOMPLETE = 'incomplete';
    const STATUS_INCOMPLETE_EXPIRED = 'incomplete_expired';
    const STATUS_PAST_DUE = 'past_due';
    const STATUS_PAUSED = 'paused';
    const STATUS_TRIALING = 'trialing';
    const STATUS_UNPAID = 'unpaid';


    protected function handleCustomerSubscriptionUpdated(array $payload)
    {
        $user=User::where("stripe_id", $payload['data']['object']['customer'])->first();


        if (isset($user))
        {
            $data = $payload['data']['object'];

            $subscription = Subscription::where('user_id', $user->id)->first();
            if(isset($subscription))
            {
                // Cancellation date...
                if (isset($data['cancel_at_period_end']))
                {
                    if ($data['cancel_at_period_end'])
                    {
                        // $subscription->ends_at = $subscription->onTrial()? $subscription->trial_ends_at: Carbon::createFromTimestamp($data['current_period_end']);
                        // Comme on n'utilise pas la période d'essaie, on ignore ça
                        $subscription->ends_at= Carbon::createFromTimestamp($data['current_period_end']);

                    }
                    elseif (isset($data['cancel_at']))
                    {
                        $subscription->ends_at = Carbon::createFromTimestamp($data['cancel_at']);
                    }
                    else
                    {
                        $subscription->ends_at = null;
                    }
                }

                // Status...
                if (isset($data['status'])) {
                    $subscription->stripe_status = $data['status'];
                }

                $subscription->save();
                return $this->successMethod();
            }
            else
            {
                return new Response('Webhook Handled has error : '.'subscription database by user not found', 500);
            }


        }
        else
        {
            return new Response('Webhook Handled has error : '.'user by customerId stripe not found', 500);
        }


    }


    protected function handleCustomerSubscriptionDeleted(array $payload)
    {
        $user=User::where("stripe_id", $payload['data']['object']['customer'])->first();

        if (isset($user))
        {

            $subscriptionIdStripe=$payload['data']['object']['id'];

            $subscription=Subscription::where('stripe_id', $subscriptionIdStripe)->first();
            if(isset($subscription))
            {
                // To markAsCanceled, we need:
                // 'stripe_status' => StripeSubscription::STATUS_CANCELED,
                // 'ends_at' => Carbon::now(),

                $subscription->stripe_status=self::STATUS_CANCELED;
                $subscription->ends_at=Carbon::now();
                $subscription->save();

                $user->forceFill([
                    // 'stripe_id' => null,
                    'trial_ends_at' => null,
                    'pm_type' => null,
                    'pm_last_four' => null,
                ])->save();
                return $this->successMethod();

            }
            else
            {
                return new Response('Webhook Handled has error : '.'subscriptionId stripe not found in database local project', 500);
            }


        }
        else
        {
            return new Response('Webhook Handled has error : '.'user by customerId stripe not found', 500);
        }

    }

    // protected function handlePaymentIntentSucceeded(array $payload)
    // {

    //     if (!$payload["data"]["object"]["metadata"]["application"] = env('APP_URL')) {
    //         return response()->json(['message' => 'Webhook not allowed'], 200);
    //     }

    //     try {

    //         \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));
    //         $data = $payload['data']['object'];

    //         // Récupérer l'identifiant du client
    //         $customer = $data['customer'];
    //         // Retrieve the customer
    //         $customerMetadata = Customer::retrieve($customer);
    //         // return $customerMetadata['metadata']['application'];

    //         // Récupérer les détails de l'intention de paiement
    //         $paymentIntent = $payload['data']['object'];

    //         // Récupérer l'identifiant du client
    //         $customer = $paymentIntent['customer'];

    //         // Utiliser cet identifiant pour récupérer des informations sur le client
    //         $stripe = new \Stripe\StripeClient(env('STRIPE_SECRET'));
    //         $customerDetails = $stripe->customers->retrieve($customer);

    //         // Récupération d'informations supplémentaires
    //         $customerEmail = $customerDetails->email;
    //         $userClient = User::where('email', $customerEmail)->first();

    //         // Déterminer le nom du client
    //         if (!empty($customerDetails->name)) {
    //             $customerName = $customerDetails->name;
    //         } elseif ($userClient) {
    //             if (!empty($userClient->first_name) && !empty($userClient->last_name)) {
    //                 $customerName = $userClient->first_name . ' ' . $userClient->last_name;
    //             } else {
    //                 $customerName = $userClient->user_name;
    //             }
    //         } else {
    //             $customerName = 'Nom du client non disponible'; // Valeur par défaut si aucun nom trouvé
    //         }

    //         // Récupérer l'utilisateur et son adresse depuis le modèle User
    //         $user = User::where('stripe_id', $customer)->first();

    //         if (!$user) {
    //             throw new Exception("Utilisateur introuvable ou invalide.");
    //         }

    //         // Récupérer l'adresse mise à jour du client
    //         $address = [
    //             'line1' => $customerDetails->address->line1,
    //             'city' => $customerDetails->address->city,
    //             'state' => $customerDetails->address->state,
    //             'postal_code' => $customerDetails->address->postal_code,
    //             'country' => $customerDetails->address->country,
    //         ];

    //         $userData = User::where('email', $customerEmail)->first();

    //         // Vérifier si l'adresse est valide
    //         // if (empty($address['line1'])) {
    //         //     throw new Exception("Adresse utilisateur introuvable ou invalide.");
    //         // }

    //         // return $userData;

    //         if (empty($userData->address)) {
    //             throw new Exception("Adresse utilisateur introuvable ou invalide.");
    //         }

    //         // Récupérer le prix du crédit
    //         $creditPrice = PurchaseMinutes::where('user_id', $user->id)->orderBy('id', 'desc')->first();

    //         if ($creditPrice) {
    //             $packs = Pack::where('price', $creditPrice->total)->first();

    //             // $result = StripeHelper::justCalculatePriceWithTva($creditPrice->total, $address);
    //             $result = StripeHelper::justCalculatePriceWithTva($creditPrice->total, $userData->address);
    //             if (!$result) {
    //                 throw new Exception("Erreur lors du calcul du prix avec TVA.");
    //             }

    //             $invoice = Invoice::create([
    //                 'user_id' => $user->id,
    //                 'type_invoice' => "payement",
    //                 'payed_date' => $creditPrice->created_at,
    //                 'payed_date_next' => null,
    //                 'frequency' => null,
    //                 'price' => $creditPrice->total,
    //                 'curency' => "eur",
    //                 'price_tva_only' => $result['tax'],
    //                 'price_tva' => $result['price_total'],
    //             ]);

    //             $invoiceData = [
    //                 'item' => "Achat crédit",
    //                 'customer_email' => $customerEmail,
    //                 'customer_name' => $customerName,
    //                 'customer_id' => $user->id,
    //                 'pack_name' => 'Crédit',
    //                 'creditPrice' => $creditPrice->total,
    //                 'price_total' => $result['price_total'],
    //                 'tax' => $result['tax'],
    //                 'taxPercent' => $result['taxPercent'],
    //                 'billing_period' => now(),
    //                 'invoiceId' => $invoice->id,
    //                 'address' => $userData->address,

    //             ];

    //             $pdfInfo = StripeHelper::generateInvoicePdf($invoiceData);

    //             // Envoyer l'email avec la facture
    //             Mail::to($customerEmail)->send(new InvoiceMail($invoiceData, $pdfInfo['path'], $invoice->id));

    //             return response()->json(['status' => 'success'], 200);
    //         }
    //     } catch (Exception $e) {
    //         // Loguer l'erreur pour débogage
    //         Log::error('Erreur dans handlePaymentIntentSucceeded: ' . $e->getMessage());
    //         return response()->json(['error' => $e->getMessage()], 400);
    //     }
    // }

    // protected function handleInvoicePaid(array $payload)
    // {


    //     \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));
    //     $stripe = new \Stripe\StripeClient(env('STRIPE_SECRET'));


    //     $invoice = $payload['data']['object'];

    //     // Récupérer l'identifiant du client
    //     $customer = $invoice['customer'];
    //     // Retrieve the customer
    //     $customerMetadata = Customer::retrieve($customer);
    //     // return $customerMetadata['metadata']['application'];

    //     if ($customerMetadata['metadata']['application'] == env('APP_URL')) {
    //         try {


    //             $customerDetails = $stripe->customers->retrieve($customer);

    //             // Récupération d'informations supplémentaires
    //             $customerEmail = $customerDetails->email;
    //             // $customerName = $customerDetails->name;
    //             $userClient = User::where('email', $customerEmail)->first();


    //             // Déterminer le nom du client
    //             if (!empty($customerDetails->name)) {
    //                 $customerName = $customerDetails->name;
    //             } elseif ($userClient) {
    //                 if (!empty($userClient->first_name) && !empty($userClient->last_name)) {
    //                     $customerName = $userClient->first_name . ' ' . $userClient->last_name;
    //                 } else {
    //                     $customerName = $userClient->user_name;
    //                 }
    //             } else {
    //                 $customerName = 'Nom du client non disponible'; // Valeur par défaut si aucun nom trouvé
    //             }

    //             // Récupérer l'utilisateur depuis le modèle User
    //             $user = User::where('stripe_id', $customer)->first();

    //             if (!$user) {
    //                 throw new Exception("Utilisateur introuvable ou invalide.");
    //             }

    //             $address = [
    //                 'line1' => $customerDetails->address->line1,
    //                 'city' => $customerDetails->address->city,
    //                 'state' => $customerDetails->address->state,
    //                 'postal_code' => $customerDetails->address->postal_code,
    //                 'country' => $customerDetails->address->country,
    //             ];

    //             // Extraire les informations de la facture
    //             $subTotal = $invoice['subtotal'] / 100; // Sous-total en euros
    //             $taxAmount = $invoice['tax'] / 100; // Montant de la TVA en euros
    //             $total = $invoice['total'] / 100; // Montant total en euros
    //             $amountDue = $invoice['amount_due'] / 100; // Montant dû en euros

    //             // Calculer le pourcentage de taxe
    //             $taxPercentage = ($subTotal > 0) ? ($taxAmount / $subTotal) * 100 : 0;

    //             // Extraire les dates de la période de facturation
    //             $startDate = Carbon::createFromTimestamp($invoice['lines']['data'][0]['period']['start']);
    //             $endDate = Carbon::createFromTimestamp($invoice['lines']['data'][0]['period']['end']);

    //             // Formater les dates en français
    //             $startDateFormatted = $startDate->locale('fr')->isoFormat('D MMM. YYYY');
    //             $endDateFormatted = $endDate->locale('fr')->isoFormat('D MMM. YYYY');
    //             $periodFormatted = "{$startDateFormatted} - {$endDateFormatted}";

    //             // Récupérer l'ID du produit à partir de l'objet `price`
    //             $priceId = $invoice['lines']['data'][0]['price']['id'];
    //             $priceDetails = $stripe->prices->retrieve($priceId);
    //             $productId = $priceDetails['product'];

    //             // Récupérer les détails du produit pour obtenir le nom
    //             $productDetails = $stripe->products->retrieve($productId);
    //             $productName = $productDetails['name'];

    //             // Créer l'enregistrement de la facture dans votre base de données
    //             $invoice = Invoice::create([
    //                 'user_id' => $user->id,
    //                 'type_invoice' => "subscription",
    //                 'payed_date' => $startDate,
    //                 'payed_date_next' => $endDate,
    //                 'frequency' => 'per month',
    //                 'price' => $subTotal,
    //                 'curency' => "eur",
    //                 'price_tva_only' => $taxAmount,
    //                 'price_tva' => $total,
    //                 'billing_period' => $periodFormatted,
    //             ]);

    //             $invoiceData = [
    //                 'item' => "Achat Abonnement",
    //                 'customer_email' => $customerEmail,
    //                 'customer_name' => $customerName,
    //                 'customer_id' => $user->id,
    //                 'pack_name' => $productName, // Utiliser le nom du produit
    //                 'creditPrice' => $subTotal,
    //                 'price_total' => $total,
    //                 'tax' => $taxAmount,
    //                 'taxPercent' => $taxPercentage,
    //                 'billing_period' => $periodFormatted,
    //                 'invoiceId' => $invoice->id,
    //                 'address' => $userClient->address


    //             ];

    //             $pdfInfo = StripeHelper::generateInvoicePdf($invoiceData);

    //             // Envoyer l'email avec la facture
    //             Mail::to($customerEmail)->send(new InvoiceMail($invoiceData, $pdfInfo['path'], $invoice->id));

    //             return response()->json(['status' => 'success'], 200);
    //         } catch (Exception $e) {
    //             // Loguer l'erreur pour débogage
    //             Log::error('Erreur dans handleInvoicePaid: ' . $e->getMessage());
    //             return response()->json(['error' => $e->getMessage()], 400);
    //         }
    //     } else {
    //         return response()->json(['status' => 'success', 'message' => 'app.neuralcalls.com'], 200);
    //     }
    // }

    // public function handleInvoicePaymentFailed(Request $request)
    // {
    //     $payload = $request->all();

    //     // Récupérer l'identifiant du client
    //     $customerId = $payload['data']['object']['customer'];
    //     $subscriptionId = $payload['data']['object']['subscription'];

    //     // Récupérer l'utilisateur associé à l'ID Stripe
    //     $user = $this->getUserByStripeId($customerId);

    //     if ($user) {
    //         // Récupérer l'abonnement dans la base de données
    //         $subscription = $user->subscriptions()->where('subscription_stripe_id', $subscriptionId)->first();

    //         if ($subscription) {
    //             // Mettre à jour la valeur stripe_status à 'pending'
    //             $subscription->stripe_status = 'pending';
    //             $subscription->save();
    //         }

    //         // Effectuer les actions nécessaires, comme envoyer un email à l'utilisateur
    //         $customerEmail = $user->email;

    //         // Récupérer les détails de l'abonnement et du produit
    //         $stripe = new \Stripe\StripeClient(env('STRIPE_SECRET'));
    //         $subscriptionDetails = $stripe->subscriptions->retrieve($subscriptionId);
    //         $productId = $subscriptionDetails->items->data[0]->price->product;
    //         $product = $stripe->products->retrieve($productId);

    //         // Récupérer la date de la tentative de paiement
    //         $paymentAttemptDate = Carbon::createFromTimestamp($payload['created'])->format('d M Y');

    //         // Envoyer un email d'avertissement à l'utilisateur
    //         Mail::to($customerEmail)->send(new PaymentFailedEmail($user, $product->name, $paymentAttemptDate));

    //         // Répondre avec succès à Stripe pour accuser réception du webhook
    //         return response()->json([
    //             'status' => 'success',
    //             'message' => "Payment failed email sent to user {$user->id} (Customer ID: $customerId, Subscription ID: $subscriptionId)"
    //         ], 200);
    //     }

    //     // Répondre avec une erreur si l'utilisateur n'est pas trouvé
    //     return response()->json([
    //         'status' => 'error',
    //         'message' => "User not found for Customer ID: $customerId"
    //     ], 404);
    // }
}
