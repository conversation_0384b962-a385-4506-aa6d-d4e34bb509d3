<?php

namespace App\Http\Controllers\Subscription;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use Stripe\StripeClient;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Config;

class InvoiceController extends Controller
{
    public function show($planSlug, $new_card = false)
    {
        // Récupérer la clé Stripe depuis la configuration
        $stripeSecret = Config::get('stripe.secret');

        if (!$stripeSecret) {
            return response()->json([
                'status' => 'error',
                'result' => 'La clé secrète Stripe n\'est pas configurée.',
            ], 500);
        }

        $stripe = new StripeClient($stripeSecret);
        $subscriptionUser = Subscription::where('user_id', auth()->user()->id)->first();

        if (isset($subscriptionUser)) {
            $subscriptionId = $subscriptionUser->stripe_id;
            try {
                // Récupérer les factures associées à l'abonnement
                $invoices = $stripe->invoices->all([
                    'subscription' => $subscriptionId,
                    'limit' => 100,
                ]);

                $duplicatedInvoices = new Collection();
                $duplicationNumber = 0;
                // Dupliquer la collection des factures $duplicationNumber fois pour test de pagination
                for ($i = 0; $i <= $duplicationNumber; $i++) {
                    $duplicatedInvoices = $duplicatedInvoices->concat($invoices->data);
                }

                // Pagination manuelle
                $currentPage = LengthAwarePaginator::resolveCurrentPage();
                $perPage = 5;
                $currentInvoices = $duplicatedInvoices->slice(($currentPage - 1) * $perPage, $perPage)->all();
                $paginatedInvoices = new LengthAwarePaginator($currentInvoices, $duplicatedInvoices->count(), $perPage, $currentPage, [
                    'path' => LengthAwarePaginator::resolveCurrentPath()
                ]);
            } catch (\Stripe\Exception\ApiErrorException $e) {
                $paginatedInvoices = new LengthAwarePaginator([], 0, 10);
            }
        } else {
            $paginatedInvoices = new LengthAwarePaginator([], 0, 10);
        }

        return view("recruter.payement.invoice", compact("paginatedInvoices"));
    }
}
