<?php

namespace App\Http\Controllers\Subscription;

use App\Http\Controllers\Controller;
use App\Models\Address;
use App\Models\Card;
use App\Models\Country;
use App\Models\Plan;
use App\Models\Product;
use App\Models\Webhook;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Stripe\Price;
use Stripe\Stripe;
use Illuminate\Support\Str;
use Stripe\StripeClient;

class PlanController extends Controller
{
    public function show($planSlug, $new_card = false)
    {

        $addresseUser = Address::where('user_id', auth()->user()->id)->first();
        $countries = Country::all();
        $cards = Card::where('user_id', auth()->user()->id)->get();
        // dd('ici plan',$planSlug,$new_card,$cards,count($cards));


        if ($new_card)
            return view('recruter.payement.new-card-form', ['planSlug' => $planSlug, 'addresseUser' => $addresseUser, 'countries' => $countries, 'cards' => $cards]);

        else if (count($cards) > 0)
            return view('recruter.payement.exist-card-form', ['planSlug' => $planSlug, 'addresseUser' => $addresseUser, 'countries' => $countries, 'cards' => $cards]);
        else
            return view('recruter.payement.new-card-form', ['planSlug' => $planSlug, 'addresseUser' => $addresseUser, 'countries' => $countries, 'cards' => $cards]);
    }

    public function listProduct()
    {
        // $plans = Plan::all();
        $plans = Plan::paginate(3);

        // Retourne la vue avec les produits
        return view('admin.stripe-product.index', compact('plans'));
    }

    public function createStripeProduct(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|array',
            'recurrent' => 'nullable|boolean',
            'amount' => 'required|numeric|min:0.5',
            'currency' => 'nullable|string|size:3',
            'billing_period' => 'required|string|in:day,week,month',
        ]);

        // Vérifier la clé Stripe
        $stripeSecret = Config::get('stripe.secret');
        if (!$stripeSecret) {
            return redirect()->route('admin.stripe-product.index')->with('error', 'La clé Stripe n\'est pas configurée.');
        }

        // Vérifier si un produit avec le même nom existe déjà dans la table plans
        $existingPlan = Plan::where('name', $validated['name'])->first();
        if ($existingPlan) {
            // Si un produit avec le même nom existe, renvoyer une erreur à afficher avec SweetAlert
            return redirect()->route('admin.stripe-product.index')->with('error', 'Un produit avec ce nom existe déjà dans la base de données.');
        }

        // Validation de chaque élément du tableau de description
        foreach ($validated['description'] as $desc) {
            if (!is_string($desc)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chaque élément de description doit être une chaîne.',
                ], 400);
            }
        }

        // Générer le slug à partir du nom du produit
        $slug = Str::slug($validated['name'], '_'); // Convertit "Pro Max Student" en "pro_max_student"

        $client = new Client();

        try {
            // Créer le produit Stripe
            $productResponse = $client->post('https://api.stripe.com/v1/products', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $stripeSecret,
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
                'form_params' => [
                    'name' => $validated['name'],
                    'description' => implode(', ', $validated['description']),
                ],
            ]);

            $product = json_decode($productResponse->getBody()->getContents(), true);

            // Créer un prix associé
            $priceResponse = $client->post('https://api.stripe.com/v1/prices', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $stripeSecret,
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
                'form_params' => [
                    'unit_amount' => $validated['amount'] * 100,
                    'currency' => $validated['currency'] ?? 'usd',
                    'product' => $product['id'],
                    'recurring[interval]' => $validated['billing_period'],
                ],
            ]);

            $price = json_decode($priceResponse->getBody()->getContents(), true);

            // Ajouter le prix par défaut
            $product['default_price'] = $price['id'];

            // Enregistrer le produit dans la base de données
            $plan = Plan::create([
                'name' => $validated['name'],
                'slug' => $slug, // Ajout du slug ici
                'stripe_product_id' => $product['id'],
                'stripe_price_id' => $price['id'],
                'price' => $validated['amount'],
                'currency' => $validated['currency'],
                'show_name' => true,
                'description_html' => json_encode($validated['description']),
                'duration_in_days' => $validated['billing_period'] === 'day' ? 1 : ($validated['billing_period'] === 'week' ? 7 : 30),
            ]);

            // Retourner à la page avec un message de succès
            return redirect()->route('admin.stripe-product.index')->with('success', 'Produit Stripe créé avec succès.');
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la création du produit Stripe.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function deleteStripeProduct($productId)
    {
        $stripeSecret = Config::get('stripe.secret');
        if (!$stripeSecret) {
            return redirect()->route('admin.stripe-product.index')->with('error', 'La clé Stripe n\'est pas configurée.');
        }

        $stripe = new StripeClient($stripeSecret);

        try {
            // Mettre à jour le produit pour le désactiver
            $product = $stripe->products->update(
                $productId,
                ['active' => false] // Désactiver le produit
            );

            // Vérifier si le produit a bien été mis à jour
            if ($product->id === $productId && !$product->active) {
                // Supprimer l'enregistrement correspondant dans la table `plans`
                $plan = Plan::where('stripe_product_id', $productId)->first();

                if ($plan) {
                    $plan->delete(); // Supprimer l'enregistrement
                    // Rediriger avec un message de succès
                    return redirect()->route('admin.stripe-product.index')->with('success', 'Produit Stripe désactivé et plan supprimé avec succès.');
                }

                return redirect()->route('admin.stripe-product.index')->with('error', 'Aucun plan trouvé avec cet ID Stripe.');
            }

            return redirect()->route('admin.stripe-product.index')->with('error', 'Échec de la désactivation du produit Stripe.');
        } catch (\Exception $e) {
            // Gérer les erreurs
            return redirect()->route('admin.stripe-product.index')->with('error', 'Erreur lors de la désactivation du produit Stripe : ' . $e->getMessage());
        }
    }
    public function updateStripeProduct(Request $request, $productId)
    {
        // Validation des données
        $validated = $request->validate([
            'name' => 'required|string',
            // 'description' => 'required|string',
            'description' => 'required|array',

        ]);

        // Validation de chaque élément du tableau de description
        foreach ($validated['description'] as $desc) {
            if (!is_string($desc)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chaque élément de description doit être une chaîne.',
                ], 400);
            }
        }


        $stripeSecret = Config::get('stripe.secret');
        if (!$stripeSecret) {
            return redirect()->route('admin.stripe-product.index')->with('error', 'La clé Stripe n\'est pas configurée.');
        }

        $stripe = new StripeClient($stripeSecret);


        try {
            // Mise à jour du produit dans Stripe
            $product = $stripe->products->update(
                $productId, // Utilisation de $productId passé en paramètre
                [
                    'name' => $validated['name'],
                    'description' => implode(', ', $validated['description']),
                ]
            );

            // Retourner la réponse
            return response()->json($product, 200);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function retrievePrice($priceId)
    {

        $stripeSecret = Config::get('stripe.secret');
        if (!$stripeSecret) {
            return redirect()->route('admin.stripe-product.index')->with('error', 'La clé Stripe n\'est pas configurée.');
        }

        $stripe = new StripeClient($stripeSecret);


        try {
            // Récupération des informations du prix
            $price = $stripe->prices->retrieve($priceId, []);

            // Retourner les informations du prix sous forme de réponse JSON
            return response()->json($price, 200);
        } catch (\Exception $e) {
            // Gérer les erreurs éventuelles
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function webhookList()
    {
        $webhooks = Webhook::all();

        return view('admin.stripe-webhook.index', compact('webhooks'));
    }

    public function createWebhook(Request $request)
    {
        // Validation de l'URL envoyée par l'utilisateur
        $validated = $request->validate([
            'url' => 'required|string', // Vérification que l'URL est valide
        ]);

        // Vérifier s'il existe déjà un webhook dans la base de données
        if (Webhook::count() > 0) {
            // Retourner une réponse avec un message d'erreur SweetAlert
            return redirect()->back()->with('error', 'Vous ne pouvez pas ajouter deux webhooks.');
        }

        // Récupérer l'URL de la requête et ajouter "/stripe/webhook" à l'URL de base
        $webhookUrl = 'https://' . $validated['url'] . '/stripe/webhook';

        // Récupérer la clé Stripe depuis la configuration
        $stripeSecretKey = Config::get('stripe.secret');

        if (empty($stripeSecretKey)) {
            return redirect()->back()->with('error', 'La clé API Stripe n\'est pas configurée.');
        }

        // Initialiser Stripe avec la clé secrète
        $stripe = new StripeClient($stripeSecretKey);

        try {
            // Créer le webhook avec Stripe
            $webhook = $stripe->webhookEndpoints->create([
                'url' => $webhookUrl,
                'enabled_events' => ['customer.subscription.deleted', 'customer.subscription.updated'],
            ]);

            // Enregistrer le webhook dans la base de données
            $webhookData = Webhook::create([
                'status' => $webhook->status,
                'webhook_id' => $webhook->id,
                'secret' => $webhook->secret,
                'url' => $webhook->url,
            ]);

            // Retourner une réponse indiquant que le webhook a été créé avec succès et enregistré
            return redirect()->back()->with('success', 'Webhook created and saved successfully.');
        } catch (ApiErrorException $e) {
            // Gestion des erreurs Stripe
            return redirect()->back()->with('error', 'Error creating webhook: ' . $e->getMessage());
        }
    }

    public function updateWebhookUrl(Request $request, $webhookId)
    {
        // Validation de l'URL
        $validated = $request->validate([
            'url' => 'required|string', // Vérification que l'URL est valide
        ]);

        // Récupérer la clé Stripe depuis la configuration
        $stripeSecretKey = Config::get('stripe.secret');

        if (empty($stripeSecretKey)) {
            return redirect()->back()->with('error', 'La clé API Stripe n\'est pas configurée.');
        }

        // Initialiser Stripe avec la clé secrète
        $stripe = new StripeClient($stripeSecretKey);

        // Récupérer le webhook existant à partir de l'ID
        $webhook = Webhook::where('webhook_id', $webhookId)->first();

        if (!$webhook) {
            return redirect()->back()->with('error', 'Webhook not found.');
        }

        // Construire l'URL du webhook
        $webhookUrl = 'https://' . $validated['url'] . '/stripe/webhook';

        try {
            // Mettre à jour l'URL du webhook avec Stripe
            $updatedWebhook = $stripe->webhookEndpoints->update(
                $webhookId, // L'ID du webhook à mettre à jour
                [
                    'url' => $webhookUrl, // L'URL du webhook mise à jour
                ]
            );

            // Mettre à jour l'URL dans la base de données
            $webhook->update([
                'url' => $updatedWebhook->url, // Mise à jour de l'URL dans la base de données
            ]);

            // Retourner une réponse indiquant que le webhook a été mis à jour avec succès
            return redirect()->back()->with('success', 'Webhook URL updated successfully.');
        } catch (ApiErrorException $e) {
            // Gestion des erreurs Stripe
            return redirect()->back()->with('error', 'Error updating webhook URL: ' . $e->getMessage());
        }
    }


    public function deleteWebhook(Request $request)
    {
        // Validation de l'ID du webhook
        $validated = $request->validate([
            'webhook_id' => 'required|string|exists:webhooks,webhook_id', // Vérifier que l'ID existe dans la base de données
        ]);

        $webhookId = $validated['webhook_id'];

        // Récupérer la clé Stripe depuis la configuration
        $stripeSecretKey = Config::get('stripe.secret');

        if (empty($stripeSecretKey)) {
            return redirect()->back()->with('error', 'La clé API Stripe n\'est pas configurée.');
        }

        // Initialiser Stripe avec la clé secrète
        $stripe = new StripeClient($stripeSecretKey);

        try {
            // Supprimer le webhook avec l'ID fourni
            $stripe->webhookEndpoints->delete($webhookId);

            // Optionnel : Supprimer l'enregistrement du webhook dans la base de données
            Webhook::where('webhook_id', $webhookId)->delete();

            // Retourner une réponse de succès
            return redirect()->back()->with('success', 'Webhook supprimé avec succès.');
        } catch (\Stripe\Exception\ApiErrorException $e) {
            // Gestion des erreurs Stripe
            return redirect()->back()->with('error', 'Erreur lors de la suppression du webhook : ' . $e->getMessage());
        }
    }

    public function editWebhook($webhookId)
    {
        // Récupérer le webhook à partir de la base de données
        $webhook = Webhook::where('webhook_id', $webhookId)->first();

        // Si le webhook n'existe pas, rediriger avec un message d'erreur
        if (!$webhook) {
            return redirect()->route('stripe-webhook.index')->with('error', 'Webhook non trouvé.');
        }

        // Retourner la vue avec le webhook à modifier
        return view('admin.webhook.edit', compact('webhook'));
    }
}
