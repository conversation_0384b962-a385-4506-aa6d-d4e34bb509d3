<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckSuspension
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();

        if ($user && $user->is_suspend) {
            // Déconnecter l'utilisateur suspendu
            Auth::logout();

            // Rediriger vers la page de suspension
            return redirect()->route('suspenssion_page.suspenssion-page');
        }

        return $next($request);
    }
}
