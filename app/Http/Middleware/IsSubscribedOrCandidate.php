<?php

namespace App\Http\Middleware;

use App\Helpers\UsedUpFunction;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class IsSubscribedOrCandidate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if(UsedUpFunction::isCandidate())
        {
            return $next($request);
        } 
        
        if(UsedUpFunction::isRecruter() && UsedUpFunction::isSubscribed())
        {
            return $next($request);
        } 
        else
        {
            return redirect()->route('recruter.packages');
        }
    }
}
