<?php

namespace App\Http\Middleware;

use Closure;

class TrailingSlashMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $uri = $request->getRequestUri();

        // Vérifie si l'URI ne se termine pas par un slash, sauf pour les fichiers (comme .css, .js)
        if (!preg_match('/\/$/', $uri) && !pathinfo($uri, PATHINFO_EXTENSION)) {
            return redirect($uri . '/');
        }

        return $next($request);
    }
}
