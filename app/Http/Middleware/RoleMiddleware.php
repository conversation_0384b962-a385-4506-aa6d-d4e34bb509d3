<?php

namespace App\Http\Middleware;

use App\Models\Role;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  ...$roles
     * @return mixed
     */
    public function handle(Request $request, Closure $next, ...$roles)
    {
        // Vérifie que l'utilisateur est authentifié
        if (!Auth::check()) {
            return redirect()->route('login'); // Redirige vers la page de connexion si non connecté
        }

        $userRole = Role::find(Auth::user()->role_id)->slug; // Récupère le rôle de l'utilisateur

        // dd($userRole, $roles,in_array($userRole, $roles));

        // Vérifie si le rôle de l'utilisateur correspond à un des rôles autorisés
        if (!in_array($userRole, $roles)) {
            if($userRole == 'admin'){
                return redirect()->route('admin.index');
            }
            if($userRole == 'recruter'){
                return redirect()->route('recruter.dashboard');
            }
            if($userRole == 'candidate'){
                return redirect()->route('candidate.dashboard');
            }
            abort(403, 'Accès interdit'); // Renvoie une erreur 403 si l'utilisateur n'a pas le bon rôle
        }

        return $next($request);
    }
}
