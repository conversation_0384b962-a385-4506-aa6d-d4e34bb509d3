<?php

namespace App\Http\Middleware;

use App\Helpers\UsedUpFunction;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class IsSubscribed
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {

        if(UsedUpFunction::isSubscribed())
        {
            return $next($request);
        } 
        else
        {
            return redirect()->route('recruter.packages');
        }

    }
}
