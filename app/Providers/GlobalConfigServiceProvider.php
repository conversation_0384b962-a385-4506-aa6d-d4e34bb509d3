<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class GlobalConfigServiceProvider extends ServiceProvider
{
    /**
     * Enregistre les configurations avant l'exécution de boot().
     *
     * @return void
     */
    public function register()
    {
        Log::info("GlobalConfigServiceProvider exécuté dans register()");

        // Charger les configurations depuis la table config_global_apps
        $configs = DB::table('config_global_apps')->get();

        foreach ($configs as $config) {
            Log::info("Chargement de la configuration", ['name' => $config->name, 'value' => $config->value]);

            // Si la valeur est un JSON, on la décode
            $decodedValue = json_decode($config->value, true);
            $value = (json_last_error() === JSON_ERROR_NONE) ? $decodedValue : $config->value;

            Config::set($config->name, $value);
        }

        // Configurer les paramètres Stripe immédiatement
        $this->configureStripeSettings();

        // Configurer les paramètres de messagerie
        $this->configureMailSettings();

        // Configurer CAPTCHA
        $this->configureCaptchaSettings();

        // Configurer l'email administrateur
        $adminEmail = config('admin_mail');
        if ($adminEmail && is_string($adminEmail)) {
            Config::set('app.admin_email', $adminEmail);
        }

        Log::info("Configurations chargées avec succès");
    }

    /**
     * Configure Stripe depuis la base de données.
     *
     * @return void
     */
    private function configureStripeSettings()
    {
        // Charger la configuration Stripe dans boot()
        $stripeConfig = config('stripe_config');

        if ($stripeConfig && is_array($stripeConfig)) {
            Config::set('cashier.currency', $stripeConfig['CASHIER_CURRENCY'] ?? 'usd');
            Config::set('stripe.key', $stripeConfig['STRIPE_KEY'] ?? null);
            Config::set('stripe.secret', $stripeConfig['STRIPE_SECRET'] ?? null);
            Config::set('stripe.webhook.secret', $stripeConfig['STRIPE_WEBHOOK_SECRET'] ?? null);
        }

        Log::info("Config Stripe après chargement dans boot()", [
            'cashier_currency' => config('cashier.currency'),
            'stripe_key' => config('stripe.key'),
            'stripe_secret' => config('stripe.secret'),
            'stripe_webhook_secret' => config('stripe.webhook.secret'),
        ]);
    }

    /**
     * Configure les paramètres de messagerie.
     *
     * @return void
     */
    private function configureMailSettings()
    {

        $mailConfig = config('mail_config');

        if ($mailConfig && is_array($mailConfig)) {
            Config::set('mail.default', $mailConfig['MAIL_MAILER'] ?? 'smtp');
            Config::set('mail.mailers.smtp', [
                'transport' => 'smtp',
                'host' => $mailConfig['MAIL_HOST'] ?? 'smtp.mailgun.org',
                'port' => $mailConfig['MAIL_PORT'] ?? 587,
                'encryption' => $mailConfig['MAIL_ENCRYPTION'] ?? 'tls',
                'username' => $mailConfig['MAIL_USERNAME'] ?? null,
                'password' => $mailConfig['MAIL_PASSWORD'] ?? null,
                'timeout' => null,
                'local_domain' => null,
            ]);
            Config::set('mail.from', [
                'address' => $mailConfig['MAIL_FROM_ADDRESS'] ?? '<EMAIL>',
                'name' => $mailConfig['MAIL_FROM_NAME'] ?? 'Example',
            ]);
        }

        Log::info("Configurations mail chargées");
    }

    /**
     * Configure les paramètres CAPTCHA.
     *
     * @return void
     */
    private function configureCaptchaSettings()
    {

        // dd(config('captcha.secret'));

        $captchaConfig = config('captcha_config');

        if ($captchaConfig && is_array($captchaConfig)) {
            Config::set('captcha.secret', $captchaConfig['NOCAPTCHA_SECRET'] ?? null);
            Config::set('captcha.sitekey', $captchaConfig['NOCAPTCHA_SITEKEY'] ?? null);
        }

        Log::info("Configurations CAPTCHA chargées");
    }

    /**
     * Exécute les actions une fois le framework prêt.
     *
     * @return void
     */
    public function boot()
    {
        Log::info("GlobalConfigServiceProvider exécuté dans boot()");

        // Vérification si les configurations Stripe sont bien en mémoire
        Log::info("Config Stripe dans boot():", [
            'stripe_key' => config('stripe.key'),
            'stripe_secret' => config('stripe.secret'),
        ]);
    }
}
