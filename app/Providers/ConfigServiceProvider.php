<?php

namespace App\Providers;

use App\Services\ConfigsService;
use Illuminate\Support\ServiceProvider;

class ConfigServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot()
    {
        // Charger les configurations de la base de données
        $mailSettings = ConfigsService::getMailConfig();

        // Fusionner les configurations avec celles existantes
        config(['mail' => array_merge(config('mail', []), $mailSettings)]);
    }
}
