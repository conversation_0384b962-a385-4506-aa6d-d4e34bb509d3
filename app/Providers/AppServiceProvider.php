<?php

namespace App\Providers;

use App\Models\Priority;
use App\Models\Severity;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\View;
use Laravel\Cashier\Cashier;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {

        Cashier::calculateTaxes();
        // Calcul taxe automaticly

        if(env(('APP_ENV')) !== 'local') {
            URL::forceScheme('https');
        }

        View::composer('layouts.guest', function ($view) {
            $view->with('priorities', Priority::all())
                 ->with('severities', Severity::all());
        });

        View::composer('components.recruter-dashboard-layout', function ($view) {
            $view->with('priorities', Priority::all())
                 ->with('severities', Severity::all());
        });

        View::composer('components.candidate-dashboard-layout', function ($view) {
            $view->with('priorities', Priority::all())
                 ->with('severities', Severity::all());
        });
    }
}
