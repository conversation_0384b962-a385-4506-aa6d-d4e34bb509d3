<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Jenssegers\Mongodb\Eloquent\Model;

class UserLanguage extends Model
{
    use HasFactory;

    protected $connection = 'mongodb';
    protected $collection = 'user_languages';

    protected $fillable = [
        'user_id',
        'language',
        'level',
    ];

    /**
     * Relation avec l'utilisateur
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relation avec la langue
     */
    public function languageModel()
    {
        return $this->belongsTo(Language::class, 'language');
    }
}
