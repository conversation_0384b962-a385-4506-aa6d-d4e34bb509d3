<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use MongoDB\Laravel\Eloquent\Model;


class Mantis extends Model
{
    use HasFactory;

    protected $connection = 'mongodb';

    protected $fillable = [
        'email',
        'summary',
        'description',
        // 'category_id',
        // 'priority_id',
        // 'severity_id',
    ];

    protected $casts = [
        // 'category_id' => 'integer',
        // 'priority_id' => 'integer',
        // 'severity_id' => 'integer',
        // 'mantis_id' => 'integer',
        // 'project_id' => 'integer',
    ];

    // Relation éventuelle avec un modèle User si vous voulez lier à des utilisateurs
    public function user()
    {
        return $this->belongsTo(User::class, 'email', 'email');
    }
}