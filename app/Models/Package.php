<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use MongoDB\Laravel\Eloquent\Model;

class Package extends Model
{
    use HasFactory;

    protected $connection = 'mongodb';


    protected $fillable = [
        'name',
        'price',
        'currency',
        'tax_included',
        'features'
    ];

    protected $casts = [
        'features' => 'array', // Décodage automatique du JSON
        'tax_included' => 'boolean',
    ];
}