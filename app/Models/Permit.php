<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Model;
use MongoDB\Laravel\Eloquent\Model;

class Permit extends Model
{
    use HasFactory;
    
    protected $connection = 'mongodb'; 

    public $fillable = ['name', 'slug'];

    public static function getAllActive()
    {
        return Permit::where('is_active', true)->get();
    }
}
