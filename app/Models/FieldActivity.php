<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use MongoDB\Laravel\Eloquent\Model;

class FieldActivity extends Model
{
    use HasFactory;

    protected $connection = 'mongodb';

    protected $fillable = [
        'name',
        'slug',
        'is_active',
        'normalizedName'
    ];

    protected static function boot()
    {
        parent::boot();

        // Avant sauvegarde, on génère normalizedName
        static::saving(function ($model) {
            $model->normalizedName = self::normalizeString($model->name);
        });
    }

    public static function getAllActive()
    {
        return self::where('is_active', true)->get();
    }

    public static function normalizeString($string)
    {
        $string = iconv('UTF-8', 'ASCII//TRANSLIT', $string);
        $string = preg_replace('/[^A-Za-z0-9]/', '', $string);
        return strtolower($string);
    }
}
