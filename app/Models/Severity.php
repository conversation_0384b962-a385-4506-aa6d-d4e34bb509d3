<?php
// app/Models/Severity.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use MongoDB\Laravel\Eloquent\Model;


class Severity extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'value', 'is_default'];

    protected $casts = [
        'value' => 'integer',
        'is_default' => 'boolean'
    ];

    public static function getDefault()
    {
        return self::where('is_default', true)->first();
    }

    public function scopeActive($query)
    {
        return $query->orderBy('value');
    }
}