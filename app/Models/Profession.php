<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use MongoDB\Laravel\Eloquent\Model;

class Profession extends Model
{
    use HasFactory;

    protected $connection = 'mongodb';

    protected $fillable = [
        'name',
        'slug',
        'is_active',
        'field_activity_id', // Clé étrangère
        'normalizedName'      // Champ pour les recherches sans accent
    ];

    /**
     * Boot method pour gérer les événements du modèle.
     */
    protected static function boot()
    {
        parent::boot();

        // Avant sauvegarde, on génère normalizedName
        static::saving(function ($model) {
            $model->normalizedName = self::normalizeString($model->name);
        });
    }

    /**
     * Normalise une chaîne (supprime accents et met en minuscule)
     */
    public static function normalizeString($string)
    {
        // Remplace les accents
        $string = iconv('UTF-8', 'ASCII//TRANSLIT', $string);
        // Supprime les caractères spéciaux
        $string = preg_replace('/[^A-Za-z0-9]/', '', $string);
        // Met en minuscule
        return strtolower($string);
    }

    /**
     * <PERSON>tourne toutes les professions actives
     */
    public static function getAllActive()
    {
        return self::where('is_active', true)->get();
    }

    /**
     * Relation avec FieldActivity
     */
    public function fieldActivity()
    {
        return $this->belongsTo(FieldActivity::class);
    }
}
