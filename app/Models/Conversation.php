<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Model;
use MongoDB\Laravel\Eloquent\Model;

class Conversation extends Model
{
    use HasFactory;

    protected $fillable = ['user_one_id', 'user_two_id'];

    public function messages()
    {
        // return $this->hasMany(Message::class);
        return Message::where('conversation_id', $this->id)->get();
    }

    public function last_message()
    {
        // return $this->messages()?->orderBy('created_at', 'desc')?->first();
        return Message::where('conversation_id', $this->id)->orderBy('created_at', 'desc')->first();
    }

    public function userOne()
    {
        // return $this->belongsTo(User::class, 'user_one_id');
        return User::find($this->user_one_id);
    }

    public function userTwo()
    {
        // return $this->belongsTo(User::class, 'user_two_id');
        return User::find($this->user_two_id);
    }
}
