<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Model;
use MongoDB\Laravel\Eloquent\Model;

class Civility extends Model
{
    use HasFactory;
    
    protected $connection = 'mongodb'; 

    protected $fillable = [
        'user_id',
        'first_name',
        'last_name',
        'sexe',
        'photo_file_id',
        'date_of_birth',
        'category',
        'vehicle',
        'permit_id',
        'residence_permit_id',
        'criminal_record',
        'country_of_residence_country_id',
        'commune',
        'open_professions',
        'type_profession_id',
        'contract_type',
        'responsibility_candidate_id',
        'work_rate',
        'native_language',
        'fluent_languages',
        'intermediate_languages',
        'basic_languages',
        'profession_1',
        'profession_2',
        'profession_3',
        'profession_4',
        'profession_5',
        'duration_1',
        'duration_2',
        'duration_3',
        'duration_4',
        'duration_5',
        'cv_file_id',
        'work_certificates_file_id',
        'study_certificates_file_id',
        'company_name',
        'website'
    ];

    
}
