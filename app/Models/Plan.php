<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Model;
use MongoDB\Laravel\Eloquent\Model;


class Plan extends Model
{
    use HasFactory;
    protected $connection = 'mongodb';

    // protected $fillable = ['name', 'slug', 'stripe_product_id', 'stripe_price_id', 'price','show_name','description_html','duration_in_days'];
    protected $fillable = [
        'name', 
        'slug', 
        'stripe_product_id', 
        'stripe_price_id', 
        'price',
        'currency' ,
        'show_name',
        'description_html',
        'duration_in_days',
    ];
}
