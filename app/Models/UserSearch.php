<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use MongoDB\Laravel\Eloquent\Model;


class UserSearch extends Model
{
    use HasFactory;

    protected $connection = 'mongodb';

    protected $fillable = [
        'user_id',
        'search_parameters',
        'results_count',
    ];

    protected $casts = [
        'search_parameters' => 'json',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}