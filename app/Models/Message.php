<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Model;
use MongoDB\Laravel\Eloquent\Model; 

class Message extends Model
{
    use HasFactory;

    protected $fillable = ['conversation_id', 'sender_id', 'content', 'is_read'];

    public function conversation()
    {
        // return $this->belongsTo(Conversation::class);
        return Conversation::find($this->conversation_id);
    }

    public function sender()
    {
        // return $this->belongsTo(User::class, 'sender_id');
        return User::find($this->sender_id);
    }
}
