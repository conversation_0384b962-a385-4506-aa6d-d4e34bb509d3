<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use MongoDB\Laravel\Eloquent\Model;


class Priority extends Model
{

    protected $connection = 'mongodb';

    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'value',
        'is_default'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'value' => 'integer',
        'is_default' => 'boolean'
    ];

    /**
     * Get the default priority
     */
    public static function getDefault()
    {
        return self::where('is_default', true)->first();
    }

    /**
     * Scope a query to only include active priorities.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->orderBy('value');
    }
}