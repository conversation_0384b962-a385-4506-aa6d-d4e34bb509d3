<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Model;
use MongoDB\Laravel\Eloquent\Model;

class File extends Model
{
    use HasFactory;

    protected $connection = 'mongodb';

    protected $fillable = [
        'path',
        'full_path',
        'url',
        'type',
        'name',
        'original_name',
        'extension',
        'usage',
        'user_id',
    ];
}
