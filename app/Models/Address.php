<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Model;
use MongoDB\Laravel\Eloquent\Model;

class Address extends Model
{
    use HasFactory;

    protected $connection = 'mongodb';
    protected $fillable = [
        'user_id',
        'lat',
        'log',
        'name',
        'place_id',
        'city',
        'country',
        'line1',
        'state',
        'postal_code'
    ];


    /**
     * Récupère le nom de l'adresse pour un user_id donné
     *
     * @param string $userId
     * @return string|null
     */
    public static function getNameByUserId(string $userId): ?string
    {
        $address = self::where('user_id', $userId)->first();

        return $address ? $address->name : null;
    }
}
