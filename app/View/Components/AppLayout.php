<?php

namespace App\View\Components;

use App\Models\ConfigGlobalApp;
use App\Models\Subscription;
use Illuminate\View\Component;
use Illuminate\View\View;

class AppLayout extends Component
{
    /**
     * Get the view / contents that represents the component.
     */
    public function render(): View
    {
        $configGlobal = ConfigGlobalApp::where('name', 'day_free_after_publish')->first();
        // dd($configGlobal->value);

        if ($configGlobal) {
            $dateExpiration = $configGlobal->value;
            $dateNow = now();

            // Vérification si la date d'expiration est passée
            $date_publish_is_expired = $dateExpiration < $dateNow;

            // Formatage de la date si elle n'est pas expirée
            $dateExpirationFormatted = $date_publish_is_expired ? null : $dateExpiration->toIso8601String();
        } else {
            // Si pas de configuration trouvée, considérer comme expiré
            $date_publish_is_expired = true;
            $dateExpirationFormatted = null;
        }

        $displayBandeau = ConfigGlobalApp::where('name', 'display_bandeau')->first();

        $subscriptionIsActive = Subscription::where('user_id', auth()->user()->id)
            ->where('stripe_status', 'active')
            ->exists();

        // dd($date_publish_is_expired);
        $dateHeureFinFreePeriod = $configGlobal->value;
        $dateHeureFinFreePeriod = $dateHeureFinFreePeriod->locale('fr')->isoFormat('LL [à] HH[h]mm');
        return view('layouts.app', [
            'publishIsExpired' => $date_publish_is_expired,
            'dateExpirationPublish' => $dateExpirationFormatted,
            'subscriptionIsActive' => $subscriptionIsActive,
            'displayBandeau' => $displayBandeau,
            'dateHeureFinFreePeriod' => $dateHeureFinFreePeriod
        ]);
    }
}
