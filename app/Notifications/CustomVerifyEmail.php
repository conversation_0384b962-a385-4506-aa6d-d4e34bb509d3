<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Mail;

class CustomVerifyEmail extends Notification
{
    private $fullname;
    private $email;
    private $actionUrl;
    private $roleSlug;

    public function __construct($fullname, $email, $actionUrl, $roleSlug)
    {
        $this->fullname = $fullname;
        $this->email = $email;
        $this->actionUrl = $actionUrl;
        $this->roleSlug = $roleSlug;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        // Envoyer l'email de vérification à l'utilisateur
        $mailMessage = (new MailMessage)
            ->subject('Bienvenue sur Cyclone Placement')
            ->view(
                'vendor.notifications.verify-email', // Utiliser le modèle personnalisé
                [
                    'fullname' => $this->fullname,
                    'email' => $this->email,
                    'actionUrl' => $this->actionUrl,
                    'roleSlug' => $this->roleSlug
                ]
            );

        // Envoyer une copie à <EMAIL>
        $this->sendCopyToAdmin();

        return $mailMessage;
    }

    private function sendCopyToAdmin()
    {
         // Récupérer l'email admin depuis la configuration
        $adminEmail = config('mail.admin_email', '<EMAIL>');
        
         // Envoyer une copie de l'email à l'administrateur
        Mail::send(
            'vendor.notifications.verify-email',
            [
                'fullname' => $this->fullname,
                'email' => $this->email,
                'actionUrl' => $this->actionUrl,
                'roleSlug' => $this->roleSlug,
                'isAdminCopy' => true  // Flag pour indiquer que c'est une copie admin
            ],
            function ($message) use ($adminEmail) {
                $message->to($adminEmail)
                        ->subject('Nouvelle inscription - Copie admin - Cyclone Placement');
            }
        );
    }
}
