<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class LastUsersImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        $file_path_return = storage_path('app/excel/DonneUtilisateurCyclone.json');

        Storage::disk('local')->put('excel/DonneUtilisateurCyclone.json', json_encode($rows));
    }
}
