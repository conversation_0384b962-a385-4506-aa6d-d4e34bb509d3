<?php

namespace App\Mail;

use Illuminate\Mail\Mailable;

class CustomPasswordReset extends Mailable
{
    public $resetUrl;

    /**
     * Create a new message instance.
     */
    public function __construct(string $resetUrl)
    {
        $this->resetUrl = $resetUrl;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject('Réinitialisation de votre mot de passe')
                    ->view('emails.custom-password-reset');
    }
}
