<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ContactMail extends Mailable
{
    use Queueable, SerializesModels;

    public $name;
    public $email;
    public $field_activities;
    public $professions;
    public $other;
    public $attachment;
    public $isRecruter; // Ajout d'une propriété pour déterminer si l'utilisateur est un recruteur

    /**
     * Create a new message instance.
     */
    public function __construct($data, $isRecruter = false, $subject = null)
    {
        $this->name = $data['name'];
        $this->email = $data['email'];
        $this->field_activities = $data['field_activities'] ?? "";
        $this->professions = $data['professions'] ?? "";
        $this->other = $data['other'] ?? "";
        $this->attachment = $data['attachment'] ?? null;
        $this->isRecruter = $isRecruter;
        $this->subject = $subject ?? 'Nouveau message de contact sur le site - ' . env('APP_NAME');
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        // Personnaliser le sujet en fonction du type d'utilisateur
        if ($this->isRecruter && !empty($this->subject)) {
            $subject = $this->subject;
        }else{
            $subject = 'Nouveau message de contact sur le site - ' . env('APP_NAME');

        }

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'mail.contact',
        );
    }

    /**
     * Get the attachments for the message.
     */
    public function attachments(): array
    {
        if ($this->attachment) {
            return [$this->attachment];
        }

        return [];
    }
}