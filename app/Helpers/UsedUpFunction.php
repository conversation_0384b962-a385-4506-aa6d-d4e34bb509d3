<?php

namespace App\Helpers;

use App\Models\ConfigGlobalApp;
use App\Models\Like;
use App\Models\Role;
use App\Models\Subscription;
use Illuminate\Support\Facades\DB;

class UsedUpFunction
{

    /**
     * Get route name
     * @param string $allRoute // route('get-started') or route('introduce-your-self')
     */
    public static function breakSeeders($seederName, $seederClass): bool
    {

        // Vérifier si le seeder a déjà été exécuté
        if (DB::table('seeders')->where('name', $seederName)->exists()) {
            var_dump('Seeder already executed.' . $seederName);
            return true;
        } else
            return false;
    }

    public static function isLiked($candidate_id)
    {
        return Like::where('user_id', auth()->user()->id)
            ->where('candidate_id', $candidate_id)
            ->exists();
    }

    public static function getCategoryLabel($slug)
    {
        // current_profiles,retired,migrants,students
        $categories = [
            'current_profiles' => 'Profils actuels',
            'retired' => 'Retraités',
            'migrants' => 'Migrants',
            'students' => 'Etudiants'
        ];

        return $categories[$slug] ?? '';
    }

    public static function getContractTypeLabel($slug)
    {
        // cdi,cdd,interim,stage,call
        $contractTypes = [
            'cdi' => 'CDI',
            'cdd' => 'CDD',
            'interim' => 'Intérim',
            'stage' => 'Stage',
            'call' => 'Appel'
        ];

        // Si $slug est un tableau, traitez chaque élément
        if (is_array($slug)) {
            $labels = [];
            foreach ($slug as $singleSlug) {
                if (isset($contractTypes[$singleSlug])) {
                    $labels[] = $contractTypes[$singleSlug];
                }
            }
            return implode(', ', $labels); // Retourne les labels séparés par une virgule
        }

        // Si $slug est une chaîne de caractères, retournez le label correspondant
        return $contractTypes[$slug] ?? '';
    }

    public static function isCandidate()
    {
        $role = Role::where('name', 'candidate')->first();
        return auth()->user()->role_id == $role->id;
    }

    public static function isRecruter()
    {
        $role = Role::where('name', 'recruter')->first();
        return auth()->user()->role_id == $role->id;
    }
    public static function filterUsersByRadius(array $users, float $centerLat, float $centerLog, float $radius): array
    {
        $filteredUsers = [];

        foreach ($users as $user) {
            $distance = self::calculateDistance($centerLat, $centerLog, $user['lat'], $user['log']);
            if ($distance <= $radius) {
                $filteredUsers[] = $user;
            }
        }

        return $filteredUsers;
    }

    public static function calculateDistance(float $lat1, float $log1, float $lat2, float $log2): float
    {
        $earthRadius = 6371; // Rayon de la Terre en kilomètres

        $dLat = deg2rad($lat2 - $lat1);
        $dLog = deg2rad($log2 - $log1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
            sin($dLog / 2) * sin($dLog / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    public static function isSubscribed()
    {
        if (!auth()->check()) {
            return false;
        }

        // Vérifier l'abonnement actif
        $hasActiveSubscription = Subscription::where('user_id', auth()->user()->id)
            ->where('stripe_status', 'active')
            ->exists();

        // Si abonnement actif existe, retourner true directement
        if ($hasActiveSubscription) {
            return true;
        }

        // Vérifier si le bandeau est activé
        $displayBandeau = ConfigGlobalApp::where('name', 'display_bandeau')->first();

        // Si le bandeau est désactivé, retourner false
        if (!$displayBandeau || !$displayBandeau->value) {
            return false;
        }

        // Vérifier la période d'essai gratuite
        $expirationConfig = ConfigGlobalApp::where('name', 'day_free_after_publish')->first();

        // Si pas de configuration d'expiration, considérer comme expiré
        if (!$expirationConfig) {
            return false;
        }

        // Vérifier si la date d'expiration est encore valide
        $expirationDate = $expirationConfig->value;
        $isTrialActive = now() < $expirationDate;

        return $isTrialActive;
    }
}
