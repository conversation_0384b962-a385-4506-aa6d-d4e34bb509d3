<?php

namespace App\Helpers;

use App\Imports\LastUsersImport;
use App\Mail\UpdateUserAccount;
use App\Models\Address;
use App\Models\Civility;
use App\Models\Country;
use App\Models\FieldActivity;
use App\Models\Formation;
use App\Models\Language;
use App\Models\Permit;
use App\Models\Phone;
use App\Models\Profession;
use App\Models\ResidencePermit;
use App\Models\ResponsibilityCandidate;
use App\Models\Role;
use App\Models\TypeProfession;
use App\Models\User;
use App\Models\UserFieldActivity;
use App\Models\UserFormation;
use App\Models\UserPermit;
use App\Models\UserProfession;
use App\Models\UserTypeProfession;
use Carbon\Carbon;
use DateTime;
use Faker\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Maatwebsite\Excel\Facades\Excel;

class ExcelImportHelper {

    /**
     * Importer le fichier Excel déjà présent sur le serveur.
     */
    public static function importFromFileUsers()
    {
        $filePath = storage_path('app/excel/DonneUtilisateurCyclone.xlsx');

        if (!file_exists($filePath)) {
            dd('Le fichier Excel spécifié est introuvable.');
        }

        try {
            dump('Importation ...');
            Excel::import(new LastUsersImport, $filePath);

            ExcelImportHelper::initialize();

            return dump('Importation réussie.');
        } catch (\Exception $e) {
            dd('Erreur lors de l\'importation : ' . $e->getMessage());
        }
    }

    public static function initialize()
    {
        $rows = json_decode(file_get_contents(storage_path('app/excel/DonneUtilisateurCyclone.json')), true);

        self::userImport($rows);
    }

    public static function sendEmail()
    {
        foreach (User::where('instance_of', 'excel_import')->get() as $key => $user) {
            if ($user->sended_email_update_at) {
                continue;
            }
            $token = Password::createToken($user); // Génère un token
            $user->reset_token = $token;          // Stocke le token dans la base de données
            $user->save();                        // Sauvegarde l'utilisateur
            Mail::to($user->email)->send(new UpdateUserAccount($user));
            dump('Email envoyé à ' . $user->email);
            $user->sended_email_update_at = now();
            $user->save();
        }
    }

    public static function userImport($rows)
    {

        $rows = collect($rows);

        $roles = [
            'Candidate' => Role::where('slug', 'candidate')->first()->id,
            'Employer' => Role::where('slug', 'recruter')->first()->id,
        ];

        $rows->each(function ($row) use ($roles) {

            if (empty($row['e_mail']) || empty($row['role'])) {
                return;
            }

            $name = (!$row['nom'] || $row['nom'] == '—Inconnu') ? $row['identifiant'] : $row['nom'];

            $dataToCreate = [
                'old_id' => $row['identifiant'],
                'name' => $name,
                'email' => strtolower($row['e_mail']),
                'role_id' => $roles[$row['role']] ?? $roles['Candidate'],
                'statut' => strtolower($row['statut']),
                'registered_at' => $row['registered_at']
            ];

            dump($dataToCreate);

            if ($row['role'] == 'Candidate') {
                self::createCandidate($dataToCreate);
            } else {
                self::createRecruter($dataToCreate);
            }
        });
    }

    public static function createCandidate($data)
    {
        $fieldActivityIds = FieldActivity::all()->pluck('id')->toArray();
        $professionIds = Profession::all()->pluck('id')->toArray();
        $formationIds = Formation::all()->pluck('id')->toArray();
        $permitIds = Permit::all()->pluck('id')->toArray();
        $residencePermitIds = ResidencePermit::all()->pluck('id')->toArray();
        $typeProfessionIds = TypeProfession::all()->pluck('id')->toArray();
        $responsibilityCandidateIds = ResponsibilityCandidate::all()->pluck('id')->toArray();
        $countryOfResidenceIds = Country::all()->pluck('id')->toArray();
        $languagesIds = Language::all()->pluck('id')->toArray();

        $fullname = $data['name'];
        if (str_contains($fullname, ' ')) {
            list($first_name, $last_name) = explode(' ', $fullname, 2);
        } else {
            $first_name = $fullname;
            $last_name = 'Aucun(e)';
        }

        $faker = Factory::create();
        $password = $faker->regexify('[a-zA-Z0-9]{12}');

        if(User::where('email', $data['email'])->exists()) {
            return;
        }

        // Création de l'utilisateur
        $user = User::create([
            'name' => $first_name . ' ' . $last_name,
            'email' => $data['email'],
            'password' => Hash::make($password),
            'generated_password' => $password,
            'registered_at' => $data['registered_at'],
            'email_verified_at' => $data['statut'] == 'approved' ? now() : null,
            'role_id' => $data['role_id'],
            'is_suspend' => false,
            'instance_of' => 'excel_import',
        ]);

        dump($user->toArray());

        // Ajout du téléphone
        Phone::create([
            'number' => '078 123 45 67',
            'user_id' => $user->id,
        ]);

        // Associations multiples
        UserFieldActivity::create([
            'user_id' => $user->id,
            'field_activity_id' => $fieldActivityIds[0],
        ]);

        UserProfession::create([
            'user_id' => $user->id,
            'profession_id' => $professionIds[0],
        ]);

        UserFormation::create([
            'user_id' => $user->id,
            'formation_id' => $formationIds[0],
        ]);

        $permitSelected = [$permitIds[0]];
        foreach ($permitSelected as $permitId) {
            UserPermit::create([
                'user_id' => $user->id,
                'permit_id' => $permitId,
            ]);
        }

        $typeProfessionSelected = [$typeProfessionIds[0]];
        foreach ($typeProfessionSelected as $typeProfessionId) {
            UserTypeProfession::create([
                'user_id' => $user->id,
                'type_profession_id' => $typeProfessionId,
            ]);
        }

        $address = Address::create([
            'user_id' => $user->id,
            'name' => "Genève, Suisse",
            'lat' => "46.20427870810266",
            'log' => "6.141060842762101",
        ]);

        $civility = Civility::create([
            'user_id' => $user->id,
            'role_id' => $data['role_id'],
            'first_name' => $first_name,
            'last_name' => $last_name,
            'date_of_birth' => Carbon::parse('01/01/1990')->format('Y-m-d'),
            'category' => 'current_profiles',
            'vehicle' => false,
            'residence_permit_id' => $residencePermitIds[0],
            'criminal_record' => false,
            'country_of_residence_country_id' => $countryOfResidenceIds[0],
            'commune' => 'Genève',
            'open_professions' => false,
            'contract_type' => 'cdi',
            'responsibility_candidate_id' => $responsibilityCandidateIds[0],
            'work_rate' => '100',
            'native_language' => [$languagesIds[0]],
            'fluent_languages' => [$languagesIds[0]],
            'intermediate_languages' => [$languagesIds[0]],
            'basic_languages' => [$languagesIds[0]],
            'profession_1' => null,
            'profession_2' => null,
            'profession_3' => null,
            'profession_4' => null,
            'profession_5' => null,
            'duration_1' => null,
            'duration_2' => null,
            'duration_3' => null,
            'duration_4' => null,
            'duration_5' => null,
            'visibility' => "1",
        ]);
    }

    public static function createRecruter($data)
    {
        $fullname = $data['name'];
        if (str_contains($fullname, ' ')) {
            list($first_name, $last_name) = explode(' ', $fullname, 2);
        } else {
            $first_name = $fullname;
            $last_name = 'Aucun(e)';
        }

        $faker = Factory::create();
        $password = $faker->regexify('[a-zA-Z0-9]{12}');

        if(User::where('email', $data['email'])->exists()) {
            return;
        }

        $user = User::create([
            'name' => $first_name . ' ' . $last_name,
            'email' => $data['email'],
            'password' => Hash::make($password),
            'generated_password' => $password,
            'registered_at' => $data['registered_at'],
            'email_verified_at' => $data['statut'] == 'approved' ? now() : null,
            'role_id' => $data['role_id'],
            'is_suspend' => false,
            'instance_of' => 'excel_import',
        ]);

        dump($user->toArray());

        $civility = Civility::create([
            'user_id' => $user->id,
            'role_id' => $data['role_id'],
            'company_name' => 'Your Company',
            'website' => 'https://yourcompany.com',
        ]);

        Phone::create([
            'number' => '078 123 45 67',
            'user_id' => $user->id,
        ]);

        $address = Address::create([
            'user_id' => $user->id,
            'name' => "Genève, Suisse",
            'lat' => "46.20427870810266",
            'log' => "6.141060842762101",
        ]);
    }

}