<?php

namespace App\Helpers;

use App\Models\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File as FileFacade;

class FileHelper {
    public static function store($file, $usage, $user_id) {
        // Définir le chemin de destination directement dans /public/storage
        $destinationPath = public_path('storage');

        // Vérifier si le dossier existe et le créer si nécessaire
        if (!FileFacade::exists($destinationPath)) {
            FileFacade::makeDirectory($destinationPath, 0777, true); // Crée le dossier avec des permissions appropriées
        }

        // Récupérer le nom original du fichier
        $originalName = $file->getClientOriginalName();

        // Générer un nom unique pour le fichier
        $name = pathinfo($originalName, PATHINFO_FILENAME);
        $extension = $file->getClientOriginalExtension();
        $fileName = $name . '_' . time() . '.' . $extension;

        // Définir le chemin complet pour stocker le fichier
        $file->move($destinationPath, $fileName);

        // Le chemin relatif du fichier
        $storedPath = 'storage/' . $fileName;

        // Construire l'URL accessible publiquement
        $url = asset($storedPath); // Utilisation de asset() pour obtenir l'URL publique

        // Obtenir le type MIME du fichier
        $type = $file->getClientMimeType();

        // Créer une entrée dans la base de données
        $fileRecord = File::create([
            'path' => $storedPath,
            'full_path' => $destinationPath . '/' . $fileName,
            'url' => $url,
            'type' => $type,
            'name' => $fileName,
            'original_name' => $originalName,
            'extension' => $extension,
            'usage' => $usage,
            'user_id' => $user_id,
        ]);

        return $fileRecord;
    }
}
