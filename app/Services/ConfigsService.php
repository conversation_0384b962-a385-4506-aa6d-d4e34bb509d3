<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;

class ConfigsService
{
    /**
     * Récupère les configurations d'email depuis la base de données.
     *
     * @return array
     */
    public static function getMailConfig(): array
    {


            // $settings = EmailSetting::pluck('value', 'key_name')->toArray();
            // Get there the information from database seeder in Config global app

            $settings=[
                "MAIL_MAILER"=>"smtp",
                "MAIL_HOST"=>"mail.webagence-rte.site",
                "MAIL_PORT"=>"465",
                "MAIL_USERNAME"=>"<EMAIL>",
                "MAIL_PASSWORD"=>"3291treha@#Weba",
                "MAIL_ENCRYPTION"=>"ssl",
                "MAIL_FROM_ADDRESS"=>"<EMAIL>",
                "MAIL_FROM_NAME"=>env("APP_NAME"),

            ];

            // From .env file logics
            /*
                MAIL_MAILER=smtp
                MAIL_HOST=mail.webagence-rte.site
                MAIL_PORT=465
                MAIL_USERNAME=<EMAIL>
                MAIL_PASSWORD="3291treha@#Weba"
                MAIL_ENCRYPTION=ssl
                MAIL_FROM_ADDRESS="<EMAIL>"
                MAIL_FROM_NAME="${APP_NAME}"
            */

            return [
                'default' => $settings['MAIL_MAILER'],
                'mailers' => [
                    'smtp' => [
                        'transport' => 'smtp',
                        'host' => $settings['MAIL_HOST'],
                        'port' => $settings['MAIL_PORT'],
                        'encryption' => $settings['MAIL_ENCRYPTION'],
                        'username' => $settings['MAIL_USERNAME'],
                        'password' => $settings['MAIL_PASSWORD'],
                        'timeout' => null,
                    ],
                    'ses' => [
                        'transport' => 'ses',
                    ],
                    'mailgun' => [
                        'transport' => 'mailgun',
                    ],
                    'postmark' => [
                        'transport' => 'postmark',
                    ],
                    'sendmail' => [
                        'transport' => 'sendmail',
                        // 'path' => env('MAIL_SENDMAIL_PATH', '/usr/sbin/sendmail -bs -i'),
                    ],
                    'log' => [
                        'transport' => 'log',
                        // 'channel' => env('MAIL_LOG_CHANNEL'),
                    ],
                    'array' => [
                        'transport' => 'array',
                    ],
                    'failover' => [
                        'transport' => 'failover',
                        'mailers' => [
                            'smtp',
                            'log',
                        ],
                    ],
                ],
                'from' => [
                    'address' => $settings['MAIL_FROM_ADDRESS'],
                    'name' => $settings['MAIL_FROM_NAME'],
                ],
                'markdown' => [
                    'theme' => 'default',
                    'paths' => [
                        resource_path('views/vendor/mail'),
                    ],
                ],
            ];
    }

    public static function getCaptchaConfig():array
    {
        // Get all setting database by Config global captca

        $settings=[
            "NOCAPTCHA_SECRET"=>"6LcVClYqAAAAACs8p05tgkrfVm8m3cXG86Wt755I",
            "NOCAPTCHA_SITEKEY"=>"6LcVClYqAAAAAOth0hTq1lafMhQidZSMrw9rSkGR",
        ];


        // By .env
        // NOCAPTCHA_SECRET=6LcVClYqAAAAACs8p05tgkrfVm8m3cXG86Wt755I
        // NOCAPTCHA_SITEKEY=6LcVClYqAAAAAOth0hTq1lafMhQidZSMrw9rSkGR

        return [

            'secret' => $settings["NOCAPTCHA_SECRET"],
            'sitekey' => $settings["NOCAPTCHA_SITEKEY"] ,
            'options' => [
                'timeout' => 30,
            ],

        ];

    }

    public static function getStripeConfig():array
    {

        // Get value by database config global app

        $settings=[
            "STRIPE_KEY"=>"pk_test_51OYqNsFKK6JoGdxmMWzT2SX9IciQGeQSItvJ8TxyoaCpVdrMs5dnpSCy2sSaygudhqbsnkznIavIu2l1hzmYwzaz00zTk4g7hM",
            "STRIPE_SECRET"=>"sk_test_51OYqNsFKK6JoGdxm411OjVQKO6Lv3q2Ta6sg2ry9G5PNZXnEYzpNDE43lVXp0hVHesYlxC3gzF2VYd6Lv4ELB7Sb00smdPudEY",
            "STRIPE_WEBHOOK_SECRET"=>"whsec_eFg0JkzZjPIhNwfSzaESsQFYnKx9MPqm",
            "CASHIER_CURRENCY"=>"chf"
        ];

        /*
            CASHIER_CURRENCY="chf"
            STRIPE_KEY="pk_test_51OYqNsFKK6JoGdxmMWzT2SX9IciQGeQSItvJ8TxyoaCpVdrMs5dnpSCy2sSaygudhqbsnkznIavIu2l1hzmYwzaz00zTk4g7hM"
            STRIPE_SECRET="sk_test_51OYqNsFKK6JoGdxm411OjVQKO6Lv3q2Ta6sg2ry9G5PNZXnEYzpNDE43lVXp0hVHesYlxC3gzF2VYd6Lv4ELB7Sb00smdPudEY"
            STRIPE_WEBHOOK_SECRET="whsec_eFg0JkzZjPIhNwfSzaESsQFYnKx9MPqm"
        */
        return [

            'key'=>$settings['STRIPE_KEY'],
            'secret'=>$settings['STRIPE_SECRET'],

            'webhook' => [
                'secret' => $settings['STRIPE_WEBHOOK_SECRET'],
                'tolerance' =>  300,
            ],
            'currency'=>$settings['CASHIER_CURRENCY']
        ];
    }

    public static function getAdminMailConfig():string
    {


        // Get database from global var application

        $settings=[
            'ADMIN_MAIL'=>'<EMAIL>'
        ];

        return $settings['ADMIN_MAIL'];
    }
}
