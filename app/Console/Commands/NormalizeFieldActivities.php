<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\FieldActivity;

class NormalizeFieldActivities extends Command
{
    protected $signature = 'activities:normalize';
    protected $description = 'Met à jour le champ normalizedName de toutes les activités';

    public function handle()
    {
        $this->info('Mise à jour des activités...');

        $count = 0;
        $activities = FieldActivity::all();

        foreach ($activities as $activity) {
            $activity->normalizedName = FieldActivity::normalizeString($activity->name);
            $activity->save();
            $count++;
        }

        $this->info("{$count} activités mises à jour.");
    }
}
