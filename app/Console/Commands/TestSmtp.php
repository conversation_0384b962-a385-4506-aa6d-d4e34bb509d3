<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Mail\TestEmail;

class TestSmtp extends Command
{
    protected $signature = 'test:smtp1';
    protected $description = 'Test SMTP configuration';

    public function handle()
    {
        try {
            Mail::to('<EMAIL>')->send(new TestEmail());
            $this->info('Email sent successfully!');
        } catch (\Exception $e) {
            $this->error('Error sending email: ' . $e->getMessage());
        }
    }
}