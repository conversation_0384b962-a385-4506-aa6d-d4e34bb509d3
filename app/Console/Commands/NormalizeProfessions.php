<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Profession;

class NormalizeProfessions extends Command
{
    protected $signature = 'professions:normalize';
    protected $description = 'Met à jour le champ normalizedName de toutes les professions';

    public function handle()
    {
        $this->info('Mise à jour des professions...');

        $count = 0;
        $professions = Profession::all();

        foreach ($professions as $profession) {
            $profession->normalizedName = Profession::normalizeString($profession->name);
            $profession->save();
            $count++;
        }

        $this->info("{$count} professions mises à jour.");
    }
}
