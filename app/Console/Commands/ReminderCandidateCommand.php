<?php

namespace App\Console\Commands;

use App\Mail\ReminderCandidateMail;
use App\Models\Civility;
use App\Models\Role;
use App\Models\ReminderCandidate;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class ReminderCandidateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:reminder-candidate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Envoie des rappels périodiques aux candidats pour vérifier leur statut de recherche d\'emploi';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $candidateRole = Role::where('slug', 'candidate')->first();
        if (!$candidateRole) {
            $this->error('Role candidat non trouvé');
            return;
        }

        $users = User::where('role_id', $candidateRole->id)->get();
        $this->info("Traitement de " . $users->count() . " candidats");

        foreach ($users as $user) {
            $this->processCandidate($user);
        }

        $this->info('Traitement terminé');
    }

    /**
     * Traite un candidat selon la nouvelle logique de rappels
     */
    private function processCandidate(User $user)
    {
        $reminder_candidate = ReminderCandidate::where('user_id', $user->id)->first();

        if ($reminder_candidate) {
            $this->handleExistingReminder($user, $reminder_candidate);
        } else {
            $this->handleNewCandidate($user);
        }
    }

    /**
     * Gère un candidat avec un reminder existant
     */
    private function handleExistingReminder(User $user, ReminderCandidate $reminder_candidate)
    {
        // Si le candidat a refusé (search_work = false), on arrête les rappels
        if ($reminder_candidate->search_work === false) {
            $this->info("Candidat {$user->email} a refusé - pas de rappel");
            return;
        }

        // Si pas de réponse (search_work = null), masquer le profil et continuer les rappels
        if ($reminder_candidate->search_work === null) {
            $this->hideProfileIfNotHidden($user);
        }

        // Si pas de réponse (search_work = null) OU si a accepté (search_work = true)
        // On vérifie s'il faut envoyer un rappel
        if ($this->shouldSendReminder($user, $reminder_candidate)) {
            $this->sendReminderEmail($user, $reminder_candidate);
        }
    }

    /**
     * Gère un nouveau candidat (premier rappel)
     */
    private function handleNewCandidate(User $user)
    {
        // Premier rappel après 12 jours d'inscription
        if ($user->created_at->diffInDays(now()) >= 12) {
            $reminder_candidate = ReminderCandidate::create([
                'user_id' => $user->id,
                'search_work' => null,
                'last_reminder_sent_at' => now()
            ]);

            Mail::to($user->email)->send(new ReminderCandidateMail($reminder_candidate));
            $this->info("Premier rappel envoyé à {$user->email}");
        }
    }

    /**
     * Détermine s'il faut envoyer un rappel
     */
    private function shouldSendReminder(User $user, ReminderCandidate $reminder_candidate): bool
    {
        $lastReminderDate = $reminder_candidate->last_reminder_sent_at;

        // Si aucun rappel n'a été envoyé, utiliser la date de création du reminder
        if (!$lastReminderDate) {
            $lastReminderDate = $reminder_candidate->created_at;
        }

        // Envoyer un rappel tous les 14 jours
        return $lastReminderDate->diffInDays(now()) >= 14;
    }

    /**
     * Envoie l'email de rappel et met à jour la date
     */
    private function sendReminderEmail(User $user, ReminderCandidate $reminder_candidate)
    {
        try {
            Mail::to($user->email)->send(new ReminderCandidateMail($reminder_candidate));

            // Mettre à jour la date du dernier rappel
            $reminder_candidate->update([
                'last_reminder_sent_at' => now()
            ]);

            $this->info("Rappel envoyé à {$user->email}");
        } catch (\Exception $e) {
            $this->error("Erreur lors de l'envoi à {$user->email}: " . $e->getMessage());
        }
    }

    /**
     * Masque le profil du candidat s'il n'est pas déjà masqué
     */
    private function hideProfileIfNotHidden(User $user)
    {
        $civility = Civility::where('user_id', $user->id)->first();

        if ($civility && $civility->visibility !== '0') {
            $civility->visibility = '0';
            $civility->save();
            $this->info("Profil masqué pour {$user->email} (pas de réponse)");
        }
    }
}
