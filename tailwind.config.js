import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        "./node_modules/flowbite/**/*.js"
    ],
    theme: {
        extend: {
            fontFamily: {
                title: ['Jost', 'sans-serif'],
                content: ['Lato', 'sans-serif'],
            },
            colors: {
                textNormal: 'rgb(100, 102, 108)',
                primary: '#0BBBEF',
            },
        },
    },

    plugins: [
        forms,
        require('flowbite/plugin'),
    ],
};
