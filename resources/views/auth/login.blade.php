<x-guest-layout>
    <div class="container mx-auto my-10 flex justify-center">
        <div class=" md:w-2/3 mx-auto w-full">


            <!-- Session Status -->
            <x-auth-session-status class="mb-4" :status="session('status')" />

            <form method="POST" action="{{ route('login') }}">
                @csrf

                <!-- Email Address -->
                <div class="mb-4">
                    <x-input-label for="email" :value="__('Email*')" class="font-bold text-gray-700" />
                    <x-text-input id="email"
                        class="block w-full h-[50px] px-3 py-2 bg-[#F5F5F5] border border-gray-300 rounded-[4px] focus:outline-none focus:ring-[#0BBBEF] focus:border-[#0BBBEF]"
                        type="email" name="email" :value="old('email')" required autofocus autocomplete="username"
                        placeholder="<PERSON><PERSON>" />
                    <x-input-error :messages="$errors->get('email')" class="mt-2" />
                </div>

                <!-- Password -->
                <div class="mb-4">
                    <x-input-label for="password" :value="__('Mot de passe*')" class="font-bold text-gray-700" />
                    <x-text-input id="password"
                        class="block w-full h-[50px] px-3 py-2 bg-[#F5F5F5] border border-gray-300 rounded-[4px] focus:outline-none focus:ring-[#0BBBEF] focus:border-[#0BBBEF]"
                        type="password" name="password" required autocomplete="current-password"
                        placeholder="Mot de passe" />
                    <x-input-error :messages="$errors->get('password')" class="mt-2" />
                </div>

                <!-- Remember Me and Forgot Password -->
                <div class="flex items-center justify-between mb-4">
                    <label for="remember_me" class="inline-flex items-center">
                        <input id="remember_me" type="checkbox"
                            class="rounded border-gray-300 text-[#0BBBEF] focus:ring-[#0BBBEF]" name="remember">
                        <span class="ml-2 text-sm text-gray-600">{{ __('Se souvenir de moi') }}</span>
                    </label>

                    @if (Route::has('password.request'))
                        <a class="underline text-sm text-gray-600 hover:text-gray-900"
                            href="{{ route('password.request') }}">
                            {{ __('Mot de passe oublié ?') }}
                        </a>
                    @endif
                </div>

                <!-- Recaptcha Component -->
                <div class="mb-6">
                    <x-recaptcha />
                    @error('g-recaptcha-response')
                        <p class="text-red-600 text-sm mt-2">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Submit Button -->
                <button type="submit"
                    class="w-full h-[50px] bg-[#0BBBEF] hover:bg-[#0E7355] text-white py-2 px-4 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 font-bold text-center">
                    {{ __('Se connecter') }}
                </button>
            </form>
        </div>
    </div>
</x-guest-layout>
