<x-guest-layout>
    <div class="container mx-auto my-10 flex justify-center">
        <div class="w-full max-w-4xl p-6">
            <form method="POST" action="{{ route('register') }}">
                @csrf

                <!-- Email Address -->
                <div class="mb-4">
                    <label for="email" class="block text-sm font-medium text-gray-700">{{ __('Email*') }}</label>
                    <input id="email"
                        class="mt-1 block w-full h-12 max-w-[900px] px-3 py-2 bg-[#F5F5F5] border border-[#E5E5E5] rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                        type="email" name="email" placeholder="Email" required autocomplete="username" />
                    @error('email')
                        <p class="text-red-600 text-sm mt-2">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Password -->
                <div class="mb-4">
                    <label for="password"
                        class="block text-sm font-medium text-gray-700">{{ __('Mot de passe*') }}</label>
                    <input id="password"
                        class="mt-1 block w-full h-12 max-w-[900px] px-3 py-2 bg-[#F5F5F5] border border-[#E5E5E5] rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                        type="password" name="password" placeholder="Mot de passe" required autocomplete="new-password" />
                    @error('password')
                        <p class="text-red-600 text-sm mt-2">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Confirm Password -->
                <div class="mb-4">
                    <label for="password_confirmation"
                        class="block text-sm font-medium text-gray-700">{{ __('Confirmer le mot de passe*') }}</label>
                    <input id="password_confirmation"
                        class="mt-1 block w-full h-12 max-w-[900px] px-3 py-2 bg-[#F5F5F5] border border-[#E5E5E5] rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                        type="password" name="password_confirmation" placeholder="Confirmer le mot de passe" required autocomplete="new-password" />
                    @error('password_confirmation')
                        <p class="text-red-600 text-sm mt-2">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Recaptcha -->
                <div class="mb-4">
                    <x-recaptcha />
                    @error('g-recaptcha-response')
                        <p class="text-red-600 text-sm mt-2">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Terms and Conditions -->
                <div class="mb-4">
                    <input type="checkbox" name="terms" id="terms" class="mr-2">
                    <label for="terms" class="text-sm text-gray-600">Accepter les <a href="#"
                            class="text-blue-600 hover:underline">conditions générales d'utilisation</a></label>
                    <div class="text-xs text-gray-500 mt-2">
                        <small style="font-size: 12.25px; line-height: 2.5; color: #000000;">
                            • Les informations fournies par les candidats ne peuvent être utilisées qu’à des fins de
                            recrutement. En tant qu’employeur, vous vous engagez à traiter ces informations dans la plus
                            stricte confidentialité. Aucune donnée ne doit être transmise à des tiers sans le
                            consentement explicite du candidat. Tout manquement à cette obligation pourrait entraîner
                            des poursuites judiciaires conformément aux lois applicables en matière de protection des
                            données.<br>

                            • Dans le cadre d’un processus de recherche de candidats par l'intermédiaire de Cyclone
                            placement, le nom et les coordonnées de l’entreprise restent confidentiels. Les candidats ne
                            sont jamais informés que vous avez consulté leur profil. Cette restriction peut être levée
                            dès l’instant où vous le jugez utile ou nécessaire, par exemple pour une convocation à un
                            entretien. L’initiative du contact relève toujours de l’employeur.<br>

                            • La politique de sélection de candidats sur notre plateforme vous offre la possibilité de
                            sélectionner un nombre indéterminé de profils dans le temps qui vous est imparti. Cette
                            approche vise à garantir une sélection fondée sur un comparatif de compétences,
                            d'expériences et de qualités inhérentes à chaque individu.<br>

                            • Afin de maintenir l'efficacité de notre outil et d'en garantir la pertinence, nous avons
                            mis en place un processus de notification régulier. Tous les quatorze jours, les candidats
                            reçoivent une invitation à actualiser et/ou à maintenir leur profil actif sur la plateforme.
                            En l’absence de réponse, le profil du candidat est temporairement désactivé. Toutefois,
                            Cyclone Placement ne garantit pas la disponibilité du candidat sur le marché du travail
                            durant l’intervalle du temps imparti de relance dès lors qu’il a été sélectionné par vos
                            soins pendant la procédure de recherche.<br>

                            • Nous respectons scrupuleusement les termes de la nouvelle Loi sur la Protection des
                            données. Conformément à cette législation, nous avons mis en place des mesures rigoureuses
                            pour garantir la confidentialité, l'intégrité et la sécurité des informations personnelles
                            des candidats et des entreprises. Nous veillons à obtenir le consentement éclairé des
                            parties pour la collecte, le traitement, la diffusion et le stockage des données les
                            concernant.<br>

                            • En tant que plateforme de services dédiée à l'emploi, Cyclone placement est soumis à la
                            Loi fédérale sur le Service de l'emploi (LSE). Nous sommes au bénéficie d’une autorisation
                            de placement privé et transfrontalier. En vertu de cette législation, nous nous engageons à
                            respecter les normes fédérales en vigueur visant à protéger les demandeurs d’emploi.
                        </small>
                    </div>
                    @error('terms')
                        <p class="text-red-600 text-sm mt-2">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Submit Button -->
                <div class="flex justify-center">
                    <button type="submit"
                        class="w-full h-12 max-w-[900px] bg-[rgba(11,187,239,0.8)] text-white py-3 px-4 rounded-md hover:bg-[#0e7355] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 text-left font-bold">
                        {{ __('S\'inscrire') }}
                    </button>
                </div>

            </form>
        </div>
    </div>
</x-guest-layout>
