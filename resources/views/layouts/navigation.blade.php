<nav x-data="{ open: false }" class="bg-white dark:bg-gray-800 border-b border-gray-100 dark:border-gray-700">
    <!-- Primary Navigation Menu -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex">
                <!-- Logo -->
                <div class="shrink-0 flex items-center">
                    <a href="{{ route('candidate.dashboard') }}">
                        <x-application-logo class="block h-9 w-auto fill-current text-gray-800 dark:text-gray-200" />
                    </a>
                </div>
            </div>

            <!-- Navigation Links -->
            <div class="hidden space-x-8 sm:-my-px sm:ms-10 sm:flex sm:justify-center">
                @if (Auth::user()?->getRoleSlugAttribute() === 'candidate')
                    <x-nav-link :href="route('candidate.dashboard')" :active="Str::startsWith(request()->route()->getName(), 'candidate')" class="uppercase">
                        {!! __('layouts_navigation.tableau_de_bord') !!}
                    </x-nav-link>
                    <x-nav-link :href="route('contact')" :active="request()->routeIs('contact')" class="uppercase">
                        {!! __('layouts_navigation.proposition_s_d_amxlioration_s_') !!}
                    </x-nav-link>
                @endif

                @if (Auth::user()?->getRoleSlugAttribute() === 'recruter')
                    <x-nav-link :href="route('recruter.dashboard')" :active="request()->routeIs('recruter.dashboard')" class="uppercase">
                        {!! __('layouts_navigation.tableau_de_bord') !!}
                    </x-nav-link>
                    <x-nav-link :href="route('recruter.candidate-selected') . '?sortedBy=liked'" :active="request()->is(route('recruter.candidate-selected') . '?sortedBy=liked')" :active="request()->routeIs('selected.candidates')" class="uppercase">
                        {!! __('components_recruter-dashboard-layout.candidat_e_s_sxlectionnx_e_s') !!}
                    </x-nav-link>
                    <x-nav-link :href="route('contact')" :active="request()->routeIs('contact')" class="uppercase">
                        {!! __('layouts_navigation.proposition_s_d_amxlioration_s_') !!}
                    </x-nav-link>
                @endif
            </div>

            <!-- Settings Dropdown -->
            <div class="hidden sm:flex sm:items-center sm:ms-6">
                <x-dropdown align="right" width="48">
                    <x-slot name="trigger">
                        <div class="flex items-center gap-4">
                            @if (Auth::user()?->getRoleSlugAttribute() === 'candidate')
                                <a href="{{ route('candidate.messages') }}"
                                    class="relative text-gray-500 bg-white hover:text-gray-700">
                                    <span style="display: none;" id="messages-count"
                                        class="absolute top-0 left-7 px-[6px] py-[1px] bg-red-500 text-white font-bold text-[9px] rounded-full">8</span>
                                    <div
                                        class="flex justify-center items-center text-center h-9 w-9 rounded-full border-none mr-3">
                                        <i class="fa-solid fa-message"></i>
                                    </div>
                                </a>
                            @endif
                            @if (\App\Helpers\UsedUpFunction::isSubscribed())
                                @if (Auth::user()?->getRoleSlugAttribute() === 'recruter')
                                    @php

                                    @endphp
                                    <a href="{{ route('recruter.messages') }}"
                                        class="relative text-gray-500 bg-white hover:text-gray-700">
                                        <span style="display: none;" id="messages-count"
                                            class="absolute top-0 left-7 px-[6px] py-[1px] bg-red-500 text-white font-bold text-[9px] rounded-full">8</span>
                                        <div
                                            class="flex justify-center items-center text-center h-9 w-9 rounded-full border-none mr-3">
                                            <i class="fa-solid fa-message"></i>
                                        </div>
                                    </a>
                                @endif
                            @endif
                            <button
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none transition ease-in-out duration-150">
                                <div class="flex items-center gap-4">
                                    <img class="h-8 w-8 rounded-full border"
                                        src="{{ Auth::user()?->civility()?->photo ?? asset('images/user.png') }}"
                                        alt="Photo" />
                                    <span>{{ Auth::user()?->fullname() }}</span>
                                </div>

                                <div class="ms-1">
                                    <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </button>
                        </div>
                    </x-slot>

                    <x-slot name="content">

                        @if (Auth::user()?->getRoleSlugAttribute() === 'candidate')
                            <x-dropdown-link :href="route('candidate.dashboard')">
                                <i class="fas fa-user-circle mr-2"></i> {!! __('components_candidate-dashboard-layout.profil') !!}
                            </x-dropdown-link>
                            <x-dropdown-link :href="route('candidate.messages')">
                                <i class="fas fa-comments mr-2"></i> {!! __('components_candidate-dashboard-layout.messages') !!}
                            </x-dropdown-link>
                            <x-dropdown-link :href="route('contact')">
                                <i class="fa-brands fa-ideal mr-2"></i> {!! __('layouts_navigation.proposition_s_d_amxlioration_s_') !!}
                            </x-dropdown-link>
                            <x-dropdown-link :href="route('candidate.update-password.index')">
                                <i class="fas fa-lock mr-2"></i> {!! __('components_candidate-dashboard-layout.mot_de_passe') !!}
                            </x-dropdown-link>
                            <x-dropdown-link :href="route('candidate.profile.delete.index')">
                                <i class="fas fa-trash-alt mr-2"></i> {!! __('components_candidate-dashboard-layout.effacer_le_profil') !!}
                            </x-dropdown-link>
                        @endif

                        @if (Auth::user()?->getRoleSlugAttribute() === 'recruter')
                            <x-dropdown-link :href="route('recruter.dashboard')">
                                <i class="fas fa-user-circle mr-2"></i> {!! __('components_candidate-dashboard-layout.profil') !!}
                            </x-dropdown-link>
                            <x-dropdown-link :href="route('recruter.candidate-selected') . '?sortedBy=liked'">
                                <i class="fas fa-heart mr-2"></i> {!! __('components_recruter-dashboard-layout.candidat_e_s_sxlectionnx_e_s') !!}
                            </x-dropdown-link>
                            <x-dropdown-link :href="route('recruter.candidate-selected')">
                                <i class="fas fa-search mr-2"></i> Recherche des candidats
                            </x-dropdown-link>
                            <x-dropdown-link :href="route('recruter.packages')">
                                <i class="fas fa-circle-dollar-to-slot mr-2"></i> {!! __('components_recruter-dashboard-layout.packages') !!}
                            </x-dropdown-link>
                            <x-dropdown-link :href="route('contact')">
                                <i class="fa-brands fa-ideal mr-2"></i> {!! __('layouts_navigation.proposition_s_d_amxlioration_s_') !!}
                            </x-dropdown-link>
                            <x-dropdown-link :href="route('recruter.update-password.index')">
                                <i class="fas fa-lock mr-2"></i> {!! __('components_candidate-dashboard-layout.mot_de_passe') !!}
                            </x-dropdown-link>
                            <x-dropdown-link :href="route('recruter.profile.delete.index')">
                                <i class="fas fa-trash-alt mr-2"></i> {!! __('components_candidate-dashboard-layout.effacer_le_profil') !!}
                            </x-dropdown-link>
                        @endif

                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <x-dropdown-link :href="route('logout')"
                                onclick="event.preventDefault();
                                            this.closest('form').submit();">
                                <i class="fas fa-sign-out-alt mr-2"></i> {!! __('components_candidate-dashboard-layout.dxconnexion') !!}
                            </x-dropdown-link>
                        </form>

                    </x-slot>
                </x-dropdown>

            </div>

            <!-- Hamburger -->
            <div class="-me-2 flex items-center sm:hidden">
                <button @click="open = ! open"
                    class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-900 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-900 focus:text-gray-500 dark:focus:text-gray-400 transition duration-150 ease-in-out">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path :class="{ 'hidden': open, 'inline-flex': !open }" class="inline-flex"
                            stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16" />
                        <path :class="{ 'hidden': !open, 'inline-flex': open }" class="hidden" stroke-linecap="round"
                            stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>


        <div class="fixed top-10 right-1 z-50 flex justify-center mt-4">
            {{-- @dd($publishIsExpired, $subscriptionIsActive, $displayBandeau->value) --}}
            @auth
                @if (\App\Helpers\UsedUpFunction::isRecruter())
                    @if (!$publishIsExpired && !$subscriptionIsActive && $displayBandeau->value)
                        {{--
                            La logique énonce qu'on affiche la banniere que si la date de publication + 60 jours n'est pas expirer
                            Et que l'abonnement actuelle n'est pas active(dans le cas, contraire on l'affiche évidemment, à condition aussi que not $publishIsExpired).
                        --}}
                        <div id="container-popup"
                            class="bg-[rgb(11,187,239)] border-l-4 border-[rgb(9,159,204)] text-white p-2 rounded-md shadow max-w-md">
                            <div class="flex justify-between gap-1 items-center">
                                <div>
                                    <h1 class="text-base font-medium leading-tight">
                                        Vous avez encore une période gratuite jusqu'au {{ $dateHeureFinFreePeriod }}, profitez en!
                                    </h1>
                                    <p class="text-xs mt-0.5">
                                        Temps restant :
                                        <span id="countdown" class="font-bold"></span>
                                    </p>
                                </div>
                                <div>
                                    <button id="close-popup"
                                        class="text-white text-base hover:text-primary hover:bg-white rounded-full px-2 py-0.5 duration-200">
                                        &times;
                                    </button>
                                </div>
                            </div>
                        </div>

                        <script>
                            document.addEventListener("DOMContentLoaded", function() {
                                const containerPopup = document.querySelector('#container-popup');
                                const closePopup = document.querySelector('#close-popup'); // Corrigé le sélecteur

                                if (containerPopup && closePopup) {
                                    closePopup.addEventListener('click', () => {
                                        containerPopup.remove();
                                    });
                                }

                                // Récupérer la date d'expiration correctement formatée
                                const expirationDate = new Date("{{ $dateExpirationPublish }}");

                                function updateCountdown() {
                                    if (document.getElementById("countdown") != null) {
                                        const now = new Date();
                                        const timeRemaining = expirationDate - now;

                                        if (timeRemaining <= 0) {
                                            document.getElementById("countdown").innerHTML = "Période expirée";
                                            clearInterval(interval);
                                            return;
                                        }

                                        // Calcul précis du temps restant
                                        const totalSeconds = Math.floor(timeRemaining / 1000);
                                        const days = Math.floor(totalSeconds / (3600 * 24));
                                        const hours = Math.floor((totalSeconds % (3600 * 24)) / 3600);
                                        const minutes = Math.floor((totalSeconds % 3600) / 60);
                                        const seconds = Math.floor(totalSeconds % 60);

                                        let countdownText = "";
                                        if (days > 0) countdownText += `${days} jours, `;
                                        if (hours > 0) countdownText += `${hours} heures, `;
                                        if (minutes > 0) countdownText += `${minutes} minutes, `;
                                        countdownText += `${seconds} secondes`;

                                        document.getElementById("countdown").innerHTML = countdownText;
                                    }
                                }

                                // Lancer immédiatement et toutes les secondes
                                updateCountdown();
                                const interval = setInterval(updateCountdown, 1000);
                            });
                        </script>
                    @endif
                @endif
            @endauth
        </div>


    </div>

    <!-- Responsive Navigation Menu -->
    <div :class="{ 'block': open, 'hidden': !open }" class="hidden sm:hidden">
        <div class="pt-2 pb-3 space-y-1">
            <x-responsive-nav-link :href="route('candidate.dashboard')" :active="request()->routeIs('dashboard')">
                {{ __('Dashboard') }}
            </x-responsive-nav-link>
        </div>

        <!-- Responsive Settings Options -->
        <div class="pt-4 pb-1 border-t border-gray-200 dark:border-gray-600">
            <div class="px-4">
                <div class="font-medium text-base text-gray-800 dark:text-gray-200">{{ Auth::user()?->name }}</div>
                <div class="font-medium text-sm text-gray-500">{{ Auth::user()?->email }}</div>
            </div>

            <div class="mt-3 space-y-1">
                <x-responsive-nav-link :href="route('profile.edit')">
                    {{ __('Profile') }}
                </x-responsive-nav-link>

                <!-- Authentication -->
                <form method="POST" action="{{ route('logout') }}">
                    @csrf

                    <x-responsive-nav-link :href="route('logout')"
                        onclick="event.preventDefault();
                                        this.closest('form').submit();">
                        {{ __('Deconnexion') }}
                    </x-responsive-nav-link>
                </form>
            </div>
        </div>
    </div>
</nav>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const messagesCount = document.getElementById('messages-count');

        function getCountMessagesNotRead() {
            fetch("{{ route('messages.count') }}")
                .then(response => response.json())
                .then(data => {
                    console.log(data);
                    if (data.count > 0) {
                        messagesCount.style.display = 'block';
                        messagesCount.innerText = data.count ?? 0;
                    } else {
                        messagesCount.style.display = 'none';
                    }
                });
        }

        getCountMessagesNotRead();
    });
</script>
