<x-app-layout>
    <style>
        body {
            overflow: hidden !important;
        }

        @media (max-width: 768px) {
            .sidebar {
                display: none;
                /* Masquer la sidebar par défaut en mobile */
            }

            .main-chat-area {
                width: 100% !important;
            }

            .chat-footer {
                width: 100% !important;
            }

            .user-conversations-list {
                height: calc(100vh - 150px) !important;
            }

            .chat-messages {
                height: calc(100vh - 200px) !important;
            }
        }
    </style>
    <div class="flex flex-col md:flex-row py-8 h-screen overflow-hidden">
        <!-- Sidebar -->
        <div id="sidebar" class="sidebar w-full md:w-1/4 bg-white border-r border-gray-300">
            <!-- Sidebar Header -->
            <header class="p-4 border-b border-gray-300 flex justify-between items-center bg-primary text-white">
                <h1 class="text-2xl font-semibold">Chat Web</h1>
            </header>

            <!-- Contact List -->
            <div id="userConversationsList" class="user-conversations-list overflow-y-auto h-screen p-3 pb-20 mb-20">
                <!-- Dynamic content will be loaded here -->
            </div>
        </div>

        <!-- Main Chat Area -->
        <div id="mainChatArea" class="main-chat-area flex-1 w-full md:w-3/4">
            <!-- Chat Header -->
            <header class="bg-white p-4 text-gray-700 flex items-center">
                <!-- Bouton de retour pour mobile -->
                <button id="backButton" class="md:hidden mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                </button>
                <h1 id="userMessageName" class="text-2xl font-semibold">
                    {{ $user_message?->fullname() }}
                </h1>
            </header>

            <!-- Chat Messages -->
            <div id="chatMessages" class="chat-messages h-screen overflow-y-auto p-4 pb-36">
                <!-- Dynamic messages will be loaded here -->
            </div>

            <!-- Chat Input -->
            <footer class="chat-footer bg-white border-t border-gray-300 p-4 fixed bottom-0 w-full md:w-3/4">
                <div class="flex items-center">
                    <input type="text" id="messageInput" placeholder="Type a message..."
                        class="w-full p-2 rounded-md border border-gray-400 focus:outline-none focus:border-blue-500">
                    <button id="sendMessageButton"
                        class="bg-primary text-white px-4 py-2 rounded-md ml-2">Envoyer</button>
                </div>
            </footer>
        </div>
    </div>

    <script>
        const conversationDefaultId = "{{ $conversation?->id ?? 'null' }}";
        let conversationSelectedId = conversationDefaultId;

        document.addEventListener('DOMContentLoaded', function() {
            const chatMessages = document.getElementById('chatMessages');
            const messageInput = document.getElementById('messageInput');
            const sendMessageButton = document.getElementById('sendMessageButton');
            const sidebar = document.getElementById('sidebar');
            const mainChatArea = document.getElementById('mainChatArea');
            const backButton = document.getElementById('backButton');

            // Fonction pour basculer entre la sidebar et le chat en mode mobile
            function toggleMobileView(showChat) {
                if (window.innerWidth <= 768) {
                    if (showChat) {
                        sidebar.style.display = 'none';
                        mainChatArea.style.display = 'block';
                    } else {
                        sidebar.style.display = 'block';
                        mainChatArea.style.display = 'none';
                    }
                }
            }

            // Afficher la sidebar par défaut en mode mobile
            toggleMobileView(false);

            // Gérer le clic sur le header de la sidebar pour afficher le chat
            const sidebarHeader = sidebar.querySelector('header');
            sidebarHeader.addEventListener('click', () => {
                toggleMobileView(true);
            });

            // Gérer le clic sur le bouton de retour pour revenir à la sidebar
            backButton.addEventListener('click', () => {
                toggleMobileView(false);
            });

            // Fonction pour charger les messages
            function loadMessages(conversationId) {
                conversationSelectedId = conversationId;
                let url_messages = "{{ route('messages.get', 'CONVERSATIONID') }}";
                const userId = "{{ auth()->id() }}";

                url_messages = url_messages.replace("CONVERSATIONID", conversationId);

                fetch(url_messages)
                    .then(response => response.json())
                    .then(messages => {
                        chatMessages.innerHTML = '';
                        messages.forEach(message => {
                            const messageElement = document.createElement('div');
                            messageElement.setAttribute('class', 'flex mb-4 cursor-pointer ' + (message
                                .sender_id === userId ? 'justify-end' : ''));
                            messageElement.innerHTML = `
                                <div class="flex max-w-96 ${message.sender_id === userId ? 'bg-primary text-white' : 'bg-white text-gray-700'} rounded-lg p-3 gap-3">
                                    <p>${message.content}</p>
                                </div>
                            `;
                            chatMessages.appendChild(messageElement);
                            chatMessages.scrollTop = chatMessages.scrollHeight;
                        });
                        const lastMessage = chatMessages.lastElementChild;
                        lastMessage.style.marginBottom = '100px';
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    });
            }

            if (conversationDefaultId !== 'null') {
                loadMessages(conversationDefaultId);
            }

            const userConversationsList = document.getElementById('userConversationsList');

            function loadUserConversations() {
                fetch("{{ route('messages.conversations') }}", {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        }
                    })
                    .then(response => response.json())
                    .then(conversations => {
                        userConversationsList.innerHTML = '';

                        conversations.forEach(conversation => {
                            const userElement = document.createElement('div');
                            if (conversation.last_message.is_read == false && conversation.last_message
                                .sender_id != "{{ auth()->id() }}") {
                                userElement.classList.add('flex', 'items-center', 'mb-4',
                                    'cursor-pointer',
                                    'hover:bg-gray-100', 'p-2', 'rounded-md', 'bg-gray-100');
                            } else {
                                userElement.classList.add('flex', 'items-center', 'mb-4',
                                    'cursor-pointer',
                                    'hover:bg-gray-100', 'p-2', 'rounded-md');
                            }

                            console.log(conversation);
                            console.log(conversation.user);
                            console.log(conversation.user.photo);

                            userElement.innerHTML = `
                                <div class="w-12 h-12 bg-gray-300 rounded-full mr-3">
                                    <img src="${conversation.user.photo ? conversation.user.photo : 'https://placehold.co/200x200'}" alt="User Avatar" class="w-12 h-12 rounded-full">
                                </div>
                                <div class="flex-1">
                                    <h2 class="text-lg font-semibold">${conversation.user.real_name ?? 'Utilisateur'}</h2>
                                    <p class="text-gray-600">${limitShowMessage(conversation.last_message.content)}</p>
                                </div>
                            `;

                            userElement.addEventListener('click', () => {
                                document.getElementById('userMessageName').innerText =
                                    conversation.user.real_name ?? 'Utilisateur';
                                conversationSelectedId = conversation.id;
                                loadMessages(conversation.id);
                                markMessageAsRead(conversation.id);
                                toggleMobileView(true); // Afficher le chat en mode mobile
                            });

                            userConversationsList.appendChild(userElement);
                        });
                    })
                    .catch(error => console.error('Error fetching user conversations:', error));
            }

            function markMessageAsRead(conversationId) {
                const url = "{{ route('messages.read', 'CONVERSATIONID') }}".replace('CONVERSATIONID',
                    conversationId);
                fetch(url, {
                        method: 'PATCH',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        }
                    })
                    .then(response => response.json())
                    .then(data => console.log(data))
                    .catch(error => console.error('Error:', error));
            }

            function limitShowMessage(message) {
                return message.length > 20 ? message.substring(0, 20) + '...' : message;
            }

            loadUserConversations();

            sendMessageButton.addEventListener('click', function() {
                const content = messageInput.value.trim();
                if (content === '') return;

                if (conversationSelectedId === null) {
                    alert('Please select a conversation to send a message');
                    return;
                }

                fetch("{{ route('messages.send') }}", {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            conversation_id: conversationSelectedId === 'null' ? null :
                                conversationSelectedId,
                            candidate_id: "{{ $user_message?->id }}",
                            content: content
                        })
                    })
                    .then(response => response.json())
                    .then(message => {
                        messageInput.value = '';
                        conversationSelectedId = message.conversation_id;
                        loadMessages(message.conversation_id);
                    })
                    .catch(error => console.error('Error:', error));
            });

            setInterval(() => {
                loadUserConversations();
                loadMessages(conversationSelectedId);
            }, 5000);
        });
    </script>
</x-app-layout>
