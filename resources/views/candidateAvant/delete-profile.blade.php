@if (\App\Helpers\UsedUpFunction::isCandidate())
    <x-candidate-dashboard-layout>
        <!-- Title -->
        <div class="border-l-4 rounded-sm border-primary px-6 py-2 mb-8">
            <h1 class="text-2xl font-semibold text-primary">{!! __('components_candidate-dashboard-layout.effacer_le_profil') !!}</h1>
        </div>

        <!-- Profile Form -->
        <section class="space-y-6">
            <form method="POST" action="{{ route('candidate.profile.delete.post') }}" enctype="multipart/form-data"
                class="flex justify-center">
                @csrf

                <!-- Delete Profile -->
                <div class="border border-gray-300 rounded p-10 max-w-3xl">

                    <!-- Session Status -->
                    <x-auth-session-status class="mb-4" :status="session('status')" />

                    <x-input-label for="password" class="text-xl">
                        {!! __('candidate_delete-profile.vous_xtes_sxr_vous_voulez_supprimer_votre_profil_') !!}
                    </x-input-label>
                    <p class="mt-4 mb-10">
                        {!! __('candidate_delete-profile.vous_ne_pourrez_pas_ion_de_votre_profil_') !!}
                    </p>
                    <small>
                        {!! __('candidate_delete-profile.veuillez_saisir_votr_ion_de_votre_profil_') !!}
                    </small>
                    <x-text-input id="password" class="block mt-1 w-full" type="password" name="password" required />
                    <x-input-error :messages="$errors->get('password')" class="mt-2" />

                    <div class="flex justify-end mt-10">
                        <x-danger-button type="submit">
                            {!! __('candidate_delete-profile.supprimer_le_profil') !!}
                        </x-danger-button>
                    </div>

            </form>
            <div class="feedback-button fixed bottom-4 right-4">
                <x-feedback-button-guest active="true" />
            </div>
        </section>
    </x-candidate-dashboard-layout>
@elseif(\App\Helpers\UsedUpFunction::isRecruter())
    <x-recruter-dashboard-layout>
        <!-- Title -->
        <div class="border-l-4 rounded-sm border-primary px-6 py-2 mb-8">
            <h1 class="text-2xl font-semibold text-primary">{!! __('components_candidate-dashboard-layout.effacer_le_profil') !!}</h1>
        </div>

        <!-- Profile Form -->
        <section class="space-y-6">
            <form method="POST" action="{{ route('candidate.profile.delete.post') }}" enctype="multipart/form-data"
                class="flex justify-center">
                @csrf

                <!-- Delete Profile -->
                <div class="border border-gray-300 rounded p-10 max-w-3xl">

                    <!-- Session Status -->
                    <x-auth-session-status class="mb-4" :status="session('status')" />

                    <x-input-label for="password" class="text-xl">
                        {!! __('candidate_delete-profile.vous_xtes_sxr_vous_voulez_supprimer_votre_profil_') !!}
                    </x-input-label>
                    <p class="mt-4 mb-10">
                        {!! __('candidate_delete-profile.vous_ne_pourrez_pas_ion_de_votre_profil_') !!}
                    </p>
                    <small>
                        {!! __('candidate_delete-profile.veuillez_saisir_votr_ion_de_votre_profil_') !!}
                    </small>
                    <x-text-input id="password" class="block mt-1 w-full" type="password" name="password" required />
                    <x-input-error :messages="$errors->get('password')" class="mt-2" />

                    <div class="flex justify-end mt-10">
                        <x-danger-button type="submit">
                            {!! __('candidate_delete-profile.supprimer_le_profil') !!}
                        </x-danger-button>
                    </div>

            </form>
            <div class="feedback-button fixed bottom-4 right-4">
                <x-feedback-button-guest active="true" />
            </div>
        </section>
    </x-recruter-dashboard-layout>
@endif
