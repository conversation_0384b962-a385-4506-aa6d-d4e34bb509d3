<x-guest-layout title="Cyclone Placement | Inscription Canditat">
    <div class="flex justify-center items-center w-full py-6">
        <div class="sm:w-[70rem] max-w-3xl bg-white p-10 rounded-sm">
            <!-- Session Status -->
            <x-auth-session-status class="mb-4" :status="session('status')" />

            <!-- Debug errors -->
            @if ($errors->any())
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('candidate.registerStore') }}" enctype="multipart/form-data" novalidate>
                @csrf

                <!-- Email -->
                <div class="mt-6">
                    <x-input-label for="email" value="{!! __('candidate_register.email_') !!}" />
                    <x-text-input id="email" class="block mt-1 w-full" type="email" name="email"
                        :value="old('email')" required autocomplete="username" />
                    <x-input-error :messages="$errors->get('email')" class="mt-2" />
                </div>

                <!-- Password -->
                <div class="mt-6">
                    <x-input-label for="password" value="{!! __('candidate_register.mot_de_passe_') !!}" />
                    <x-text-input id="password" class="block mt-1 w-full" type="password" name="password" required
                        autocomplete="new-password" />
                    <x-input-error :messages="$errors->get('password')" class="mt-2" />
                </div>

                <!-- Confirm Password -->
                <div class="mt-6">
                    <x-input-label for="password_confirmation" value="{!! __('candidate_register.confimer_le_mot_de_passe_') !!}" />
                    <x-text-input id="password_confirmation" class="block mt-1 w-full" type="password"
                        name="password_confirmation" required autocomplete="new-password" />
                    <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
                </div>

                <!-- Profile Picture -->
                <div class="mt-6">
                    <x-input-label for="profile_picture" value="{!! __('candidate_register.photo_de_profil_facultatif_') !!}" />
                    <x-upload-profile :id="'profile_picture'" />
                    <x-input-error :messages="$errors->get('profile_picture')" class="mt-2" />
                </div>

                <!-- First Name -->
                <div class="mt-6">
                    <x-input-label for="first_name" value="{!! __('candidate_register.prenom_') !!}" />
                    <x-text-input id="first_name" class="block mt-1 w-full" type="text" name="first_name"
                        :value="old('first_name')" required />
                    <x-input-error :messages="$errors->get('first_name')" class="mt-2" />
                </div>

                <!-- Last Name -->
                <div class="mt-6">
                    <x-input-label for="last_name" value="{!! __('candidate_register.nom_') !!}" />
                    <x-text-input id="last_name" class="block mt-1 w-full" type="text" name="last_name"
                        :value="old('last_name')" required />
                    <x-input-error :messages="$errors->get('last_name')" class="mt-2" />
                </div>

                <!-- Date of Birth -->
                <div class="mt-6">
                    <x-input-label for="date_of_birth" value="{!! __('candidate_register.date_de_naissance_') !!}" />
                    <x-text-input id="date_of_birth" class="block mt-1 w-full" type="text" name="date_of_birth"
                        :value="old('date_of_birth')" required />
                    <x-input-error :messages="$errors->get('date_of_birth')" class="mt-2" />
                </div>

                <!-- Category -->
                <div class="mt-6">
                    <x-input-label for="category" value="Catégorie *" />
                    <x-select-input id="category" class="block mt-1 w-full" name="category" required>
                        <option value="current_profiles" @selected(old('category') == 'current_profiles')>Profils courants</option>
                        <option value="retired" @selected(old('category') == 'retired')>Retraité(e)s</option>
                        <option value="migrants" @selected(old('category') == 'migrants')>Migrant(e)s</option>
                        <option value="students" @selected(old('category') == 'students')>Étudiant(e)s</option>
                    </x-select-input>
                    <x-input-error :messages="$errors->get('category')" class="mt-2" />
                </div>

                <!-- Phone Number -->
                <div class="mt-6">
                    <x-input-label for="phone" value="{!! __('candidate_register.numxro_de_txlxphone_') !!}" />
                    <x-text-input id="phone" class="block mt-1 w-full" type="tel" name="phone"
                        :value="old('phone')" required />
                    <x-input-error :messages="$errors->get('phone')" class="mt-2" />
                </div>

                <!-- Vehicle Holder -->
                <div class="mt-6">
                    <x-input-label for="vehicle" value="{!! __('candidate_register.titulaire_d_un_vxhicule') !!}" />
                    <x-select-input id="vehicle" name="vehicle" class="your-select-class block mt-1 w-full">
                        <option value="yes" @selected(old('vehicle') == 'yes')>Oui</option>
                        <option value="no" @selected(old('vehicle') == 'no')>Non</option>
                    </x-select-input>
                    <x-input-error :messages="$errors->get('vehicle')" class="mt-2" />
                </div>

                <!-- Driver's License -->
                <div class="mt-6">
                    <x-input-label for="permits" value="{!! __('candidate_register.permis_de_conduire_') !!}" />
                    <x-select-input id="permits" name="permits[]" class="block mt-1 w-full" multiple>
                        @foreach ($permits as $item)
                            <option value="{{ $item->id }}" @selected(in_array($item->id, old('permits', [])))>{{ $item->name }}
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('permits.*')" class="mt-2" />
                </div>

                <!-- Residence Permit -->
                <div class="mt-6">
                    <x-input-label for="residence" value="{!! __('candidate_register.suisse_permis_de_sxjour_') !!}" />
                    <x-select-input id="residence" name="residence" class="block mt-1 w-full" required>
                        <option value="suisse" @selected(old('residence') == 'suisse')>{!! __('candidate_register.suisse') !!}</option>
                        @foreach ($residencePermits as $item)
                            <option value="{{ $item->id }}" @selected(old('residence') == $item->id)>{{ $item->name }}
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('residence')" class="mt-2" />
                </div>

                <!-- Criminal Record -->
                <div class="mt-6">
                    <x-input-label for="criminal_record" value="{!! __('candidate_register.casier_judiciaire_vierge_facultatif_') !!}" />
                    <x-select-input id="criminal_record" name="criminal_record" class="block mt-1 w-full">
                        <option value="yes" @selected(old('criminal_record') == 'yes')>Oui</option>
                        <option value="no" @selected(old('criminal_record') == 'no')>Non</option>
                        <option value="skip" @selected(old('criminal_record') == 'skip')>{!! __('candidate_register.je_passe_cette_xtape') !!}</option>
                    </x-select-input>
                    <x-input-error :messages="$errors->get('criminal_record')" class="mt-2" />
                </div>

                <!-- Country of Residence -->
                <div class="mt-6">
                    <x-input-label for="country_of_residence" value="{!! __('candidate_register.pays_de_rxsidence_') !!}" />
                    <x-select-input id="country_of_residence" name="country_of_residence" class="block mt-1 w-full"
                        required>
                        <option value="" disabled selected>{{ __('Sélectionnez un Pays') }}</option>
                        @foreach ($countries as $item)
                            <option value="{{ $item->id }}" @if (old('country_of_residence') == $item->id) selected @endif
                                id-country="{{ $item->id }}">
                                {{ $item->name }}
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('country_of_residence')" class="mt-2" />
                </div>

                <!-- Commune canton -->
                <div class="mt-6">
                    <x-input-label for="commune" value="{!! __('candidate_register.commune_de_domicile') !!}" />
                    <x-select-input id="commune" name="commune" class="block mt-1 w-full" required>
                        <!-- Option par défaut pour sélectionner la région ou le canton -->
                        <option value="" disabled selected>{{ __('Sélectionner Région ou Canton') }}</option>


                    </x-select-input>
                    <x-input-error :messages="$errors->get('commune')" class="mt-2" />
                </div>

                <div class="mt-6">
                    <x-input-label for="address" value="Adresse de domicile (Rue,NPA,Ville) *" />
                    <x-text-input-address-completion id="address" name="address" class="block mt-1 w-full"
                        type="text" :value="old('address')" :disabled="old('commune') ? true : false" required />
                    <x-input-error :messages="$errors->get('address')" class="mt-2" />
                    <x-input-error :messages="$errors->get('latitude_longitude')" class="mt-2" />
                </div>

                <!-- Activity Fields -->
                <div class="mt-6 activity-domaine">
                    <x-input-label for="activity_fields" value="{!! __('candidate_register.domaine_s_d_activitx_') !!}" />
                    <x-select-input id="activity_fields" name="activity_fields[]" class="block mt-1 w-full" multiple>
                        @foreach ($fieldActivities as $item)
                            <option value="{{ $item->id }}" id-activity="{{ $item->id }}"
                                @selected($errors->any() ? false : in_array($item->id, old('activity_fields', [])))>
                                {{ $item->name }}
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('activity_fields.*')" class="mt-2" />
                </div>

                <!-- Desired Professions -->
                <div class="mt-6 professions_list hiddenc"> <!-- Ajoutez hidden ici -->
                    <x-input-label for="professions_list" value="{!! __('candidate_register.profession_s_recherchxe_s_') !!}" />
                    <x-select-input id="professions_list" name="professions_list[]" class="block mt-1 w-full"
                        multiple>
                    </x-select-input>
                    <x-input-error :messages="$errors->get('professions_list')" class="mt-2" />
                </div>

                <!-- Open to All Professions -->
                <div class="mt-6">
                    <x-input-label for="open_professions" value="{!! __('candidate_register.ouvert_x_toutes_professions_facultatif_') !!}" />
                    <x-select-input id="open_professions" name="open_professions" class="block mt-1 w-full" required>
                        <option value="yes" @selected(old('open_professions') == 'yes')>Oui</option>
                        <option value="no" @selected(old('open_professions') == 'no')>Non</option>
                    </x-select-input>
                    <x-input-error :messages="$errors->get('open_professions')" class="mt-2" />
                </div>

                <!-- Job Type -->
                <div class="mt-6">
                    <x-input-label for="job_types" value="{!! __('candidate_register.type_de_poste_recherchx_') !!}" />
                    <x-select-input id="job_types" name="job_types[]" class="block mt-1 w-full" multiple>
                        @foreach ($typeProfessions as $item)
                            <option value="{{ $item->id }}" @selected(in_array($item->id, old('job_types', [])))>{{ $item->name }}
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('job_types.*')" class="mt-2" />
                </div>

                <!-- Contract Type -->
                <div class="mt-6">
                    <x-input-label for="contract_type" value="{!! __('candidate_register.je_recherche_contrat_de_travail_') !!}" />
                    <x-select-input id="contract_type" name="contract_type[]" class="block mt-1 w-full" multiple>
                        <option value="call" @selected(in_array('call', old('contract_type', [])))>Travail sur appel</option>
                        <option value="cdi" @selected(in_array('cdi', old('contract_type', [])))>Contrat à durée indéterminée/fixe (CDI)
                        </option>
                        <option value="cdd" @selected(in_array('cdd', old('contract_type', [])))>Contrat à durée déterminée (CDD)</option>
                    </x-select-input>
                    <x-input-error :messages="$errors->get('contract_type.*')" class="mt-2" />
                </div>

                <!-- Availability -->
                <div class="mt-6">
                    <x-input-label for="availability" value="{!! __('candidate_register.disponibilitx_') !!}" />
                    <x-select-input id="availability" name="availability" class="block mt-1 w-full" required>
                        @foreach ($responsibilities as $item)
                            <option value="{{ $item->id }}" @selected(old('availability') == $item->id)>{{ $item->name }}
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('availability')" class="mt-2" />
                </div>

                <!-- Work Rate -->
                <div class="mt-6">
                    <x-input-label for="work_rate" value="{!! __('candidate_register.taux_d_activitx_') !!}" />
                    <x-select-input id="work_rate" name="work_rate" class="block mt-1 w-full" required>
                        @foreach (['100', '90-100', '80-100', '90', '80-90', '70-90', '80', '70-80', '60-80', '70', '60-70', '50-70', '60', '50-60', '40-60', '50', '40-50', '30-50', '40', '30-40', '20-40', '20-30', '30', '10-30', '20', '10-20', '10'] as $rate)
                            <option value="{{ $rate }}" @selected(old('work_rate') == $rate)>{{ $rate }}%
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('work_rate')" class="mt-2" />
                </div>

                <!-- Languages -->
                <div class="mt-6">
                    <x-input-label for="native_language" value="{!! __('candidate_register.langue_s_maternelle') !!}" />
                    <x-select-input id="native_language" name="native_language[]" class="block mt-1 w-full" multiple>
                        @foreach ($languages as $item)
                            <option value="{{ $item->id }}" @selected(in_array($item->id, old('native_language', [])))>{{ $item->name }}
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('native_language.*')" class="mt-2" />
                </div>

                <div class="mt-6">
                    <x-input-label for="fluent_languages" value="{!! __('candidate_register.langue_s_parlxe_s_couramment') !!}" />
                    <x-select-input id="fluent_languages" name="fluent_languages[]" class="block mt-1 w-full"
                        multiple>
                        @foreach ($languages as $item)
                            <option value="{{ $item->id }}" @selected(in_array($item->id, old('fluent_languages', [])))>{{ $item->name }}
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('fluent_languages.*')" class="mt-2" />
                </div>

                <div class="mt-6">
                    <x-input-label for="intermediate_languages" value="{!! __('candidate_register.langue_s_parlxe_s_avec_notion_intermxdiaire') !!}" />
                    <x-select-input id="intermediate_languages" name="intermediate_languages[]"
                        class="block mt-1 w-full" multiple>
                        @foreach ($languages as $item)
                            <option value="{{ $item->id }}" @selected(in_array($item->id, old('intermediate_languages', [])))>{{ $item->name }}
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('intermediate_languages.*')" class="mt-2" />
                </div>

                <div class="mt-6">
                    <x-input-label for="basic_languages" value="{!! __('candidate_register.langue_s_parlxe_s_avec_notion_de_base') !!}" />
                    <x-select-input id="basic_languages" name="basic_languages[]" class="block mt-1 w-full" multiple>
                        @foreach ($languages as $item)
                            <option value="{{ $item->id }}" @selected(in_array($item->id, old('basic_languages', [])))>{{ $item->name }}
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('basic_languages.*')" class="mt-2" />
                </div>

                <!-- Formation -->
                <div class="mt-6">
                    <x-input-label for="formations" value="{!! __('candidate_register.formations_') !!}" />
                    <x-select-input id="formations" name="formations[]" class="block mt-1 w-full" multiple>
                        @foreach ($formations as $item)
                            <option value="{{ $item->id }}" @selected(in_array($item->id, old('formations', [])))>{{ $item->name }}
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('formations.*')" class="mt-2" />
                </div>

                <!-- Work Experience -->
                @for ($i = 1; $i <= 5; $i++)
                    <div class="mt-6">
                        <x-input-label for="profession_{{ $i }}"
                            value="{!! __('candidate_register.profession_excercxe') !!} {{ $i }}" />
                        <x-text-input id="profession_{{ $i }}" class="block mt-1 w-full" type="text"
                            name="profession_{{ $i }}" :value="old('profession_' . $i)" />
                        <x-input-error :messages="$errors->get('profession_' . $i)" class="mt-2" />
                    </div>
                    <div class="mt-6">
                        <x-input-label for="duration_{{ $i }}"
                            value="{!! __('candidate_register.durxe_de_la_profession_excercxe') !!} {{ $i }}" />
                        <x-select-input id="duration_{{ $i }}" name="duration_{{ $i }}"
                            class="block mt-1 w-full">
                            <option></option>
                            @foreach (['1 semaine', '2 semaine', '3 semaine', 'Entre 1 mois et 6 mois', 'Entre 6 mois et 12 mois', 'Entre 2 ans et 5 ans', 'Entre 6 ans et 10 ans', 'Plus de 11 ans'] as $duration)
                                <option value="{{ $duration }}" @selected(old('duration_' . $i) == $duration)>{{ $duration }}
                                </option>
                            @endforeach
                        </x-select-input>
                        <x-input-error :messages="$errors->get('duration_' . $i)" class="mt-2" />
                    </div>
                @endfor

                <!-- Upload CV -->
                <div class="mt-6">
                    <x-input-label for="cv" value="CV joint" />
                    <x-upload-file :id="'cv'" />
                    <x-input-error :messages="$errors->get('cv')" class="mt-2" />
                </div>

                <!-- Certificates -->
                <div class="mt-6">
                    <x-input-label for="work_certificates" value="{!! __('candidate_register.certificat_s_de_travail_s_') !!}" />
                    <x-upload-file :id="'work_certificates'" />
                    <x-input-error :messages="$errors->get('work_certificates')" class="mt-2" />
                </div>

                <div class="mt-6">
                    <x-input-label for="study_certificates" value="{!! __('candidate_register.certificat_s_d_xtude_s_diplxme_s_') !!}" />
                    <x-upload-file :id="'study_certificates'" />
                    <x-input-error :messages="$errors->get('study_certificates')" class="mt-2" />
                </div>

                <!-- Recaptcha -->
                <div class="mt-6">
                    <x-recaptcha />
                    <x-input-error :messages="$errors->get('g-recaptcha-response')" class="mt-2" />
                </div>

                <!-- Terms and Conditions -->
                <div class="mt-6">
                    <div class="flex space-x-2 items-center">
                        <input type="checkbox" name="terms" id="terms" required>
                        <x-input-label for="terms">
                            {!! __('candidate_register.accepter_les') !!} <x-a for="terms" :href="route('generalConditions')"
                                target="_bank">{!! __('candidate_register.conditions_gxnxrales_d_utilisation') !!}</x-a>
                        </x-input-label>
                    </div>
                    <p class="text-xs mt-2">
                    <ul class="list-disc">
                        <li>{!! __('candidate_register.dans_le_cadre_de_la_lxgislation_') !!}</li>
                        <li>{!! __('candidate_register.l_utilisation_de_not_rofil_professionnel_') !!}</li>
                        <li>{!! __('candidate_register.seuls_les_employeurs_fins_de_recrutement_') !!}</li>
                        <li>{!! __('candidate_register.afin_de_maintenir_l_me_du_dxlai_lxgal_') !!}</li>
                    </ul>
                    </p>
                    <x-input-error :messages="$errors->get('terms')" class="mt-2" />
                </div>

                <!-- Submit Button -->
                <div class="flex items-center justify-end mt-6">
                    <x-primary-button class="ms-4 w-full">
                        {!! __('candidate_register.s_inscrire') !!}
                    </x-primary-button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {


            window.errorCount = {{ count($errors) }};

            flatpickr("#date_of_birth");

            const selectElement = document.getElementById("activity_fields");


            setTimeout(() => {
                // Traduction des jours
                const transDay = {
                    "sun": "Dim",
                    "mon": "Lun",
                    "tue": "Mar",
                    "wed": "Mer",
                    "thu": "Jeu",
                    "fri": "Ven",
                    "sat": "Sam"
                };

                const allDayToFlatpickr = document.querySelectorAll('.flatpickr-weekday');
                console.log("allDayToFlatpickr", allDayToFlatpickr, transDay);

                allDayToFlatpickr.forEach(day => {
                    const dayText = day.textContent.toLowerCase().trim(); // Normalisation du texte
                    if (transDay[dayText]) { // Vérifie si le jour existe dans la traduction
                        console.log("Translating day:", day.textContent, "=>", transDay[dayText]);
                        day.textContent = transDay[dayText]; // Applique la traduction
                    }
                });

                // Traduction des mois
                const transMonth = {
                    "january": "Janvier",
                    "february": "Février",
                    "march": "Mars",
                    "april": "Avril",
                    "may": "Mai",
                    "june": "Juin",
                    "july": "Juillet",
                    "august": "Août",
                    "september": "Septembre",
                    "october": "Octobre",
                    "november": "Novembre",
                    "december": "Décembre"
                };

                const allMonthToFlatpickr = document.querySelectorAll('.flatpickr-monthDropdown-month');
                console.log("allMonthToFlatpickr", allMonthToFlatpickr, transMonth);

                allMonthToFlatpickr.forEach(month => {
                    const monthText = month.textContent.toLowerCase()
                        .trim(); // Normalisation du texte
                    if (transMonth[monthText]) { // Vérifie si le mois existe dans la traduction
                        console.log("Translating month:", month.textContent, "=>", transMonth[
                            monthText]);
                        month.textContent = transMonth[monthText]; // Applique la traduction
                    }
                });
            }, 3000);

            new MultiSelectTag("permits", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
                onChange: function(values) {
                    console.log(values)
                }
            })

            new MultiSelectTag("activity_fields", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
                onChange: function() {
                    let selectedOptions = Array.from(selectElement.selectedOptions);
                    let activityIds = selectedOptions.map(option => option.getAttribute("id-activity"));

                    const professionContainer = document.querySelector('.professions_list');
                    console.log(activityIds)

                    if (activityIds.length > 0) {
                        professionContainer.classList.remove('hiddenc'); // Afficher l'élément
                    } else {
                        professionContainer.classList.add(
                            'hiddenc'); // Cacher l'élément si aucun élément n'est sélectionné
                    }

                    // Effectuer la requête AJAX pour récupérer les professions
                    fetch(`/get-all-profession?ids=${activityIds.join(',')}`)
                        .then(response => response.json())
                        .then(data => {
                            console.log(data);
                            let select = document.getElementById("professions_list");
                            select.innerHTML = '';

                            // Ajouter les options récupérées depuis l'API
                            data.forEach(item => {
                                let option = document.createElement("option");
                                option.value = item.id;
                                option.textContent = item.name;
                                select.appendChild(option);
                            });

                            // Appliquer MultiSelectTag
                            new MultiSelectTag("professions_list", {
                                rounded: true,
                                placeholder: 'Filtre ...',
                                tagColor: {
                                    textColor: '#0aaedb',
                                    borderColor: '#6ddaf8',
                                    bgColor: '#e7f9fe',
                                },
                                onChange: function(values) {
                                    console.log("values===============",values)
                                }
                            });




                            // Récupérer tous les div.multi-select-tag dans professions_list
                            const allTags = document.querySelectorAll(
                                '.mt-6.professions_list .multi-select-tag');

                            // Convertir la NodeList en tableau si besoin (pas obligatoire pour forEach, mais utile parfois)
                            const tagsArray = Array.from(allTags);

                            // console.log("tagsArray=======", tagsArray)

                            // Supprimer tous sauf le premier
                            tagsArray.slice(1).forEach(tag => tag.remove());


                        })
                        .catch(error => {
                            console.error('Erreur lors de la récupération des professions:', error);
                        });
                },
            });

            // new MultiSelectTag("desired_professions", {
            //     rounded: true,
            //     placeholder: 'Filtre ...',
            //     tagColor: {
            //         textColor: '#0aaedb',
            //         borderColor: '#6ddaf8',
            //         bgColor: '#e7f9fe',
            //     },
            // });



            new MultiSelectTag("native_language", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
            });
            new MultiSelectTag("fluent_languages", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
            });
            new MultiSelectTag("intermediate_languages", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
            });
            new MultiSelectTag("basic_languages", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
            });
            new MultiSelectTag("formations", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
            });
            new MultiSelectTag("job_types", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
                onChange: function(values) {
                    console.log(values)
                }
            })
            new MultiSelectTag("contract_type", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
                onChange: function(values) {
                    console.log(values)
                }
            });


            document.getElementById('country_of_residence').addEventListener('change', function() {
                // Récupère l'option sélectionnée
                const selectedOption = this.options[this.selectedIndex];

                // Récupère la valeur de l'attribut 'id-country' de l'option sélectionnée
                const countryId = selectedOption.getAttribute('id-country');

                // Vérifie si un pays a bien été sélectionné
                if (countryId) {
                    // Effectue une requête AJAX pour récupérer les régions par ID du pays
                    fetch(`/get-all-region?ids=${countryId}`)
                        .then(response => response.json())
                        .then(data => {
                            // Récupère le select des communes ou cantons
                            const communeSelect = document.getElementById('commune');
                            communeSelect.innerHTML = ''; // Réinitialise le select des communes

                            // Ajoute une option par défaut
                            const defaultOption = document.createElement('option');
                            defaultOption.value = '';
                            defaultOption.disabled = true;
                            defaultOption.selected = true;
                            defaultOption.textContent = '{{ __('Sélectionner Région ou Canton') }}';
                            communeSelect.appendChild(defaultOption);

                            // Ajoute les options de région au select des communes
                            data.forEach(region => {
                                const option = document.createElement('option');
                                option.value = region.name; // Utilise l'id de la région
                                option.textContent = region.name; // Affiche le nom de la région
                                communeSelect.appendChild(option);
                            });
                        })
                        .catch(error => {
                            console.error('Erreur lors de la récupération des régions:', error);
                        });
                }
            });


        });
    </script>


</x-guest-layout>
