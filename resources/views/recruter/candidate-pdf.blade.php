<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Profil du Candidat - PDF</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        .title {
            font-weight: bold;
            font-size: 14px;
            background-color: #f0f0f0;
            padding: 8px;
        }
        .table-section {
            width: 100%;
            margin-bottom: 15px;
            border-collapse: collapse;
        }
        .table-section td, .table-section th {
            padding: 8px;
            border: 1px solid #ddd;
        }
        .table-section-border-none {
            width: 100%;
            margin-bottom: 15px;
            border: none;
        }
        .table-section td, .table-section th {
            padding: 8px;
            border: none;
        }
        .section-header {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 10px;
            font-size: 16px;
            margin-bottom: 10px;
        }
        .profile-image {
            text-align: center;
            margin-bottom: 10px;
        }
        .profile-image img {
            width: 100px;
            height: auto;
            border-radius: 5%;
        }
    </style>
</head>

<body>

    <table class="table-section-border-none">
        <tr>
            <td>
                <div class="profile-image">
                    @if(!empty($logoBase64))
                        <img src="{{ $logoBase64 }}" alt="Logo" style="width:120px; height:auto;">
                    @endif
                </div>
            </td>
            <td>
            <div class="profile-image" style="width: 150px; height: 150px; overflow: hidden;">
                @if(!empty($photoBase64))
                    <img src="{{ $photoBase64 }}" alt="Profile Photo" style="width:100px; height:100px; object-fit:cover; border-radius:5%;">
                @else
                    <div style="width: 100%; height: 100%; background: #eee; display: flex; align-items: center; justify-content: center;">
                        Pas de photo disponible
                    </div>
                @endif
            </div>
            </td>
        </tr>
    </table>

    <!-- Informations personnelles -->
    <div class="section-header">Informations Personnelles</div>
    <table class="table-section">
        <tr>
            <td><strong>Prénom</strong></td>
            <td>{{ $civility->first_name }}</td>
        </tr>
        <tr>
            <td><strong>Nom</strong></td>
            <td>{{ $civility->last_name }}</td>
        </tr>
        <tr>
            <td><strong>Date de naissance</strong></td>
            <td>{{ $civility->date_of_birth }}</td>
        </tr>
        <tr>
            <td><strong>Catégorie</strong></td>
            <td>{{ \App\Helpers\UsedUpFunction::getCategoryLabel($civility->category) }}</td>
        </tr>
        <tr>
            <td><strong>Téléphone</strong></td>
            <td>{{ $phones->first()->number ?? 'Non spécifié' }}</td>
        </tr>
        <tr>
            <td><strong>Email</strong></td>
            <td>{{ $candidate->email }}</td>
        </tr>
    </table>

    <!-- Informations de résidence -->
    <div class="section-header">Lieu de Domicile</div>
    <table class="table-section">
        <tr>
            <td><strong>Pays de résidence</strong></td>
            <td>{{ $countryOfResidence?->name }}</td>
        </tr>
        <tr>
            <td><strong>Commune/Caton/Region</strong></td>
            <td>{{ $civility->commune }}</td>
        </tr>
        @if(auth()->user()->getAddress()?->id)
            <tr>
                <td><strong>Adresse</strong></td>
                <td>{{ $addressName }}</td>
            </tr>
        @endif
    </table>

    <!-- Domaine et professions recherchés -->
    <div class="section-header">Domaine(s) & Profession(s) Recherché(e)(s)</div>
    <table class="table-section">
        <tr>
            <td><strong>Domaine(s) d'activité</strong></td>
            <td>
                @foreach ($fieldActivities as $activity)
                    {{ $activity->name }}{{ !$loop->last ? ', ' : '' }}
                @endforeach
            </td>
        </tr>
        <tr>
            <td><strong>Profession(s) recherchée(s)</strong></td>
            <td>
                @foreach ($professions as $profession)
                    {{ $profession->name }}{{ !$loop->last ? ', ' : '' }}
                @endforeach
            </td>
        </tr>
    </table>

    <!-- Informations professionnelles -->
    <div class="section-header">Informations Professionnelles</div>
    <table class="table-section">
        <tr>
            <td><strong>Type de poste recherché</strong></td>
            <td>{{ $typeProfession?->name }}</td>
        </tr>
        <tr>
            <td><strong>Contrat de travail souhaité</strong></td>
            <td>{{ \App\Helpers\UsedUpFunction::getContractTypeLabel($civility->contract_type) }}</td>
        </tr>
        <tr>
            <td><strong>Disponibilité</strong></td>
            <td>{{ $responsibilityCandidate?->name }}</td>
        </tr>
        <tr>
            <td><strong>Taux d'activité</strong></td>
            <td>{{ $civility->work_rate }}%</td>
        </tr>
    </table>

    <!-- Langues parlées -->
    <div class="section-header">Langues</div>
    <table class="table-section">
        <tr>
            <td><strong>Langues maternelles</strong></td>
            <td>
                @foreach ($nativeLanguages as $language)
                    {{ $language?->name }}{{ !$loop->last ? ', ' : '' }}
                @endforeach
            </td>
        </tr>
        <tr>
            <td><strong>Langues courantes</strong></td>
            <td>
                @foreach ($fluentLanguages as $language)
                    {{ $language?->name }}{{ !$loop->last ? ', ' : '' }}
                @endforeach
            </td>
        </tr>
        <tr>
            <td><strong>Langues intermédiaires</strong></td>
            <td>
                @foreach ($intermediateLanguages as $language)
                    {{ $language?->name }}{{ !$loop->last ? ', ' : '' }}
                @endforeach
            </td>
        </tr>
        <tr>
            <td><strong>Langues de base</strong></td>
            <td>
                @foreach ($basicLanguages as $language)
                    {{ $language?->name }}{{ !$loop->last ? ', ' : '' }}
                @endforeach
            </td>
        </tr>
    </table>

    <!-- Formations -->
    <div class="section-header">Formations</div>
    <table class="table-section">
        <tr>
            <td><strong>Formations suivies</strong></td>
            <td>
                @foreach ($formations as $formation)
                    {{ $formation?->name }}{{ !$loop->last ? ', ' : '' }}
                @endforeach
            </td>
        </tr>
    </table>

    <!-- Expérience professionnelle -->
    <div class="section-header">Expérience(s) Professionnelle(s)</div>
    <table class="table-section">
        @for ($i = 1; $i <= 5; $i++)
            @if($civility->{'profession_' . $i})
                <tr>
                    <td><strong>Profession {{ $i }}</strong></td>
                    <td>{{ $civility->{'profession_' . $i} }}</td>
                </tr>
                <tr>
                    <td><strong>Durée</strong></td>
                    <td>{{ $civility->{'duration_' . $i} }}</td>
                </tr>
            @endif
        @endfor
    </table>

</body>
</html>
