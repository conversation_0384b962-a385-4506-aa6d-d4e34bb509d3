<x-recruter-dashboard-layout>
    {{-- @dd($addresseUser->country,$countries[1]->code) --}}
    <div class="max-w-7xl text-textNormal">
        <!-- Title -->
        <div class="border-l-4 rounded-sm border-primary px-6 py-2 mb-8">
            <h1 class="text-2xl font-semibold text-primary">MON ABONNEMENT</h1>

            @if (count($cards)>0)
                <!-- Bouton + avec texte au survol -->
                <div class="mt-0 text-left mr-1 relative group">
                    <button type="button" class="add-card-button text-3xl" onclick="window.location.href='{{ route('recruter.plan.show', ['slug' => $planSlug, 'new_card' => false]) }}'">
                        <i class="fa fa-arrow-circle-left"></i>
                    </button>
                    <!-- Texte affiché au survol -->
                    <span class="mt-8 absolute top-1/2 transform -translate-y-1/2 bg-gray-700 text-white text-sm px-2 py-1 rounded hidden group-hover:block">
                        Choisir carte existante
                    </span>
                </div>
            @endif

        </div>
        <style>
            #overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.5); /* Couleur semi-transparente */
            z-index: 9999; /* Place au-dessus de tout autre contenu */
            align-items: center;
            justify-content: center;
            }

            #card-button {
            background-color: rgb(11, 187, 239);
            color: white;
            padding: 0.5rem;
            border-radius: 0.5rem;
            width: 100%;
            transition: background-color 0.3s ease;
            }

            #card-button:hover {
                background-color: rgb(9, 150, 191); /* Couleur plus foncée pour le survol */
            }
        </style>
        <div id="overlay" class="hidden fixed inset-0 bg-white bg-opacity-50 flex items-center justify-center z-50">
            <div class="text-center">
                {{-- <div class="loader border-t-4 border-blue-500 rounded-full w-12 h-12 animate-spin mb-4"></div> --}}
                {{-- <p class="text-gray-700">Veuillez patienter, paiement en cours...</p> --}}
            </div>
        </div>
        <div class="container mx-auto px-4">
            <div class="mx-auto bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4 text-center">Formulaire de paiement Stripe</h2>
                <form id="stripe-form" class="space-y-4">

                    <!-- Section saisie de nom et adresse de paiement -->
                    <fieldset class="border border-gray-300 p-4 rounded-md">
                        {{-- <legend class="text-sm font-medium text-gray-700 mb-2">Nom et adresse de paiement</legend> --}}

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                            <div class="form-row">
                                <label for="card-name" class="block text-sm font-medium text-gray-700 mb-2">Nom du titulaire de la carte</label>
                                <input type="text" id="card-name" name="card_name" class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Votre nom" required>
                            </div>

                            <div class="form-row">
                                <label for="city" class="block text-sm font-medium text-gray-700 mb-2">City</label>
                                <input type="text" id="city" name="city" class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="City" value="{{isset($addresseUser->city) ? $addresseUser->city:''}}" required>
                            </div>

                            {{-- <div class="form-row">
                                <label for="country" class="block text-sm font-medium text-gray-700 mb-2">Pays</label>
                                <input type="text" id="country" name="country" class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Pays" value="{{$addresseUser->country}}" required>
                            </div> --}}

                            <div class="form-row">
                                <label for="country" class="block text-sm font-medium text-gray-700 mb-2">Pays</label>
                                <select id="country" name="country" class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>

                                    {{-- <option value="">Sélectionnez un pays</option> --}}
                                    @if(!isset($addresseUser->country))
                                    <option value="">Sélectionnez un pays</option>
                                    @endif

                                    @foreach ($countries as $country)
                                        @if(isset($addresseUser->country))
                                            <option value="{{$country->code}}" {{ $addresseUser->country == $country->code ? 'selected' : '' }}>{{$country->name}}</option>
                                        @else
                                            <option value="{{$country->code}}">{{$country->name}}</option>
                                        @endif
                                    @endforeach

                                </select>
                            </div>
                            <div class="form-row">
                                <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Adresse</label>
                                <input type="text" id="address" name="address" class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Adresse" value="{{isset($addresseUser->line1) ? $addresseUser->line1:''}}" required>
                            </div>

                            <div class="form-row">
                                <label for="state" class="block text-sm font-medium text-gray-700 mb-2">State</label>
                                <input type="text" id="state" name="state" class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="State" value="{{isset($addresseUser->state) ? $addresseUser->state:''}}" required>
                            </div>

                            <div class="form-row">
                                <label for="postal-code" class="block text-sm font-medium text-gray-700 mb-2">Code postal</label>
                                <input type="text" id="postal-code" name="postal_code" class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Code postal" value="{{isset($addresseUser->postal_code) ? $addresseUser->postal_code:''}}" required>
                            </div>
                        </div>
                    </fieldset>

                    <!-- Champ de saisie pour les informations de carte -->
                    <div class="form-row mt-6">
                        <label for="card-element" class="block text-sm font-medium text-gray-700 mb-2">Carte de crédit ou de débit</label>
                        <div id="card-element" class="p-3 border border-gray-300 rounded-md"></div>
                        <div id="card-errors" role="alert" class="text-center text-red-500 mt-2 text-sm"></div>
                    </div>

                    <!-- Bouton de paiement -->
                    <button type="button" id="card-button" class="mt-6 w-full text-white py-2 rounded-lg transition">
                        Payer
                    </button>

                    <!-- Loader -->
                    <div id="spinner-loader" class="flex items-center justify-center hidden">
                        <div class="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                    </div>
                </form>

            </div>
        </div>

        <script src="https://js.stripe.com/v3/"></script>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script>

            function inProcessus() {
                console.log('click in inProcessus');
                document.getElementById('card-errors').textContent = "";
                document.getElementById('spinner-loader').classList.remove('hidden');
                document.getElementById('card-button').classList.add('hidden');
                document.getElementById('overlay').classList.remove('hidden'); // Affiche l'overlay
            }

            function finishProcessus(disableButton = false) {
                document.getElementById('spinner-loader').classList.add('hidden');
                document.getElementById('card-button').classList.remove('hidden');
                document.getElementById('overlay').classList.add('hidden'); // Masque l'overlay
                if (disableButton) {
                    document.getElementById('card-button').classList.add('disabled');
                }
                else
                {
                    Swal.fire({
                        icon: 'error', // Indique une alerte d'échec
                        title: "Erreur durant le processus",
                        text: "Votre paiement d'abonnement a échoué. Veuillez réessayer.",
                        confirmButtonText: 'Ok'
                    });

                }
            }

            const stripe = Stripe("{{ config('stripe.key') }}", { locale: "{{ app()->getLocale() }}" });

            console.log("Stripe", stripe)


            
            const elements = stripe.elements();

            // Création de l'élément de carte avec style
            const cardElement = elements.create('card', {
                style: {
                    base: {
                        fontSize: '16px',
                        color: '#32325d',
                        '::placeholder': {
                            color: '#a0aec0',
                        }
                    }
                }
            });

            // Montage de l'élément de carte
            cardElement.mount('#card-element');

            // Gestionnaire de l'événement de soumission du formulaire
            const cardButton = document.getElementById('card-button');
            cardButton.addEventListener('click', async (e) =>
            {
                inProcessus();
                e.preventDefault();

                const cardName = document.getElementById('card-name').value;
                const city=document.getElementById('city').value;
                const country=document.getElementById('country').value;
                const address=document.getElementById('address').value;
                const state=document.getElementById('state').value;
                const postal_code=document.getElementById('postal-code').value;

                // Vérifiez si le nom est renseigné et addresses avant de soumettre le paiement
                if (!cardName) {
                    document.getElementById('card-errors').textContent = "Veuillez renseigner le nom du titulaire de la carte.";
                    finishProcessus();
                    return;
                }
                else if((!city)){
                    document.getElementById('card-errors').textContent = "Veuillez renseigner city.";
                    finishProcessus();
                    return;
                }
                else if(!country){
                    document.getElementById('card-errors').textContent = "Veuillez renseigner country.";
                    finishProcessus();
                    return;
                }
                else if(!address){
                    document.getElementById('card-errors').textContent = "Veuillez renseigner address.";
                    finishProcessus();
                    return;
                }
                // else if(!state){
                //     document.getElementById('card-errors').textContent = "Veuillez renseigner state.";
                //     finishProcessus();
                //     return;
                // }
                else if(!postal_code){
                    document.getElementById('card-errors').textContent = "Veuillez renseigner postal code.";
                    finishProcessus();
                    return;
                }


                // Send data name and adresses to databases

                axios.post("{{ route('recruter.plan.updateAddresse') }}", {
                    city: city,
                    country: country,
                    line1: address,
                    state: state,
                    postal_code: postal_code,
                },
                ).then(async function(response)
                {
                        console.log('Ici, success update addresse', response.data);
                        // Get setupintent by api
                        // Axios request subscription.getSetupIntent
                        axios.post("{{ route('recruter.plan.getSetupIntent') }}", {
                            // param1: 'valeur1',
                            // param2: 'valeur2'
                        },
                        ).then(async function(response)
                        {
                            console.log('Ici, success get setup intent', response.data);
                            const { setupIntent, error } = await stripe.confirmCardSetup(response.data.client_secret, {
                                payment_method: {
                                    card: cardElement,
                                    billing_details: {
                                        name: cardName,
                                    }
                                }
                            });

                            if (error) {
                                console.error(error);
                                document.getElementById('card-errors').textContent = error.message;
                                finishProcessus();
                            }
                            else
                            {
                                let paymentMethod = setupIntent.payment_method;
                                console.log('paymentMethod', paymentMethod);

                                // Launch axios request for payment
                                axios.post("{{route('recruter.plan.initiateSubscription')}}", {
                                    payment_method: paymentMethod,
                                    planSlug: "{{$planSlug}}",
                                    exist_card:false,
                                },
                                {}).then(async function(response)
                                {
                                    console.log('Ici, succès', response.data);

                                    if (response.data.status === 'success')
                                    {
                                        let subscription_id = response.data.subscription_id;
                                        console.log('Payment succès finally send axios post finish',response.data);

                                        axios.post("{{route('recruter.plan.subscriptionFinish')}}", {
                                            payment_method: paymentMethod,
                                            subscription_id: subscription_id,
                                            cardName:cardName,
                                            exist_card:false,
                                            planSlug:"{{$planSlug}}"
                                        }, {}
                                        ).then(function(response) {
                                            // alertMessage('{{ __('cont__plan.subscription_payed_successfully') }}', 'success');
                                            finishProcessus(true);
                                            // alert('Subscription finish need webhook active to finalise');
                                            Swal.fire({
                                                icon: 'success',
                                                title: 'Abonnement terminé',
                                                text: "Votre paiement d\'abonnement s'est bien effectué.",
                                                confirmButtonText: 'OK'
                                            });
                                            setTimeout(() => {
                                                window.location.href = "{{route('recruter.packages')}}";
                                            }, 2000);
                                        }).catch(function(error) {
                                            console.log('Error finish subscription', error);
                                            finishProcessus();
                                        });

                                    }
                                    else if (response.data.status === 'requires_action')
                                    {
                                        let subscription_id = response.data.subscription_id;
                                        const { error } = await stripe.confirmCardPayment(response.data.client_secret);

                                        if (error)
                                        {
                                            console.error('error confirmCardPayment 3DS', error);
                                            finishProcessus();
                                        } else
                                        {
                                            console.log('Payment succès 3DS finally send axios post finish');

                                            axios.post("{{route('recruter.plan.subscriptionFinish')}}", {
                                                payment_method: paymentMethod,
                                                subscription_id: subscription_id,
                                                cardName:cardName,
                                                exist_card:false,
                                                planSlug:"{{$planSlug}}"
                                            }, {}
                                            ).then(function(response) {
                                                finishProcessus(true);
                                                // alert('Subscription 3DS finish need webhook active to finalise', 'success');
                                                Swal.fire({
                                                    icon: 'success',
                                                    title: 'Abonnement terminé',
                                                    text: "Votre paiement d\'abonnement s'est bien effectué.",
                                                    confirmButtonText: 'OK'
                                                });
                                                setTimeout(() => {
                                                    window.location.href = "{{route('recruter.packages')}}";
                                                }, 2000);
                                            }).catch(function(error)
                                            {
                                                finishProcessus();
                                                console.log('Error finish subscription:', error);
                                            });
                                        }
                                    }
                                }).catch(function(error) {
                                    console.log('Ici, error initiate subscription');
                                    finishProcessus();
                                });

                            }

                        }).catch(function(error) {
                            console.log('Ici, error get setup intent',error);
                            finishProcessus();
                        });



                }).catch(function() {
                    console.log('Ici, error updateAddresse');
                    finishProcessus();
                });


            });
        </script>



    </div>

</x-recruter-dashboard-layout>
