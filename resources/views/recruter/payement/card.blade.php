<x-recruter-dashboard-layout>
    <div class="container mx-auto px-4">
        <style>
            /* Badge de carte par défaut */
            .default-badge {
                position: absolute;
                top: 10px;
                left: 10px;
                background-color: #4c51bf;
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 0.8em;
                font-weight: bold;
                text-align: center;
            }

            /* Styles pour le formulaire de sélection de carte */
            .card-selection-form {
                max-width: 400px;
                margin: 20px auto;
                padding: 20px;
                background-color: #f9f9f9;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
                font-family: Arial, sans-serif;
            }

            /* Styles pour chaque carte */
            .card-option {
                position: relative;
                margin-bottom: 15px;
                padding: 10px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                transition: box-shadow 0.3s ease;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
            }

            .card-option:hover {
                box-shadow: 0 4px 12px rgba(11, 187, 239, 0.2);
                border-color: #0bbbef;
            }

            /* Styles pour le texte et les informations de la carte */
            .card-label {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 10px;
                cursor: pointer;
                border-radius: 6px;
                transition: background-color 0.3s ease, box-shadow 0.3s ease;
            }

            .card-label:hover {
                background-color: #f0f8ff;
                box-shadow: 0 2px 8px rgba(11, 187, 239, 0.15);
            }

            .card-owner,
            .card-brand,
            .card-last-number {
                flex: 1;
                font-size: 1em;
                font-weight: 500;
                color: #333;
            }

            /* Boutons d'action */
            .action-buttons {
                display: flex;
                gap: 8px;
            }

            .delete-card-button,
            .set-default-card-button {
                width: 38px;
                height: 38px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0.5rem;
                cursor: pointer;
                font-size: 1.2em;
                color: white;
                transition: transform 0.2s ease;
            }

            .delete-card-button {
                background-color: #e53e3e;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .delete-card-button:hover {
                transform: scale(1.1);
                box-shadow: 0 4px 10px rgba(229, 62, 62, 0.2);
            }

            .set-default-card-button {
                background-color: #4c51bf;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .set-default-card-button:hover {
                transform: scale(1.1);
                box-shadow: 0 4px 10px rgba(76, 81, 191, 0.2);
            }

            /* Responsive pour petits écrans */
            @media (max-width: 600px) {
                .card-selection-form {
                    padding: 15px;
                }

                .card-label {
                    flex-direction: column;
                    align-items: flex-start;
                }

                .card-owner,
                .card-brand,
                .card-last-number {
                    margin-right: 0;
                    font-size: 0.85em;
                }
            }
        </style>

        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

        @if(count($cards)>0)
            <h1 class="text-3xl font-bold text-center text-primary mb-6">Liste des cartes</h1>
            {{-- @if (session('status'))
                <div class="alert alert-success">
                    {{ session('status') }}
                </div>
            @endif --}}

            @foreach ($cards as $card)
                <div class="card-option">
                    @if ($card->is_default_card)
                        <span class="default-badge">Par défaut</span>
                    @endif
                    <div class="card-list table w-full border-collapse">
                        <div class="table-row-group">
                            <div class="table-row border-b border-gray-200">
                                <!-- Colonne pour le nom du propriétaire de la carte -->
                                <div class="table-cell p-4 w-1/3">{{ $card->name }}</div>

                                <!-- Colonne pour la marque de la carte -->
                                <div class="table-cell p-4 w-1/6 text-center italic text-xl uppercase flex items-center justify-center space-x-2">
                                    <span>{{ $card->brand }}</span>

                                    @if ($card->brand === 'visa')
                                        <i class="fab fa-cc-visa text-blue-600"></i>
                                    @elseif ($card->brand === 'mastercard')
                                        <i class="fab fa-cc-mastercard text-red-600"></i>
                                    @elseif ($card->brand === 'amex')
                                        <i class="fab fa-cc-amex text-blue-800"></i>
                                    @elseif ($card->brand === 'discover')
                                        <i class="fab fa-cc-discover text-orange-600"></i>
                                    @elseif ($card->brand === 'diners')
                                        <i class="fab fa-cc-diners-club text-purple-600"></i>
                                    @elseif ($card->brand === 'jcb')
                                        <i class="fab fa-cc-jcb text-green-600"></i>
                                    @elseif ($card->brand === 'unionpay')
                                        <i class="fas fa-credit-card text-gray-600"></i> <!-- Utilisez une icône générique pour UnionPay si non disponible -->
                                    @else
                                        <i class="fas fa-credit-card text-gray-600"></i> <!-- Icône générique pour les autres marques -->
                                    @endif
                                </div>


                                <!-- Colonne pour le numéro de la carte -->
                                <div class="table-cell p-4 w-1/4 text-right">**** **** **** {{ $card->lastNumber }}</div>

                                <!-- Colonne pour les boutons d'action -->
                                <div class="table-cell p-4 w-1/6 text-right">
                                    <div class="flex justify-end space-x-2">
                                        @if (!$card->is_default_card)
                                        <button
                                            class="set-default-card-button rounded-full p-2 bg-blue-500 text-white hover:bg-blue-600 transition"
                                            onclick="confirmSetDefault('{{ route('recruter.card.setDefault', ['customerId' => $customerId, 'paymentMethodId' => $card->paymentMethodId]) }}')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        @endif
                                        <button
                                            class="delete-card-button rounded-full p-2 bg-red-500 text-white hover:bg-red-600 transition"
                                            onclick="confirmDelete('{{ route('recruter.card.delete', ['paymentMethodId' => $card->paymentMethodId]) }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            @endforeach

            <script>
                function confirmSetDefault(url) {
                    Swal.fire({
                        title: 'Définir cette carte par défaut?',
                        text: "Cette action établira cette carte comme votre moyen de paiement principal pour tous les paiements récurrents à venir.",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Oui, définir par défaut!',
                        cancelButtonText: 'Annuler'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = url;
                        }
                    });
                }

                @if(count($cards)==1)
                        let textToShow="Étant donné qu'il s'agit de votre seule carte de paiement, cette action annulera automatiquement votre abonnement pour le prochain cycle. Pour vous réabonner, vous devrez effectuer un nouveau paiement et donc ajouter une nouvelle carte";
                    @else
                        let textToShow="Cette action est irréversible, la carte sera supprimée de votre système de paiement des abonnements";
                    @endif


                function confirmDelete(url) {

                    Swal.fire({
                        title: 'Supprimer cette carte?',
                        text: textToShow,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Oui, supprimer!',
                        cancelButtonText: 'Annuler'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = url;
                        }
                    });
                }
            </script>
        @else
            <h1 class="text-3xl font-bold text-center text-primary mb-6">La liste des cartes de paiement est vide </h1>
        @endif


    </div>
</x-recruter-dashboard-layout>
