<x-recruter-dashboard-layout>
    {{-- @dd($addresseUser->country,$countries[1]->code) --}}
    <div class="max-w-7xl text-textNormal">
        <!-- Title -->
        <div class="border-l-4 rounded-sm border-primary px-6 py-2 mb-8">
            <h1 class="text-2xl font-semibold text-primary">MON ABONNEMENT</h1>
        </div>

        <style>

        #overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.5); /* Couleur semi-transparente */
            z-index: 9999; /* Place au-dessus de tout autre contenu */
            align-items: center;
            justify-content: center;
        }

        .card-selection-form {
            max-width: 400px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            font-family: Arial, sans-serif;
        }

        .form-title {
            text-align: center;
            font-size: 1.25em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }

        .card-option {
            position: relative;
            margin-bottom: 15px;
            padding: 10px;
            border: 2px solid #ccc;
            border-radius: 6px;
            transition: border-color 0.3s ease;
        }

        .card-option input[type="radio"]:checked + .card-label {
            border-color: rgb(11, 187, 239);
        }

        .card-label {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
        }

        .card-label:hover {
            background-color: #e0f7ff; /* Une couleur plus claire pour l'effet de survol */
            box-shadow: 0 0 6px rgba(11, 187, 239, 0.3);
        }

        .card-owner,
        .card-brand,
        .card-last-number {
            flex: 1;
            font-size: 0.9em;
            color: #666;
            margin-right: 10px;
        }

        input[type="radio"] {
            margin-left: auto;
        }

        .custom-radio {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid rgb(11, 187, 239);
            margin-right: 10px;
            position: relative;
            display: inline-block;
        }

        .add-card-button {
            bottom: 10px;
            right: 10px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgb(11, 187, 239);
            color: white;
            font-size: 1.5em;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .add-card-button:hover {
            background-color: rgb(9, 150, 191); /* Couleur de fond plus foncée au survol */
            transform: scale(1.1); /* Agrandit légèrement le bouton */
        }

        .add-card-button:active {
            transform: scale(0.95); /* Réduit légèrement le bouton au clic pour un effet de pression */
        }

        #card-button {
        background-color: rgb(11, 187, 239);
        color: white;
        padding: 0.5rem;
        border-radius: 0.5rem;
        width: 100%;
        transition: background-color 0.3s ease;
        }

        #card-button:hover {
            background-color: rgb(9, 150, 191); /* Couleur plus foncée pour le survol */
        }

        /* Media query for smaller screens */
        @media (max-width: 600px) {
            .card-selection-form {
                padding: 15px;
            }

            .form-title {
                font-size: 1.1em;
            }

            .card-label {
                flex-direction: column; /* Empile les éléments */
                align-items: flex-start; /* Aligne à gauche sur petit écran */
            }

            .card-owner,
            .card-brand,
            .card-last-number {
                margin-right: 0;
                font-size: 0.85em;
            }

            input[type="radio"] {
                margin-top: 10px; /* Espace pour le bouton radio en dessous */
                margin-left: 0;
            }
        }


        </style>
        <div id="overlay" class="hidden fixed inset-0 bg-white bg-opacity-50 flex items-center justify-center z-50">
            <div class="text-center">
                {{-- <div class="loader border-t-4 border-blue-500 rounded-full w-12 h-12 animate-spin mb-4"></div> --}}
                {{-- <p class="text-gray-700">Veuillez patienter, paiement en cours...</p> --}}
            </div>
        </div>

        <div class="container mx-auto px-4">
            <div class="mx-auto bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4 text-center">Paiement Stripe</h2>
                <form id="stripe-form" class="space-y-4">

                    {{-- List cards here --}}
                    <h3>Choisissez une carte</h3>

                    @foreach ($cards as $card)
                        <div class="card-option">
                            <label class="card-label">
                                <span class="card-owner">{{ $card->name }}</span>
                                <span class="card-brand">{{ $card->brand }}</span>
                                <span class="card-last-number">**** **** **** {{ $card->lastNumber }}</span>
                                <input type="radio" name="selectedCard" value="{{ $card->paymentMethodId }}" {{$card->is_default_card ? 'checked':''}}>
                            </label>
                        </div>
                    @endforeach

                    <!-- Bouton + avec texte au survol -->
                    <div class="mt-0 text-right mr-1 relative group">
                        <button type="button" class="add-card-button text-3xl" onclick="window.location.href='{{ route('recruter.plan.show', ['slug' => $planSlug, 'new_card' => 'true']) }}'">
                            <i class="fas fa-circle-plus"></i>
                        </button>
                        <!-- Texte affiché au survol -->
                        <span class="mt-8 absolute -right-12 top-1/2 transform -translate-y-1/2 bg-gray-700 text-white text-sm px-2 py-1 rounded hidden group-hover:block">
                            Ajouter une nouvelle carte
                        </span>
                    </div>

                    <div id="card-errors" role="alert" class="text-center text-red-500 mt-2 text-sm"></div>


                    <!-- Bouton de paiement -->
                    <button type="button" id="card-button" class="mt-6 w-full bg-primary text-white py-2 rounded-lg hover:bg-primary-dark transition">                        Payer
                    </button>

                    <!-- Loader -->
                    <div id="spinner-loader" class="flex items-center justify-center hidden">
                        <div class="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                    </div>
                </form>
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script src="https://js.stripe.com/v3/"></script>

        <script>

            function inProcessus() {
                console.log('click in inProcessus');
                document.getElementById('card-errors').textContent = "";
                document.getElementById('spinner-loader').classList.remove('hidden');
                document.getElementById('card-button').classList.add('hidden');
                document.getElementById('overlay').classList.remove('hidden'); // Affiche l'overlay
            }

            function finishProcessus(disableButton = false) {
                document.getElementById('spinner-loader').classList.add('hidden');
                document.getElementById('card-button').classList.remove('hidden');
                document.getElementById('overlay').classList.add('hidden'); // Masque l'overlay
                if (disableButton) {
                    document.getElementById('card-button').classList.add('disabled');
                }
                else
                {
                    Swal.fire({
                        icon: 'error', // Indique une alerte d'échec
                        title: "Erreur durant le processus",
                        text: "Votre paiement d'abonnement a échoué. Veuillez réessayer.",
                        confirmButtonText: 'Ok'
                    });

                }
            }


            const cardButton = document.getElementById('card-button');
            const stripe = Stripe("{{ config('stripe.key') }}", { locale: "{{ app()->getLocale() }}" });



            cardButton.addEventListener('click', async (e) =>
            {
                e.preventDefault();
                inProcessus();
                const radioButtons = document.querySelectorAll('input[name="selectedCard"]');

                // return;
                // Fonction pour obtenir la valeur du bouton radio sélectionné
                // Parcourir les boutons radio
                radioButtons.forEach(async (radioButton, index) => {

                    // Vérifier si le bouton radio est sélectionné
                    if (radioButton.checked)
                    {
                        // Récupérer et retourner la valeur

                        let paymentMethod = radioButton.value;
                        console.log('paymentMethod', paymentMethod);

                        // Launch axios request for payment
                        axios.post("{{route('recruter.plan.initiateSubscription')}}", {
                            payment_method: paymentMethod,
                            planSlug: "{{$planSlug}}",
                            exist_card:true,
                        },
                        {}).then(async function(response)
                        {
                            console.log('Ici, succès', response.data);

                            if (response.data.status === 'success')
                            {
                                let subscription_id = response.data.subscription_id;
                                console.log('Payment succès finally send axios post finish',response.data);
                                axios.post("{{route('recruter.plan.subscriptionFinish')}}", {
                                    payment_method: paymentMethod,
                                    subscription_id: subscription_id,
                                    exist_card:true,
                                    planSlug:"{{$planSlug}}"
                                }, {}).then(async function(response) {
                                    // alertMessage('{{ __('cont__plan.subscription_payed_successfully') }}', 'success');
                                    Swal.fire({
                                    icon: 'success',
                                    title: 'Abonnement terminé',
                                    text: "Votre paiement d\'abonnement s'est bien effectué.",
                                    confirmButtonText: 'OK'
                                });

                                    finishProcessus(true);
                                    setTimeout(() => {
                                        window.location.href = "{{route('recruter.packages')}}";
                                    }, 2000);
                                }).catch(async function(error) {
                                    console.log('Error:', error);
                                    finishProcessus();
                                });

                            }
                            else if(response.data.status === 'requires_action')
                            {
                                let subscription_id = response.data.subscription_id;
                                const { error } = await stripe.confirmCardPayment(response.data.client_secret);

                                if (error)
                                {
                                    console.error('error confirmCardPayment 3DS', error);
                                    finishProcessus();
                                } else
                                {
                                    console.log('Payment succès 3DS finally send axios post finish');

                                    axios.post("{{route('recruter.plan.subscriptionFinish')}}", {
                                        payment_method: paymentMethod,
                                        subscription_id: subscription_id,
                                        exist_card:true,
                                        planSlug:"{{$planSlug}}"
                                    }, {}
                                    ).then(function(response) {
                                        finishProcessus(true);
                                        // alert('Subscription 3DS finish need webhook active to finalise', 'success');
                                        Swal.fire({
                                            icon: 'success',
                                            title: 'Abonnement terminé',
                                            text: "Votre paiement d\'abonnement s'est bien effectué.",
                                            confirmButtonText: 'OK'
                                        });
                                        setTimeout(() => {
                                            window.location.href = "{{route('recruter.packages')}}";
                                        }, 2000);
                                    }).catch(function(error)
                                    {
                                        console.log('Error finish subscription:', error);
                                        finishProcessus();
                                    });
                                }
                            }
                        }).catch( function(error) {
                            console.log('Ici, error initiate subscription existing card',error);
                            finishProcessus();
                        });
                    }
                    if(radioButtons.length-1==index)
                    {
                        // Aucun radio n'est selectionné
                        document.getElementById('card-errors').textContent = "";
                        return;
                    }

                });

            });
        </script>



    </div>

</x-recruter-dashboard-layout>
