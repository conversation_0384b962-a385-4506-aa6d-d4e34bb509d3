<x-recruter-dashboard-layout>
    <div class="container mx-auto px-4">
        {{-- @dd('paginatedInvoices',$paginatedInvoices) --}}
        @if (count($paginatedInvoices)>0)

            <h1 class="text-3xl font-bold text-center text-primary mb-6">Liste des Factures</h1>

            <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-800 rounded-lg">
                    <thead>
                        <tr>
                            <th class="px-4 py-2 text-left text-lg font-semibold text-primary border-b border-gray-800">Numéro de Facture</th>
                            <th class="px-4 py-2 text-left text-lg font-semibold text-primary border-b border-gray-800">Description</th>
                            <th class="px-4 py-2 text-left text-lg font-semibold text-primary border-b border-gray-800">Montant HT</th>
                            <th class="px-4 py-2 text-left text-lg font-semibold text-primary border-b border-gray-800">Montant TTC</th>
                            <th class="px-4 py-2 text-left text-lg font-semibold text-primary border-b border-gray-800">Taxe</th>
                            <th class="px-4 py-2 text-left text-lg font-semibold text-primary border-b border-gray-800">Période de Facturation</th>
                            <th class="px-4 py-2 text-left text-lg font-semibold text-primary border-b border-gray-800">Facture PDF</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($paginatedInvoices as $invoice)
                            <tr class="border-gray-1000">
                                <td class="px-4 py-2 border-b border-gray-800 text-gray-700">{{ $invoice['number'] }}</td>
                                @php
                                    $lines = $invoice['lines']['data'];
                                    $descriptions = array_map(fn($line) => $line['description'], $lines);
                                @endphp
                                <td class="px-4 py-2 border-b border-gray-800 text-gray-700">
                                    @foreach ($descriptions as $description)
                                        <span>{{ $description }}</span><br>
                                    @endforeach
                                </td>
                                <td class="px-4 py-2 border-b border-gray-800 text-gray-700">{{ number_format($invoice['total_excluding_tax'] / 100, 2) }} {{ strtoupper($invoice['currency']) }}</td>
                                <td class="px-4 py-2 border-b border-gray-800 text-gray-700">{{ number_format($invoice['amount_due'] / 100, 2) }} {{ strtoupper($invoice['currency']) }}</td>
                                <td class="px-4 py-2 border-b border-gray-800 text-gray-700">
                                    {{ number_format(($invoice['tax'] / $invoice['total_excluding_tax']) * 100, 1) }}%
                                </td>
                                <td class="px-4 py-2 border-b border-gray-800 text-gray-700">{{ date('d/m/Y', $invoice['period_start']) }} - {{ date('d/m/Y', $invoice['period_end']) }}</td>
                                <td class="px-4 py-2 border-b border-gray-800">
                                    <a href="{{ $invoice['invoice_pdf'] }}" target="_blank" class="text-primary font-semibold hover:underline">Voir PDF</a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination Links -->
            <div class="mt-4">
                {{ $paginatedInvoices->links() }}
            </div>
        @else
            <h1 class="text-3xl font-bold text-center text-primary mb-6">Vous n'avez pas encore de factures</h1>
        @endif
        {{-- <div class="feedback-button fixed bottom-4 right-4">
            <x-feedback-button-guest active="true" />
        </div> --}}
    </div>
</x-recruter-dashboard-layout>
