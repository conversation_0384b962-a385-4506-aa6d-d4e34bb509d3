<x-recruter-dashboard-layout>

    <div class="max-w-7xl text-textNormal">
        <!-- Title -->
        <div class="border-l-4 rounded-sm border-primary px-6 py-2 mb-1">
            <h1 class="text-2xl font-semibold text-primary">MON ABONNEMENT
                @if($subscriptionIsActive)
                    <i class="fas fa-check-circle text-green-500"></i>
                @else
                    <i class="fas fa-times-circle"></i>
                @endif
            </h1>
            <div class="flex space-x-4">
                <button class="flex items-center justify-center gap-2 text-lg font-semibold text-primary bg-white border border-primary px-5 py-1 rounded-full shadow-lg hover:bg-primary hover:text-white transition duration-300 ease-in-out transform hover:scale-105" onclick="window.location.href='{{route('recruter.invoice.showAll')}}'">
                    <i class="fas fa-file-invoice"></i>
                    Factures
                </button>

                <button class="flex items-center justify-center gap-2 text-lg font-semibold text-primary bg-white border border-primary px-5 py-1 rounded-full shadow-lg hover:bg-primary hover:text-white transition duration-300 ease-in-out transform hover:scale-105" onclick="window.location.href='{{route('recruter.card.showAll')}}'">
                    <i class="fas fa-credit-card"></i>
                    Cartes
                </button>
            </div>


        </div>


        <!-- Session Status -->
        <x-auth-session-status class="mb-4" :status="session('status')" />

        <section class="w-full flex justify-center space-x-6">
            <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
            @foreach ($plans as $plan)
                @if ($plan->slug=='popular-plan')
                {{-- @dd($subscriptionIsActive,$plan->slug,$planSlugActive) --}}
                    <!-- 24 Heures Package -->
                    <div class="{{!$subscriptionIsActive ? 'border-2 border-primary ':'border border-gray-300'}} rounded-lg shadow-lg p-6 w-72 bg-white">
                        <div class="flex justify-between items-center">
                            <div class="text-[14px] font-semibold text-gray-400 tracking-wider">{{$plan->duration_in_days*24}} Heures</div>
                            <div class="text-sm text-white bg-primary rounded-lg px-2 py-0 inline-block">
                                @if ($plan->show_name)
                                    <small>{{$plan->name}}</small>
                                @endif
                            </div>
                        </div>
                        <h2 class="text-4xl font-bold text-gray-700 my-6">CHF {{$plan->price}}</h2>
                        <ul class="text-sm mt-4 space-y-2 text-gray-600">
                            @foreach (json_decode($plan->description_html) as $item)
                                <li> <i class="fas fa-circle-check text-primary mr-2"></i> {!!$item!!}</li>
                            @endforeach
                        </ul>
                        <a class="mt-6 w-full {{$subscriptionIsActive && $plan->slug==$planSlugActive ? 'bg-green-500 cursor-not-allowed disabled pointer-events-none' : 'bg-primary cursor-pointer hover:shadow-lg active:shadow-none active:scale-95 transition duration-150 ease-in-out'}} text-white py-2 rounded-lg text-center block"
                            onclick="confirmSubscription(event, '{{ route('recruter.plan.show', ['slug' => $plan->slug]) }}')">
                             {{$subscriptionIsActive && $plan->slug==$planSlugActive ? 'Abonnement actuel' : ''}}
                             @if ($plan->slug!=$planSlugActive)
                                 {{$subscriptionIsActive ? 'Changer' : 'Souscrire'}}
                             @endif
                         </a>


                    </div>

                @elseif ($plan->slug=='business-plan')
                    <!-- 1 Semaine Package -->
                    <div class="border border-gray-300 rounded-lg shadow-lg p-6 w-72 bg-white">

                        <div class="flex justify-between items-center">
                            <div class="text-[14px] font-semibold text-gray-400 tracking-wider">{{$plan->duration_in_days/7}} Semaine</div>
                            <div class="text-sm text-white bg-primary rounded-lg px-2 py-0 inline-block">
                                @if ($plan->show_name)
                                    <small>{{$plan->name}}</small>
                                @endif
                            </div>
                        </div>

                        <h2 class="text-4xl font-bold text-gray-700 my-6">CHF {{$plan->price}}</h2>
                        <ul class="mt-4 text-sm space-y-2 text-gray-600">
                            @foreach (json_decode($plan->description_html) as $item)
                                <li> <i class="fas fa-circle-check text-primary mr-2"></i> {!!$item!!}</li>
                            @endforeach

                        </ul>
                        <a class="mt-6 w-full {{$subscriptionIsActive && $plan->slug==$planSlugActive ? 'bg-green-500 cursor-not-allowed disabled pointer-events-none' : 'bg-primary cursor-pointer hover:shadow-lg active:shadow-none active:scale-95 transition duration-150 ease-in-out'}} text-white py-2 rounded-lg text-center block"
                            onclick="confirmSubscription(event, '{{ route('recruter.plan.show', ['slug' => $plan->slug]) }}')">
                             {{$subscriptionIsActive && $plan->slug==$planSlugActive ? 'Abonnement actuel' : ''}}
                             @if ($plan->slug != $planSlugActive)
                                 {{$subscriptionIsActive ? 'Changer' : 'Souscrire'}}
                             @endif
                         </a>
                    </div>

                @elseif ($plan->slug=='pro-plan')
                    <!-- 1 Mois Package -->
                    <div class="border border-gray-300 rounded-lg shadow-lg p-6 w-72 bg-white">

                        <div class="flex justify-between items-center">
                            <div class="text-[14px] font-semibold text-gray-400 tracking-wider">{{$plan->duration_in_days/30}} Mois</div>
                            <div class="text-sm text-white bg-primary rounded-lg px-2 py-0 inline-block">
                                @if ($plan->show_name)
                                    <small>{{$plan->name}}</small>
                                @endif
                            </div>
                        </div>

                        <h2 class="text-4xl font-bold text-gray-700 my-6">CHF {{$plan->price}}</h2>
                        <ul class="mt-4 text-sm space-y-2 text-gray-600">
                            @foreach (json_decode($plan->description_html) as $item)
                                <li> <i class="fas fa-circle-check text-primary mr-2"></i> {!!$item!!}</li>
                            @endforeach
                        </ul>
                        <a class="mt-6 w-full {{$subscriptionIsActive && $plan->slug==$planSlugActive ? 'bg-green-500 cursor-not-allowed disabled pointer-events-none' : 'bg-primary cursor-pointer hover:shadow-lg active:shadow-none active:scale-95 transition duration-150 ease-in-out'}} text-white py-2 rounded-lg text-center block"
                            onclick="confirmSubscription(event, '{{ route('recruter.plan.show', ['slug' => $plan->slug]) }}')">
                             {{$subscriptionIsActive && $plan->slug==$planSlugActive ? 'Abonnement actuel' : ''}}
                             @if ($plan->slug != $planSlugActive)
                                 {{$subscriptionIsActive ? 'Changer' : 'Souscrire'}}
                             @endif
                         </a>
                    </div>
                @endif

            @endforeach
            @if ($subscriptionIsActive)
                <script>
                    function confirmSubscription(event, url)
                    {
                        event.preventDefault();
                        Swal.fire({
                            title: 'Êtes-vous sûr ?',
                            text: "Vous êtes sur le point de changer d'abonnement. Veuillez noter qu'en passant à cette nouvelle offre, un remboursement au prorata des jours non utilisés de votre abonnement actuel sera effectué",
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'Oui, confirmer',
                            cancelButtonText: 'Annuler'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                window.location.href = url;
                            }
                        });
                    }
                </script>
            @else
                <script>
                    function confirmSubscription(event, url)
                    {
                        window.location.href = url;
                    }
                </script>
            @endif

        </section>
        <div class="feedback-button fixed bottom-4 right-4">
            <x-feedback-button-guest active="true" />
        </div>
        <x-feedback-modal />
                        @isset($priorities, $severities)
                            <x-feedback-modal :priorities="$priorities" :severities="$severities" :user="auth()->user()" />
                        @endisset
    </div>

</x-recruter-dashboard-layout>
