<style>
    .feedback-button {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        /* Pour s'assurer qu'il est au-dessus des autres éléments */
    }
</style>

<x-recruter-dashboard-layout>
    <!-- Title -->
    <div class="border-l-4 rounded-sm border-primary px-6 py-2 mb-8">
        <h1 class="text-2xl font-semibold text-primary">MODIFIER LE PROFIL</h1>
    </div>

    <!-- Session Status -->
    <x-auth-session-status class="mb-4" :status="session('status')" />

    <!-- Profile Form -->
    <section class="space-y-6">
        <form method="POST" action="{{ route('recruter.profile.update') }}" enctype="multipart/form-data">
            @csrf
            @method('PUT')

            <!-- Profile Photo and Visibility -->
            <div class="border-b border-gray-200 pb-4">
                <h2 class="text-lg font-semibold text-primary">Profil</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <div>
                        <x-input-label for="profile_picture" value="{!! __('candidate_register.photo_de_profil_facultatif_') !!}" />
                        @isset($civility->photo)
                            <x-upload-profile id="profile_picture" :profileImage="optional($civility->photo)->url" />
                        @else
                            <x-upload-profile id="profile_picture" profileImage="https://placehold.co/400x400.png" />
                        @endisset
                        <x-input-error :messages="$errors->get('profile_picture')" class="mt-2" />
                    </div>
                </div>
            </div>

            <!-- Personal Information Section -->
            <div class="border-b border-gray-200 pb-4">
                <h2 class="text-lg font-semibold text-primary">Données personnelles</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <div>
                        <x-input-label for="company_name" value="Nom de l'entreprise *" />
                        <x-text-input id="company_name" class="block mt-1 w-full" type="text" name="company_name"
                            :value="old('company_name', $civility->company_name ?? '')" required />
                        <x-input-error :messages="$errors->get('company_name')" class="mt-2" />
                    </div>
                    <!-- Email -->
                    <div>
                        <x-input-label for="email" value="{!! __('candidate_register.email_') !!}" />
                        <x-text-input id="email" disabled class="block mt-1 w-full" type="email" name="email"
                            :value="old('email', $user->email)" required autocomplete="username" />
                        <x-input-error :messages="$errors->get('email')" class="mt-2" />
                    </div>
                    <!-- Phone -->
                    <div>
                        <x-input-label for="phone" value="{!! __('candidate_register.numxro_de_txlxphone_') !!}" />
                        <x-text-input id="phone" class="block mt-1 w-full" type="tel" name="phone"
                            :value="old('phone', $phones->first()->number ?? '')" required />
                        <x-input-error :messages="$errors->get('phone')" class="mt-2" />
                    </div>
                    <!-- Website -->
                    <div>
                        <x-input-label for="website" value="Site web (facultatif)" />
                        <x-text-input id="website" class="block mt-1 w-full" type="text" name="website"
                            :value="old('website', $civility->website ?? '')" />
                        <x-input-error :messages="$errors->get('website')" class="mt-2" />
                    </div>
                </div>

                <div class="border-b border-gray-200 pb-4">

                    <!-- Country of Residence -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                        <!-- Country of Residence -->
                        <div>
                            <x-input-label for="country_of_residence" value="{!! __('candidate_register.pays_de_rxsidence_') !!}" />
                            <x-select-input id="country_of_residence" name="country_of_residence"
                                class="block mt-1 w-full" required>
                                @foreach ($countries as $item)
                                    <option value="{{ $item->id }}" @selected(old('country_of_residence', $civility->country_of_residence_country_id ?? '') == $item->id)  id-country="{{ $item->id }}">{{ $item->name }}
                                    </option>
                                @endforeach
                            </x-select-input>
                            <x-input-error :messages="$errors->get('country_of_residence')" class="mt-2" />
                        </div>
                        <!-- Commune -->

                        <div>
                            <x-input-label for="commune" value="{!! __('candidate_register.commune_de_domicile') !!}" />
                            <x-select-input id="commune" name="commune" class="block mt-1 w-full" required>
                                <!-- Option par défaut pour sélectionner la région ou le canton -->
                                <option value="{{ $civility->commune }}">{{ $civility->commune }}</option>


                            </x-select-input>
                            <x-input-error :messages="$errors->get('commune')" class="mt-2" />
                        </div>
                    </div>
                    <div class="grid grid-cols-1 gap-6 mt-4">
                        <div>
                            <x-input-label for="address" value="Localisation de l'entreprise" />
                            <x-text-input-address-completion id="address" name="address" class="block mt-1 w-full"
                                type="text" :value="old('address', $user->getAddress()->name ?? '')"
                                latitude_longitude="{{ $user->getAddress()->lat.','.$user->getAddress()->log }}" required />
                            <x-input-error :messages="$errors->get('address')" class="mt-2" />
                            <x-input-error :messages="$errors->get('latitude_longitude')" class="mt-2" />
                        </div>
                    </div>
                    <div class="mt-6 flex justify-center items-center overflow-x-auto overflow-y-hidden">
                        <x-show-point-map :address="$user->getAddress()->name" :latitude="$user->getAddress()->lat" :longitude="$user->getAddress()->log" />
                    </div>
                </div>

                <!-- Recaptcha -->
                <div class="mt-6">
                    <x-recaptcha />
                    @error('g-recaptcha-response')
                        <p class="text-red-600 text-sm mt-2">{{ $message }}</p>
                    @enderror
                </div>

            </div>

            <!-- Submit Button -->
            <div class="flex justify-end mt-8">
                <x-primary-button class="ms-4">
                    Enregistrer
                </x-primary-button>
            </div>
        </form>

        <div class="feedback-button">
            <x-feedback-button-guest active="true" />
        </div>
    </section>

    <script>
        document.getElementById('country_of_residence').addEventListener('change', function() {
            // Récupère l'option sélectionnée
            const selectedOption = this.options[this.selectedIndex];

            // Récupère la valeur de l'attribut 'id-country' de l'option sélectionnée
            const countryId = selectedOption.getAttribute('id-country');

            // Vérifie si un pays a bien été sélectionné
            if (countryId) {
                // Effectue une requête AJAX pour récupérer les régions par ID du pays
                fetch(`/get-all-region?ids=${countryId}`)
                    .then(response => response.json())
                    .then(data => {
                        // Récupère le select des communes ou cantons
                        const communeSelect = document.getElementById('commune');
                        communeSelect.innerHTML = ''; // Réinitialise le select des communes

                        // Ajoute une option par défaut
                        const defaultOption = document.createElement('option');
                        defaultOption.value = '';
                        defaultOption.disabled = true;
                        defaultOption.selected = true;
                        defaultOption.textContent = '{{ __('Sélectionner Région ou Canton') }}';
                        communeSelect.appendChild(defaultOption);

                        // Ajoute les options de région au select des communes
                        data.forEach(region => {
                            const option = document.createElement('option');
                            option.value = region.name; // Utilise l'id de la région
                            option.textContent = region.name; // Affiche le nom de la région
                            communeSelect.appendChild(option);
                        });
                    })
                    .catch(error => {
                        console.error('Erreur lors de la récupération des régions:', error);
                    });
            }
        });
    </script>
</x-recruter-dashboard-layout>
