<x-recruter-dashboard-layout>
    <div class="max-w-7xl text-textNormal">
        <!-- Titre du candidat -->
        <div class="flex justify-between items-center border-l-4 rounded-sm border-primary px-6 py-2 mb-8">
            <h1 class="text-2xl font-semibold text-primary">
                @if (\App\Helpers\UsedUpFunction::isSubscribed())
                    {{ $candidate->fullname() }}
                @else
                    {{ explode(' ', $candidate->fullname())[0] }}
                @endif
            </h1>
            {{-- @dd(\App\Helpers\UsedUpFunction::isSubscribed()) --}}
            @if (\App\Helpers\UsedUpFunction::isSubscribed())
                <div class="flex space-x-3">
                    <a href="{{ route('recruter.candidate-selected') }}">
                        <x-primary-button>Retour</x-primary-button>
                    </a>
                    <a href="{{ route('recruter.candidate-pdf', ['candidate_id' => $candidate->id]) }}">
                        <x-primary-button>PDF</x-primary-button>
                    </a>
                    <a href="{{ route('messages.user', ['user_id' => $candidate->id]) }}">
                        <x-primary-button>Discuter</x-primary-button>
                    </a>
                </div>
            @endif
        </div>

        <!-- Session Status -->
        <x-auth-session-status class="mb-4" :status="session('status')" />

        <!-- Section de catégories et informations -->
        <div class="flex flex-col md:flex-row gap-8">
            <!-- Informations du candidat -->
            <div class="flex-1">
                <p><strong class="text-primary">Catégories</strong><br>
                    {{ \App\Helpers\UsedUpFunction::getCategoryLabel($candidate->civility?->category) }}
                </p><br>
                <p><strong class="text-primary">Domaine(s) d'activité</strong><br>
                    @foreach ($fieldActivities as $activity)
                        {{ $activity->name }},
                    @endforeach
                </p><br>
                <p><strong class="text-primary">Profession(s) recherchée(s)</strong><br>
                    @foreach ($professions as $profession)
                        {{ $profession->name }},
                    @endforeach
                </p><br>
                <p><strong class="text-primary">Type de(s) poste(s) recherché(s)</strong><br>
                    @foreach ($typeProfessions as $typeProfession)
                        {{ $typeProfession?->name }},
                    @endforeach
                </p><br>
                <p><strong class="text-primary">Je recherche (contrat de travail)</strong><br>
                    {{ \App\Helpers\UsedUpFunction::getContractTypeLabel($candidate->civility?->contract_type) }}
                </p><br>
                <p><strong class="text-primary">Disponibilité</strong><br>
                    {{ $responsibilityCandidate?->name }}
                </p><br>
                <p><strong class="text-primary">Taux d'activité</strong><br>
                    {{ $candidate->civility?->work_rate }}%
                </p><br>
                <p><strong class="text-primary">Formations</strong><br>
                    @foreach ($formations as $formation)
                        {{ $formation->name }},
                    @endforeach
                </p><br>
                
                {{-- <p><strong class="text-primary">Motivations, parcours, etc.</strong></p><br> --}}
            </div>

            <!-- Carte d'informations supplémentaires -->
            <div class="w-full md:w-1/3 rounded-lg">
                <div class="px-6 py-4 bg-slate-300-100 mb-6">
                    <h2 class="text-lg font-semibold mb-3">Information</h2>
                    <div class="flex justify-between border-b border-gray-200 mb-3">
                        <span>Permis de conduire</span><span class="text-right">
                            @foreach ($permits as $permit)
                                {{ $permit->name }},
                            @endforeach
                        </span>
                    </div>
                    <div class="flex justify-between border-b border-gray-200 mb-3">
                        <span>Suisse / Permis de séjour</span><span class="text-right">
                            {{ $residencePermit?->name }}
                        </span>
                    </div>
                    <div class="flex justify-between border-b border-gray-200 mb-3">
                        <span>Pays de résidence</span><span class="text-right">
                            {{ $countryOfResidence?->name }}
                        </span>
                    </div>
                    {{-- @if (auth()->user()->getAddress()?->id)
                        <div class="flex justify-between border-b border-gray-200 mb-3">
                            <span>Adresse</span><span class="text-right">
                                {{ auth()->user()->getAddress()?->name }}
                            </span>
                        </div>
                    @endif --}}
                </div>
                @if (\App\Helpers\UsedUpFunction::isSubscribed())
                    <div class="px-6 py-4 bg-slate-300-100 mb-6">
                        <h2 class="text-lg font-semibold mb-3">Contact</h2>
                        <div class="flex justify-between border-b border-gray-200 mb-3">
                            <span>Téléphone</span><span class="text-right">
                                {{ $phones->first()->number ?? 'Non spécifié' }}
                            </span>
                        </div>
                        <div class="flex justify-between border-b border-gray-200 mb-3">
                            <span>Email</span><span class="text-right">
                                {{ $candidate->email }}
                            </span>
                        </div>
                         <div class="flex justify-between border-b border-gray-200 mb-3">
                            <span>Date de naissance</span><span class="text-right">
                                {{ $civility->date_of_birth ? \Carbon\Carbon::parse($civility->date_of_birth)->translatedFormat('d/m/Y') : '' }}
                            </span>
                        </div>
                    </div>
                @else
                    <div class="mt-4 p-4 bg-yellow-100 text-yellow-700 rounded mb-6">
                        <p>Vous n'avez aucun forfait. Pour contacter cet utilisateur, vous devez <a
                                href="{{ route('recruter.packages') }}" class="text-primary underline">cliquer ici</a>
                            pour souscrire à un forfait.</p>
                    </div>
                @endif
                
                @if ($candidate->profilePhoto)
                    <div class="text-center">
                        <img src="{{ $candidate->profilePhoto->url ?? asset('images/user.png') }}" alt="Profile Photo"
                            class="w-full h-auto rounded-lg">
                    </div>
                @endif


                <!-- Section pour afficher les fichiers (CV, certificats de travail, diplômes) -->
                @if (\App\Helpers\UsedUpFunction::isSubscribed())
                    <div class="px-6 py-4 bg-slate-300-100 mb-6">
                        <h2 class="text-lg font-semibold mb-3">Documents</h2>
                        <div class="border-b border-gray-200 mb-3 pb-2">
                            <span class="block">CV</span>
                            @if ($civility->cv)
                                <span class="block mt-1">
                                    <a href="{{ asset($civility->cv->path) }}" target="_blank" class="text-primary underline break-all block max-w-xs md:max-w-none">Télécharger</a>
                                </span>
                            @endif
                        </div>
                        @if ($civility->certificate_files && $civility->certificate_files->count())
                            <div class="mb-3">
                                <span class="font-semibold">Certificats/Diplômes</span>
                                <ul class="list-disc pl-5">
                                    @foreach ($civility->certificate_files as $file)
                                        <li>
                                            <a href="{{ asset($file->path) }}" target="_blank" class="text-primary underline">
                                                {{ $file->original_name ?? $file->name }}
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                    </div>
                @endif

            </div>
        </div>
        <div>
            <a href="{{ route('recruter.candidate-selected') }}">
                <x-primary-button>Retour</x-primary-button>
            </a>
        </div>
    </div>
</x-recruter-dashboard-layout>
