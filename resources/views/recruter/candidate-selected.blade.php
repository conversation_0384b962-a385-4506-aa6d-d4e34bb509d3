<x-app-layout>
    <div class="bg-white p-4 rounded-lg shadow-md mb-4">
        <div class="bg-white p-4 rounded-lg shadow-sm">
            <!-- Première ligne -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-3 mb-3">
                <!-- Domaine Dropdown -->
                <div>
                    <x-input-label for="activity_fields" value="{!! __('candidate_register.domaine_s_d_activitx_') !!}"
                        class="text-xs font-medium text-gray-600" />
                    <x-select-input id="activity_fields" name="activity_fields[]"
                        class="block mt-1 w-full text-xs h-10 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        multiple>
                        @foreach ($fieldActivities as $item)
                            <option value="{{ $item->id }}" id-activity="{{ $item->id }}"
                                @if (in_array($item->id, $activity_fields)) selected @endif>
                                {{ $item->name }}
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('activity_fields.*')" class="mt-1 text-xs" />
                </div>

                <!-- Profession Dropdown -->
                <div class="desired_professions">
                    <x-input-label for="desired_professions" value="{!! __('candidate_register.profession_s_recherchxe_s_') !!}"
                        class="text-xs font-medium text-gray-600" />
                    <x-select-input id="desired_professions" name="desired_professions[]"
                        class="block mt-1 w-full text-xs h-10 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        multiple>
                        <option value="" disabled>Filtre...</option>
                        @foreach ($professions as $item)
                            <option value="{{ $item->id }}" id-activity="{{ $item->field_activity_id }}"
                                @if (in_array($item->id, $desired_professions)) selected @endif
                                @if (empty($item->field_activity_id)) style="background-color:#ffe4b2;color:#b45309;font-style:italic;" @endif>
                                {{ $item->name }}@if (empty($item->field_activity_id)) (hors domaine)@endif
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('desired_professions')" class="mt-1 text-xs" />
                </div>

                <!-- Pays Dropdown -->
                <div>
                    <x-input-label for="country_of_residence" value="{!! __('candidate_register.pays_de_rxsidence_') !!}"
                        class="text-xs font-medium text-gray-600" />
                    <x-select-input id="country_of_residence" name="country_of_residence"
                        class="block mt-1 w-full text-xs h-10 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">Filtre...</option>
                        @foreach ($countries as $item)
                            <option value="{{ $item->id }}" @selected($country_of_residence == $item->id)
                                id-country="{{ $item->id }}">
                                {{ $item->name }}
                            </option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('country_of_residence')" class="mt-1 text-xs" />
                </div>

                <!-- Région  Dropdown  -->
                <div>
                    <x-input-label for="commune" value="{!! __('candidate_register.commune_de_domicile') !!}"
                        class="text-xs font-medium text-gray-600" />
                    <x-select-input id="commune" name="commune"
                        class="block mt-1 w-full text-xs h-10 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">Sélectionner Région ou Canton</option>
                        @if ($country_of_residence)
                            @php
                                $regions = \App\Models\Region::where('country_id', $country_of_residence)->get();
                            @endphp
                            @foreach ($regions as $region)
                                <option value="{{ $region->name }}" @selected($commune == $region->name)>
                                    {{ $region->name }}
                                </option>
                            @endforeach
                        @endif
                    </x-select-input>
                    <x-input-error :messages="$errors->get('commune')" class="mt-1 text-xs" />
                </div>
            </div>

            <!-- Deuxième ligne -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-3">
                <!-- Type de contrat -->
                <div>
                    <x-input-label for="contract_type" value="Type de contrat"
                        class="text-xs font-medium text-gray-600" />
                    <x-select-input id="contract_type" name="contract_type[]"
                        class="block mt-1 w-full text-xs h-10 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        multiple>
                        <option value="">Filtre...</option>
                        <option value="call" @if (in_array('call', old('contract_type', $contract_type))) selected @endif>Travail sur appel
                        </option>
                        <option value="cdi" @if (in_array('cdi', old('contract_type', $contract_type))) selected @endif>CDI</option>
                        <option value="cdd" @if (in_array('cdd', old('contract_type', $contract_type))) selected @endif>CDD</option>
                    </x-select-input>
                </div>

                <!-- Catégorie -->
                <div>
                    <x-input-label for="category" value="Catégorie" class="text-xs font-medium text-gray-600" />
                    <x-select-input id="category" name="category"
                        class="block mt-1 w-full text-xs h-10 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value=""></option>
                        <option value="current_profiles" @selected($category == 'current_profiles')>Profils courants</option>
                        <option value="retired" @selected($category == 'retired')>Retraité(e)s</option>
                        <option value="migrants" @selected($category == 'migrants')>Migrant(e)s</option>
                        <option value="students" @selected($category == 'students')>Étudiant(e)s</option>
                    </x-select-input>
                    <x-input-error :messages="$errors->get('category')" class="mt-1 text-xs" />
                </div>

                <!-- Suisse / Permis de séjours -->
                <div>
                    <x-input-label for="residence" value="Suisse / Permis de séjours"
                        class="text-xs font-medium text-gray-600" />
                    <x-select-input id="residence" name="residence"
                        class="block mt-1 w-full text-xs h-10 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value=""></option>
                        <option value="suisse" @selected($residence == 'suisse')>{!! __('candidate_register.suisse') !!}</option>
                        @foreach ($residencePermits as $item)
                            <option value="{{ $item->id }}" @selected($residence == $item->id)>{{ $item->name }}
                            </option>
                        @endforeach
                    </x-select-input>
                </div>

                <!-- Distance Dropdown -->
                <div>
                    @isset(auth()->user()->getAddress()?->id)
                        <div class="flex items-center gap-2 mb-1"> <!-- Ajout de mb-1 pour réduire l'espace en bas -->
                            <x-input-label for="distance" value="Distance" class="text-xs font-medium text-gray-600" />
                            <span id="distanceValue" class="text-sm">{{ $distance ?? 50 }}</span>
                            <!-- Ajout de text-sm pour réduire la taille -->
                            <span class="text-sm">Km</span> <!-- Ajout de text-sm pour cohérence -->
                        </div>
                        <div class="mb-4">
                            <div class="mt-1 flex items-center justify-between"> <!-- Réduction du mt-3 à mt-1 -->
                                <div id="containerDistance" class="text-sm flex items-center justify-between w-full">
                                    <x-text-input id="distance" type="range" class="w-full ml-2" min="0"
                                        max="100" value="{{ $distance ?? 50 }}" />
                                </div>
                                <input type="checkbox" id="activeDistance" class="ml-4 p-2" @checked($distance !== null)>
                            </div>
                            <div class="mt-1 flex items-center justify-between"> <!-- Réduction du mt-2 à mt-1 -->
                                <div id="containerDistance" class="text-sm flex items-center justify-between w-full">
                                    <div class="flex space-x-2 w-full justify-between">
                                        <label class="inline-flex items-center">
                                            <input type="radio" name="distance_radio_options" value="5"
                                                class="form-radio" @checked($distance == 5)>
                                            <span class="ml-1 text-xs">5 Km</span>
                                        </label>
                                        <label class="inline-flex items-center">
                                            <input type="radio" name="distance_radio_options" value="10"
                                                class="form-radio" @checked($distance == 10)>
                                            <span class="ml-1 text-xs">10 Km</span>
                                        </label>
                                        <label class="inline-flex items-center">
                                            <input type="radio" name="distance_radio_options" value="25"
                                                class="form-radio" @checked($distance == 25)>
                                            <span class="ml-1 text-xs">25 Km</span>
                                        </label>
                                        <label class="inline-flex items-center">
                                            <input type="radio" name="distance_radio_options" value="50"
                                                class="form-radio" @checked($distance == 50)>
                                            <span class="ml-1 text-xs">50 Km</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endisset
                </div>
            </div>
        </div>

        <!-- Filter Tags Section -->
        <div class="mt-3 flex flex-wrap gap-2" id="filter-tags-container">
            {{-- Example tags based on image --}}
            @if (!empty($activity_fields))
                @foreach ($activity_fields as $activityFieldId)
                    @php
                        $activityField = collect($fieldActivities)->firstWhere('id', $activityFieldId);
                    @endphp
                    @if ($activityField)
                        <span class="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                            data-filter-name="activity_fields" data-filter-value="{{ $activityFieldId }}">
                            {{ $activityField->name }} <i class="fas fa-times ml-1 text-blue-600 cursor-pointer"></i>
                        </span>
                    @endif
                @endforeach
            @endif

            @if (!empty($desired_professions))
                @foreach ($desired_professions as $professionId)
                    @php
                        $profession = collect($professions)->firstWhere('id', $professionId);
                    @endphp
                    @if ($profession)
                        <span class="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                            data-filter-name="desired_professions" data-filter-value="{{ $professionId }}">
                            {{ $profession->name }} <i class="fas fa-times ml-1 text-blue-600 cursor-pointer"></i>
                        </span>
                    @endif
                @endforeach
            @endif

            @if (!empty($country_of_residence))
                @php
                    $country = collect($countries)->firstWhere('id', $country_of_residence);
                @endphp
                @if ($country)
                    <span class="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                        data-filter-name="country_of_residence" data-filter-value="{{ $country_of_residence }}">
                        {{ $country->name }} <i class="fas fa-times ml-1 text-blue-600 cursor-pointer"></i>
                    </span>
                @endif
            @endif

            @if (!empty($commune))
                <span class="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                    data-filter-name="commune" data-filter-value="{{ $commune }}">
                    {{ $commune }} <i class="fas fa-times ml-1 text-blue-600 cursor-pointer"></i>
                </span>
            @endif

            @if (!empty($contract_type))
                @foreach ($contract_type as $contractValue)
                    <span class="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                        data-filter-name="contract_type" data-filter-value="{{ $contractValue }}">
                        {{-- Displaying user-friendly names for contract types --}}
                        @if ($contractValue == 'call')
                            Travail sur appel
                        @elseif ($contractValue == 'cdi')
                            CDI
                        @elseif ($contractValue == 'cdd')
                            CDD
                        @else
                            {{ $contractValue }}
                        @endif
                        <i class="fas fa-times ml-1 text-blue-600 cursor-pointer"></i>
                    </span>
                @endforeach
            @endif

            @if (!empty($category))
                @php
                    $categoryName = '';
                    switch ($category) {
                        case 'current_profiles':
                            $categoryName = 'Profils courants';
                            break;
                        case 'retired':
                            $categoryName = 'Retraité(e)s';
                            break;
                        case 'migrants':
                            $categoryName = 'Migrant(e)s';
                            break;
                        case 'students':
                            $categoryName = 'Étudiant(e)s';
                            break;
                        default:
                            $categoryName = $category; // Fallback in case of unknown category
                            break;
                    }
                @endphp
                <span class="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                    data-filter-name="category" data-filter-value="{{ $category }}">
                    Catégorie: {{ $categoryName }} <i class="fas fa-times ml-1 text-blue-600 cursor-pointer"></i>
                </span>
            @endif

            @if (!empty($residence))
                @php
                    $residenceName =
                        $residence == 'suisse'
                            ? __('candidate_register.suisse')
                            : collect($residencePermits)->firstWhere('id', $residence)->name ?? $residence;
                @endphp
                <span class="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                    data-filter-name="residence" data-filter-value="{{ $residence }}">
                    Suisse / Permis de séjours: {{ $residenceName }} <i
                        class="fas fa-times ml-1 text-blue-600 cursor-pointer"></i>
                </span>
            @endif

            @if (!empty($criminal_record))
                <span class="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                    data-filter-name="criminal_record" data-filter-value="{{ $criminal_record }}">
                    Casier judiciaire vierge: {{ $criminal_record == 'yes' ? 'Oui' : 'Non' }} <i
                        class="fas fa-times ml-1 text-blue-600 cursor-pointer"></i>
                </span>
            @endif

            @if (!empty($work_rate))
                <span class="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                    data-filter-name="work_rate" data-filter-value="{{ $work_rate }}">
                    Taux d'activité: {{ $work_rate }}% <i
                        class="fas fa-times ml-1 text-blue-600 cursor-pointer"></i>
                </span>
            @endif

            @if (!empty($native_language))
                @foreach ($native_language as $languageId)
                    @php
                        $language = collect($languages)->firstWhere('id', $languageId);
                    @endphp
                    @if ($language)
                        <span class="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                            data-filter-name="native_language" data-filter-value="{{ $languageId }}">
                            Langue maternelle: {{ $language->name }} <i
                                class="fas fa-times ml-1 text-blue-600 cursor-pointer"></i>
                        </span>
                    @endif
                @endforeach
            @endif

            @if (!empty($fluent_languages))
                @foreach ($fluent_languages as $languageId)
                    @php
                        $language = collect($languages)->firstWhere('id', $languageId);
                    @endphp
                    @if ($language)
                        <span class="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                            data-filter-name="fluent_languages" data-filter-value="{{ $languageId }}">
                            Langue couramment parlée: {{ $language->name }} <i
                                class="fas fa-times ml-1 text-blue-600 cursor-pointer"></i>
                        </span>
                    @endif
                @endforeach
            @endif

            @if (!empty($intermediate_languages))
                @foreach ($intermediate_languages as $languageId)
                    @php
                        $language = collect($languages)->firstWhere('id', $languageId);
                    @endphp
                    @if ($language)
                        <span class="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                            data-filter-name="intermediate_languages" data-filter-value="{{ $languageId }}">
                            Langue avec notion intermédiaire: {{ $language->name }} <i
                                class="fas fa-times ml-1 text-blue-600 cursor-pointer"></i>
                        </span>
                    @endif
                @endforeach
            @endif

            @if (!empty($basic_languages))
                @foreach ($basic_languages as $languageId)
                    @php
                        $language = collect($languages)->firstWhere('id', $languageId);
                    @endphp
                    @if ($language)
                        <span class="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                            data-filter-name="basic_languages" data-filter-value="{{ $languageId }}">
                            Langue avec notion de base: {{ $language->name }} <i
                                class="fas fa-times ml-1 text-blue-600 cursor-pointer"></i>
                        </span>
                    @endif
                @endforeach
            @endif

            @if (!empty($selectedPermits))
                @foreach ($selectedPermits as $permitId)
                    @php
                        $permit = collect($permits)->firstWhere('id', $permitId);
                    @endphp
                    @if ($permit)
                        <span class="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                            data-filter-name="permits" data-filter-value="{{ $permitId }}">
                            Permis de conduire: {{ $permit->name }} <i
                                class="fas fa-times ml-1 text-blue-600 cursor-pointer"></i>
                        </span>
                    @endif
                @endforeach
            @endif

            {{-- The actual tag display and removal logic will require JavaScript --}}
        </div>

        {{-- Add tags for other filters --}}
        <div class="mt-3 flex flex-wrap gap-2">

        </div>

    </div>

    <div class="flex max-md:flex-col">
        <!-- Banderolle horizontal -->

        <!-- Dans la section des filtres (sidebar) -->
        <div class="w-full md:w-1/4 p-3 bg-gray-50 border-r border-gray-200"
            style="box-shadow: -4px 0 6px -1px rgba(0, 0, 0, 0.1);">

            <!-- Critères du candidat -->
            <h2 class="text-sm font-semibold my-3">Critères du candidat</h2>



            <div class="mt-3">
                <label class="text-xs block mb-1">Casier judiciaire vierge</label>
                <x-select-input id="criminal_record" name="criminal_record"
                    class="block mt-1 w-full text-xs h-10 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value=""></option>
                    <option value="yes" @selected($criminal_record == 'yes')>Oui</option>
                    <option value="no" @selected($criminal_record == 'no')>Non</option>
                </x-select-input>
            </div>

            <!-- Supprimer cette section -->
            <!-- Taux d'activité -->

            <div class="mt-3">
                <label class="text-xs block mb-1">Taux d'activité</label>
                <x-select-input id="work_rate" name="work_rate" class="block mt-1 w-full text-sm" required>
                    <option value=""></option>
                    @foreach (['100', '90-100', '80-100', '90', '80-90', '70-90', '80', '70-80', '60-80', '70', '60-70', '50-70', '60', '50-60', '40-60', '50', '40-50', '30-50', '40', '30-40', '20-40', '20-30', '30', '10-30', '20', '10-20', '10'] as $rate)
                        <option value="{{ $rate }}" @selected($work_rate == $rate)>{{ $rate }}%
                        </option>
                    @endforeach
                </x-select-input>
            </div>

            <!-- Supprimer toutes ces sections de langues -->
            <!-- Languages -->
            <div class="mt-3">
                <label class="text-xs block mb-1">{!! __('candidate_register.langue_s_maternelle') !!}</label>
                <x-select-input id="native_language" name="native_language[]" class="block mt-1 w-full text-sm"
                    multiple>
                    @foreach ($languages as $item)
                        <option value="{{ $item->id }}" @selected(collect($native_language)->contains($item->id))>{{ $item->name }}
                        </option>
                    @endforeach
                </x-select-input>
                <x-input-error :messages="$errors->get('native_language')" class="mt-1 text-xs" />
            </div>

            <div class="mt-3">
                <label class="text-xs block mb-1">{!! __('candidate_register.langue_s_parlxe_s_couramment') !!}</label>
                <x-select-input id="fluent_languages" name="fluent_languages[]" class="block mt-1 w-full text-sm"
                    multiple>
                    @foreach ($languages as $item)
                        <option value="{{ $item->id }}" @selected(collect($fluent_languages)->contains($item->id))>{{ $item->name }}
                        </option>
                    @endforeach
                </x-select-input>
                <x-input-error :messages="$errors->get('fluent_languages')" class="mt-1 text-xs" />
            </div>

            <div class="mt-3">
                <label class="text-xs block mb-1">{!! __('candidate_register.langue_s_parlxe_s_avec_notion_intermxdiaire') !!}</label>
                <x-select-input id="intermediate_languages" name="intermediate_languages[]"
                    class="block mt-1 w-full text-sm" multiple>
                    @foreach ($languages as $item)
                        <option value="{{ $item->id }}" @selected(collect($intermediate_languages)->contains($item->id))>{{ $item->name }}
                        </option>
                    @endforeach
                </x-select-input>
                <x-input-error :messages="$errors->get('intermediate_languages')" class="mt-1 text-xs" />
            </div>

            <div class="mt-3">
                <label class="text-xs block mb-1">{!! __('candidate_register.langue_s_parlxe_s_avec_notion_de_base') !!}</label>
                <x-select-input id="basic_languages" name="basic_languages[]" class="block mt-1 w-full text-sm"
                    multiple>
                    @foreach ($languages as $item)
                        <option value="{{ $item->id }}" @selected(collect($basic_languages)->contains($item->id))>{{ $item->name }}
                        </option>
                    @endforeach
                </x-select-input>
                <x-input-error :messages="$errors->get('basic_languages')" class="mt-1 text-xs" />
            </div>

            <!-- Supprimer cette section -->
            <!-- Permis de conduire -->
            <div class="mt-3">
                <label class="text-xs block mb-1">Permis de conduire</label>
                <x-select-input id="permits" name="permits[]" class="block mt-1 w-full text-sm" required multiple>
                    @foreach ($permits as $item)
                        <option value="{{ $item->id }}" @selected(collect($selectedPermits)->contains($item->id))>{{ $item->name }}
                        </option>
                    @endforeach
                </x-select-input>
            </div>

            <!-- Bouton de recherche -->
            <form id="filter-form" method="GET" action="{{ route('recruter.candidate-selected') }}"
                class="mt-3">
                <!-- Supprimer ou commenter ces inputs cachés correspondants -->
                <input type="hidden" name="work_rate" id="work_rate_input">
                <input type="hidden" name="native_language" id="native_language_input">
                <input type="hidden" name="fluent_languages" id="fluent_languages_input">
                <input type="hidden" name="intermediate_languages" id="intermediate_languages_input">
                <input type="hidden" name="basic_languages" id="basic_languages_input">
                <input type="hidden" name="permits" id="permits_input">

                <!-- Garder les autres inputs cachés -->
                <input type="hidden" name="activity_fields" id="activity_fields_input">
                <input type="hidden" name="desired_professions" id="desired_professions_input">
                <input type="hidden" name="category" id="category_input">
                <input type="hidden" name="location" id="location_input">
                <input type="hidden" name="distance" id="distance_input" value="{{ $distance ?? '' }}">
                <input type="hidden" name="availability" id="availability_input">
                <input type="hidden" name="residence" id="residence_input">
                <input type="hidden" name="criminal_record" id="criminal_record_input">
                <input type="hidden" name="contract_type" id="contract_type_input">
                <input type="hidden" name="sortedBy" id="sortedBy_input" value="{{ $sortedBy }}">
                <input type="hidden" name="country_of_residence" id="country_of_residence_input">
                <input type="hidden" name="commune" id="commune_input">
                <input type="hidden" name="page" value="1">

                <div class="flex gap-2">
                    <button type="submit" id="btn-find"
                        class="flex-1 p-1.5 bg-primary text-white rounded text-sm">Trouver les
                        candidat(e)s</button>
                    <a href="{{ route('recruter.reset-search') }}"
                        class="flex-1 p-1.5 bg-gray-500 text-white rounded hover:bg-gray-600 text-center text-sm">Réinitialiser
                        la recherche</a>
                </div>
            </form>
        </div>
        <!-- Main Content Section -->
        <div class="w-full md:w-3/4 p-4 bg-white">

            <!-- Search Result Header -->
            <div class="flex justify-between items-center mb-4">
                @if (count($candidatesWithDistance) > 0)
                    <span class="text-sm text-gray-700">Affichage de {{ $candidates->firstItem() }} à
                        {{ $candidates->lastItem() }} sur {{ $candidates->total() }} résultats</span>
                    <x-select-input id="sortedBy" class="border rounded p-1.5 text-sm">
                        <option value="">Trier par</option>
                        <option value="random" @selected($sortedBy == 'random')>Tous</option>
                        {{-- <option value="created_at_asc" @selected($sortedBy == 'created_at_asc')>Date : du plus ancien au plus
                            récent</option>
                        <option value="created_at_desc" @selected($sortedBy == 'created_at_desc')>Date : du plus récent au plus
                            ancien</option> --}}
                        <option value="liked" @selected($sortedBy == 'liked')>Favoris</option>
                    </x-select-input>
                @else
                    <span class="text-sm text-gray-700">Aucun résultat trouvé</span>
                @endif
            </div>

            <div class="flex justify-between items-center mb-4">
                <a href="{{ request()->query('sortedBy') === 'liked' ? route('recruter.candidate-selected') : route('recruter.candidate-selected', ['sortedBy' => 'liked']) }}"
                    class="bg-[#0BBBEF] text-white px-4 py-2 rounded-md shadow-md hover:bg-[#0099cc] transition text-sm">
                    {{ request()->query('sortedBy') === 'liked' ? 'Recherche des candidats' : 'Candidat(e)s sélectionné(e)s' }}
                </a>
            </div>

            <!-- Candidate List -->
            @if (count($candidatesWithDistance) > 0)
                <div class="grid gap-4">
                    @foreach ($candidatesWithDistance as $candidateData)
                        @if (!Str::contains($candidateData['candidate']->email, '_deleted_'))
                            <div class="flex items-center justify-between border p-4 rounded-lg bg-white">
                                <div class="flex items-center space-x-3">
                                    @if ($candidateData['candidate']->profilePhoto)
                                        <img src="{{ $candidateData['candidate']->profilePhoto->url ?? asset('images/user.png') }}"
                                            alt="Profile Photo" class="w-10 h-10 rounded-full">
                                    @else
                                        <div class="bg-gray-200 rounded-full w-10 h-10"></div>
                                    @endif
                                    <div>
                                        <h3 class="text-sm font-semibold">{{ $candidateData['candidate']->firstname() }}
                                        </h3>
                                        @if ($candidateData['distance'] !== null)
                                            <h3 class="text-xs">{{ round($candidateData['distance'], 2) }} Km</h3>
                                        @endif
                                        <span class="text-xs font-normal text-gray-500">
                                            {{-- Créé le {{ $candidateData['candidate']->created_at->format('d/m/Y à H\hi') }} --}}
                                            Créé le {{ $candidateData['candidate']->_id }}
                                        </span>
                                    </div>
                                </div>
                                <div class="flex flex-col justify-center items-center gap-2">
                                    <span class="text-xs">Ajouter aux favoris</span>
                                    <button candidate-id="{{ $candidateData['candidate']->id }}"
                                        class="btn-like w-10 h-10 rounded-full duration-300 border {{ !\App\Helpers\UsedUpFunction::isLiked($candidateData['candidate']->id) ? 'text-textNormal border-textNormal hover:text-pink-400 hover:border-pink-300' : 'text-pink-500 border-pink-400 hover:text-textNormal hover:border-textNormal' }}">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                    <a href="{{ route('recruter.candidate-selected.show', ['candidate_id' => $candidateData['candidate']->id]) }}"
                                        class="p-1.5 text-primary border border-primary rounded text-xs">Voir profil</a>
                                </div>
                            </div>
                        @endif
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="flex justify-center mt-4">
                    {{ $candidates->links() }}
                </div>
            @else
                <div class="text-center text-sm text-gray-700 mt-4">
                    Aucun candidat ne correspond à vos critères de recherche.
                </div>
            @endif
        </div>
    </div>

    <!-- Feedback Button Fixed Bottom Right -->
    <div class="feedback-button fixed bottom-4 right-4 z-50">
        <x-feedback-button-guest active="true" />
    </div>
    <x-feedback-modal />
    @isset($priorities, $severities)
        <x-feedback-modal :priorities="$priorities" :severities="$severities" :user="auth()->user()" />
    @endisset

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('filter-form');
            const selectElement = document.getElementById("activity_fields");
            const distanceSlider = document.getElementById('distance');
            const distanceValue = document.getElementById('distanceValue');
            const displayedDistanceValue = document.getElementById('displayedDistanceValue');
            const distanceInput = document.getElementById('distance_input');
            const activeDistance = document.getElementById('activeDistance');
            const distanceRadios = document.querySelectorAll('input[name="distance_radio_options"]');
            const countrySelect = document.getElementById('country_of_residence');
            const communeSelect = document.getElementById('commune');

            // Fonction pour obtenir les paramètres de l'URL
            function getUrlParameter(name) {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get(name);
            }

            // Function to update both distance display spans
            function updateDistanceDisplays(value) {
                if (distanceValue) distanceValue.textContent = value;
                if (displayedDistanceValue) displayedDistanceValue.textContent = value;
            }

            // Fonction pour initialiser un multi-select
            function initializeMultiSelect(selectId, options = {}) {
                const select = document.getElementById(selectId);

                // Supprimer l'ancien multi-select s'il existe
                const existingMultiSelect = select.nextElementSibling;
                if (existingMultiSelect && existingMultiSelect.classList.contains('multi-select-tag')) {
                    existingMultiSelect.remove();
                }

                // Créer le nouveau multi-select avec l'événement onChange
                new MultiSelectTag(selectId, {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    },
                    onChange: function() {
                        updateHiddenInputs();
                        form.submit();
                    },
                    ...options
                });
            }

            // Fonction pour charger les professions
            async function loadProfessions(activityIds) {
                const professionContainer = document.querySelector('.desired_professions');
                // Toujours afficher le container
                professionContainer.classList.remove('hiddenc');
                try {
                    let url = '/get-all-profession';
                    if (activityIds.length > 0) {
                        url += `?ids=${activityIds.join(',')}`;
                    }
                    const response = await fetch(url);
                    const data = await response.json();
                    let select = document.getElementById("desired_professions");
                    select.innerHTML = '';
                    // Récupérer les professions de l'URL
                    const urlProfessions = getUrlParameter('desired_professions');
                    const selectedProfessions = urlProfessions ? urlProfessions.split(',') : [];
                    // Ajouter les options
                    data.forEach(item => {
                        let option = document.createElement("option");
                        option.value = item.id;
                        option.textContent = item.name + (item.field_activity_id === null || item.field_activity_id === undefined ? ' (hors domaine)' : '');
                        if (item.field_activity_id === null || item.field_activity_id === undefined) {
                            option.style.backgroundColor = '#ffe4b2';
                            option.style.color = '#b45309';
                            option.style.fontStyle = 'italic';
                        }
                        if (selectedProfessions.includes(item.id.toString())) {
                            option.selected = true;
                        }
                        select.appendChild(option);
                    });
                    // Initialiser le multi-select pour les professions
                    initializeMultiSelect('desired_professions');
                    return Promise.resolve();
                } catch (error) {
                    console.error('Erreur lors de la récupération des professions:', error);
                    return Promise.reject(error);
                }
            }

            // Initialiser les domaines d'activité
            const urlActivityFields = getUrlParameter('activity_fields');
            if (urlActivityFields) {
                const selectedIds = urlActivityFields.split(',');
                Array.from(selectElement.options).forEach(option => {
                    if (selectedIds.includes(option.value)) {
                        option.selected = true;
                    }
                });

                // Initialiser le multi-select pour les domaines d'activité
                initializeMultiSelect('activity_fields', {
                    onChange: function() {
                        let selectedOptions = Array.from(selectElement.selectedOptions);
                        let activityIds = selectedOptions.map(option => option.getAttribute(
                            "id-activity"));
                        loadProfessions(activityIds).then(() => {
                            // Après chargement des professions, désélectionner celles qui ne sont plus dans la liste
                            const professionSelect = document.getElementById('desired_professions');
                            const validProfessionIds = Array.from(professionSelect.options).map(opt => opt.value);
                            Array.from(professionSelect.selectedOptions).forEach(opt => {
                                if (!validProfessionIds.includes(opt.value)) {
                                    opt.selected = false;
                                }
                            });
                            updateHiddenInputs();
                            form.submit();
                        });
                    }
                });

                // Charger les professions correspondantes
                const activityIds = selectedIds.map(id => {
                    const option = Array.from(selectElement.options).find(opt => opt.value === id);
                    return option ? option.getAttribute("id-activity") : null;
                }).filter(id => id !== null);

                loadProfessions(activityIds);
            } else {
                // Initialiser le multi-select même s'il n'y a pas de sélection
                initializeMultiSelect('activity_fields', {
                    onChange: function() {
                        let selectedOptions = Array.from(selectElement.selectedOptions);
                        let activityIds = selectedOptions.map(option => option.getAttribute(
                            "id-activity"));
                        loadProfessions(activityIds).then(() => {
                            // Après chargement des professions, désélectionner celles qui ne sont plus dans la liste
                            const professionSelect = document.getElementById('desired_professions');
                            const validProfessionIds = Array.from(professionSelect.options).map(opt => opt.value);
                            Array.from(professionSelect.selectedOptions).forEach(opt => {
                                if (!validProfessionIds.includes(opt.value)) {
                                    opt.selected = false;
                                }
                            });
                            updateHiddenInputs();
                            form.submit();
                        });
                    }
                });
            }

            // Initialiser les autres multi-select
            ['native_language', 'fluent_languages', 'intermediate_languages', 'basic_languages', 'permits',
                'contract_type', 'desired_professions'
            ].forEach(id => {
                const select = document.getElementById(id);
                if (select) {
                    const urlValue = getUrlParameter(id);
                    if (urlValue) {
                        const values = urlValue.split(',');
                        Array.from(select.options).forEach(option => {
                            option.selected = values.includes(option.value);
                        });
                    }
                    initializeMultiSelect(id);
                }
            });

            // Ajouter des écouteurs d'événements pour tous les autres éléments de filtre
            const filterElements = [
                'category',
                'country_of_residence',
                'commune',
                'availability',
                'residence',
                'criminal_record',
                'work_rate',
                'sortedBy'
            ];

            filterElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', function() {
                        updateHiddenInputs();
                        form.submit();
                    });
                }
            });

            // Mise à jour de l'affichage du slider
            if (distanceSlider) {
                distanceSlider.addEventListener('input', function() {
                    updateDistanceDisplays(this.value);
                    distanceInput.value = this.value;
                    if (activeDistance) activeDistance.checked = true; // Ensure activeDistance is checked
                    distanceRadios.forEach(radio => radio.checked = false); // Uncheck radios
                });

                // Add change event to slider to submit form when user releases slider
                distanceSlider.addEventListener('change', function() {
                    updateHiddenInputs();
                    form.submit();
                });
            }

            // Gestion des boutons radio
            distanceRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.checked) {
                        updateDistanceDisplays(this.value);
                        if (distanceSlider) distanceSlider.value = this.value;
                        distanceInput.value = this.value;
                        if (activeDistance) activeDistance.checked =
                            true; // Ensure activeDistance is checked
                        updateHiddenInputs();
                        form.submit();
                    }
                });
            });

            // Gestion de la checkbox activeDistance
            if (activeDistance) {
                activeDistance.addEventListener('change', function() {
                    if (!this.checked) {
                        // If unchecked, clear distance related inputs and reset displays
                        distanceInput.value = '';
                        distanceRadios.forEach(radio => radio.checked = false);
                        if (distanceSlider) {
                            distanceSlider.value = 50;
                            updateDistanceDisplays('50');
                        }
                    } else {
                        // If checked, ensure distanceInput has a value (from slider or default)
                        const selectedRadio = document.querySelector(
                            'input[name="distance_radio_options"]:checked');
                        if (selectedRadio) {
                            distanceInput.value = selectedRadio.value;
                        } else if (distanceSlider) {
                            distanceInput.value = distanceSlider.value;
                        } else {
                            // Default to 50 if nothing is selected or slider is not present
                            distanceInput.value = '50';
                            if (distanceSlider) {
                                distanceSlider.value = 50;
                                updateDistanceDisplays('50');
                            }
                        }
                    }
                    // Update hidden inputs and submit immediately
                    updateHiddenInputs();
                    form.submit();
                });
            }

            // Fonction pour mettre à jour les champs cachés
            function updateHiddenInputs() {
                // Mise à jour des champs multiples
                const multiSelects = {
                    'activity_fields': 'activity_fields_input',
                    'desired_professions': 'desired_professions_input',
                    'contract_type': 'contract_type_input',
                    'native_language': 'native_language_input',
                    'fluent_languages': 'fluent_languages_input',
                    'intermediate_languages': 'intermediate_languages_input',
                    'basic_languages': 'basic_languages_input',
                    'permits': 'permits_input'
                };

                Object.entries(multiSelects).forEach(([selectId, inputId]) => {
                    const select = document.getElementById(selectId);
                    const input = document.getElementById(inputId);
                    if (select && input) {
                        const selectedOptions = Array.from(select.selectedOptions).map(o => o.value);
                        input.value = selectedOptions.join(',');
                    }
                });

                // Mise à jour des champs simples
                const simpleSelects = {
                    'category': 'category_input',
                    'location': 'location_input',
                    'availability': 'availability_input',
                    'residence': 'residence_input',
                    'criminal_record': 'criminal_record_input',
                    'work_rate': 'work_rate_input',
                    'sortedBy': 'sortedBy_input',
                    'country_of_residence': 'country_of_residence_input',
                    'commune': 'commune_input'
                };

                Object.entries(simpleSelects).forEach(([selectId, inputId]) => {
                    const select = document.getElementById(selectId);
                    const input = document.getElementById(inputId);
                    if (select && input) {
                        input.value = select.value;
                    }
                });

                // Gestion spéciale de la distance
                if (activeDistance && !activeDistance.checked) {
                    distanceInput.value = '';
                } else if (activeDistance) { // Only set if activeDistance checkbox exists and is checked
                    // Ensure distanceInput is set based on current UI state, or default to 50
                    const selectedRadio = document.querySelector('input[name="distance_radio_options"]:checked');
                    if (selectedRadio) {
                        distanceInput.value = selectedRadio.value;
                    } else if (distanceSlider) {
                        distanceInput.value = distanceSlider.value;
                    } else {
                        // Default to 50 if activeDistance is checked but no slider/radio selected
                        distanceInput.value = '50';
                    }
                }
                // If activeDistance is null (user not logged in with address), distanceInput remains as is.
            }

            // Mettre à jour les champs cachés avant la soumission
            form.addEventListener('submit', function(e) {
                updateHiddenInputs();
            });

            // Soumission du formulaire avec la touche Entrée
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' || (e.ctrlKey && e.key === 'Enter')) {
                    form.submit();
                }
            });

            // Gestion des boutons like
            document.querySelectorAll('.btn-like').forEach(btn => {
                btn?.addEventListener('click', function() {
                    const candidateId = this.getAttribute('candidate-id');
                    this.disabled = true;
                    fetch(`{{ route('recruter.like-candidate', ['candidate_id' => 'CANDIDATE_ID']) }}`
                            .replace('CANDIDATE_ID', candidateId))
                        .then(response => response.json())
                        .then(data => {
                            if (data.liked) {
                                this.classList.add('text-pink-500', 'border-pink-400',
                                    'hover:text-textNormal', 'hover:border-textNormal');
                                this.classList.remove('text-textNormal', 'border-textNormal',
                                    'hover:text-pink-400', 'hover:border-pink-300');
                            } else {
                                this.classList.add('text-textNormal', 'border-textNormal',
                                    'hover:text-pink-400', 'hover:border-pink-300');
                                this.classList.remove('text-pink-500', 'border-pink-400',
                                    'hover:text-textNormal', 'hover:border-textNormal');
                            }
                            this.disabled = false;
                        });
                });
            });

            // Mettre à jour les champs cachés quand le pays change
            if (countrySelect) {
                countrySelect.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    const countryId = selectedOption.getAttribute('id-country');

                    // Mettre à jour l'input caché
                    document.getElementById('country_of_residence_input').value = this.value;

                    // Mettre à jour les champs cachés et soumettre le formulaire
                    updateHiddenInputs();
                    form.submit();
                });
            }

            // Mettre à jour les champs cachés quand la commune change
            if (communeSelect) {
                communeSelect.addEventListener('change', function() {
                    document.getElementById('commune_input').value = this.value;
                    updateHiddenInputs();
                    form.submit();
                });
            }

            // Affiche le bloc si des professions sont déjà sélectionnées (historique)
            const desiredProfessions = @json($desired_professions);
            const professionContainer = document.querySelector('.desired_professions');
            if (desiredProfessions && desiredProfessions.length > 0) {
                professionContainer.classList.remove('hiddenc');
                // Réinitialise le multi-select custom pour appliquer le bon design
                initializeMultiSelect('desired_professions');
            }

            // Add event listener for filter tag removal
            document.getElementById('filter-tags-container')?.addEventListener('click', function(event) {
                const target = event.target;
                if (target.classList.contains('fa-times') || target.parentElement.classList.contains(
                        'fa-times')) {
                    const tagSpan = target.closest('span');
                    if (tagSpan) {
                        const filterName = tagSpan.getAttribute('data-filter-name');
                        const filterValue = tagSpan.getAttribute('data-filter-value');
                        const form = document.getElementById('filter-form');

                        // Get the actual filter element (select input)
                        const filterElement = document.getElementById(filterName);

                        if (filterElement) {
                            // Determine if it's a multi-select or single-select
                            if (filterElement.multiple) {
                                // For multi-selects, find and deselect the specific option
                                Array.from(filterElement.options).forEach(option => {
                                    if (option.value === filterValue) {
                                        option.selected = false;
                                    }
                                });
                            } else { // Handle single-selects and the distance filter
                                if (filterName === 'distance') {
                                    // Clear distance related inputs
                                    distanceInput.value = '';
                                    if (distanceSlider) distanceSlider.value = 50;
                                    updateDistanceDisplays(50);
                                    distanceRadios.forEach(radio => radio.checked = false);
                                    activeDistance.checked =
                                        false; // Uncheck activeDistance when filter tag is removed
                                } else {
                                    // For single-selects, clear the value
                                    filterElement.value = '';
                                }
                            }

                            // Update hidden inputs to reflect the change and then submit the form
                            updateHiddenInputs();
                            form.submit();
                        }
                    }
                }
            });

            // Initial call to update hidden inputs on page load
            updateHiddenInputs();
            updateDistanceDisplays('{{ $distance ?? 50 }}'); // Set initial display on button

            // Ensure distanceInput is correctly set on initial load based on $distance and activeDistance state
            if (activeDistance) {
                if ('{{ $distance }}' !== '') { // If $distance is not null, it means a distance is set
                    activeDistance.checked = true;
                    distanceInput.value = '{{ $distance }}';
                    if (distanceSlider) distanceSlider.value = '{{ $distance }}';
                    // Also ensure correct radio button is checked if applicable
                    distanceRadios.forEach(radio => {
                        if (radio.value === '{{ $distance }}') {
                            radio.checked = true;
                        }
                    });
                } else {
                    activeDistance.checked = false;
                    distanceInput.value = '';
                    if (distanceSlider) distanceSlider.value = 50; // Default slider to 50 if no distance
                }
            }

            // Add event listeners for other filter elements to update hidden inputs and submit form
            const filterElementsToSubmit = [
                'category',
                'country_of_residence',
                'commune',
                'availability',
                'residence',
                'criminal_record',
                'work_rate',
                'sortedBy'
                // Multi-selects are handled by their onChange or by tag removal
            ];

            filterElementsToSubmit.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', function() {
                        updateHiddenInputs();
                        form.submit();
                    });
                }
            });
        });

        function reinitializeMultiSelects() {
            // Dummy function to ensure it exists for previous calls, can be removed if not used elsewhere
        }
    </script>
</x-app-layout>
