<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
        @if(isset($isAdminCopy) && $isAdminCopy)
            Nouvelle inscription - Copie admin - {{ config('app.name') }}
        @else
            Bienvenue sur {{ config('app.name') }} - Vérification de l'adresse e-mail
        @endif
    </title>
</head>

<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f9f9f9;">
    <div
        style="max-width: 600px; margin: 20px auto; background: #ffffff; border: 1px solid #ddd; border-radius: 10px; overflow: hidden; box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);">
        <!-- Corps -->
        <div style="padding: 20px;">
            @if(isset($isAdminCopy) && $isAdminCopy)
                <!-- Message pour la copie admin -->
                <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                    <p style="font-size: 16px; color: #856404; margin: 0; font-weight: bold;">
                        📧 COPIE ADMIN - Nouvelle inscription détectée
                    </p>
                </div>
                
                <p style="font-size: 16px; color: #555; line-height: 1.5; margin-bottom: 20px;">
                    <strong>Bonjour Admin,</strong><br><br>
                    Une nouvelle inscription a été effectuée sur la plateforme Cyclone Placement.<br><br>
                    
                    <strong>Détails de l'inscription :</strong><br>
                    • Nom d'utilisateur : {{ $fullname }}<br>
                    • Adresse e-mail : {{ $email }}<br>
                    • Type de compte : {{ ucfirst($roleSlug) }}<br>
                    • Date d'inscription : {{ now()->format('d/m/Y H:i:s') }}<br><br>
                    
                    L'utilisateur a reçu un email de vérification à l'adresse : {{ $email }}
                </p>
            @else
                <!-- Message normal pour l'utilisateur -->
                <p style="font-size: 16px; color: #555; line-height: 1.5; margin-bottom: 20px;">
                    @if ($roleSlug == 'candidate')
                        <strong>Bonjour </strong>,<br>
                        <p style="margin: 15px 0;">Félicitations !!!</p>
                        <p style="margin: 15px 0;">Votre inscription est presque terminée. Cliquez simplement sur le lien ci-dessous pour finaliser votre profil et le rendre visible aux recruteurs :</p>
                        <a href="{{ $actionUrl }}" style="color: #007bff; text-decoration: none;">{{ $actionUrl }}</a>
                        <p style="margin: 15px 0;">Si vous arrivez sur une page de connexion, veuillez vous connecter pour valider votre inscription.</p>
                        <p style="margin: 15px 0;">Vos informations:<br>
                        Nom d'utilisateur : {{ $fullname }}<br>
                        Adresse e-mail : {{ $email }}</p>
                        <p style="margin: 15px 0;">Une fois finalisé, les recruteurs pourront accéder à votre profil pour une première prise de contact en vue d'un engagement. Vous pourrez à tout moment le modifier ou le supprimer.</p>
                        <p style="margin: 15px 0;">Nous vous souhaitons plein succès dans vos aspirations professionnelles.</p>
                    @else
                        Nous confirmons votre inscription sur la plateforme Cyclone Placement. Vous pouvez dès à présent y adhérer en vue de trouver le/s candidat/s répondant à vos besoins. Nous vous souhaitons plein succès dans vos recherches. La team Cyclone Placement.
                    @endif
                </p>

                @if ($roleSlug != 'candidate')
                <p style="font-size: 14px; color: #777; line-height: 1.5; margin-top: 20px;">
                    Information
                </p>

                <div style="text-align: left; margin: 20px 0;">
                    <p style="font-size: 14px; color: #777; line-height: 1.5; margin-top: 20px;">
                        <span>Nom d'utilisateur :</span>
                        <span>
                            {{ $fullname }}
                        </span>
                    </p>
                    <p style="font-size: 14px; color: #777; line-height: 1.5; margin-top: 20px;">
                        <span>Adresse e-mail :</span>
                        <span>
                            {{ $email }}
                        </span>
                    </p>
                    <p style="font-size: 14px; color: #777; line-height: 1.5; margin-top: 20px;">
                        <span>URL d'approbation :</span>
                        <span>
                            <a href="{{ $actionUrl }}"
                                style="display: inline-block; padding: 10px 20px; color: #007bff; text-decoration: none; font-size: 16px; border-radius: 5px;">
                                {{ $actionUrl }}
                            </a>
                        </span>
                    </p>
                </div>
                @endif
            @endif
        </div>

        <!-- Pied de page -->
        <div
            style="background: #f9f9f9; color: #777; text-align: center; padding: 15px; font-size: 12px; border-top: 1px solid #ddd;">
            <p style="margin: 0;">Cordialement,<br><strong>La team Cyclone Placement</strong></p>
        </div>
    </div>
</body>

</html>
