<x-guest-layout>
    <x-slot name="title">Inscription Candidat - Étape 2/7</x-slot>

    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">
                    Dites-nous qui vous êtes
                </h2>
                <p class="text-gray-600">
                    Quelques informations personnelles pour mieux vous connaître
                </p>
            </div>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <!-- Progress Bar -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">Étape 2 sur 7</span>
                        <span class="text-sm text-gray-500">29%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full" style="width: 29%"></div>
                    </div>
                </div>

                <form class="space-y-6" action="{{ route('candidate.registerStore') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="step" value="2">
                    
                    <!-- Message de succès -->
                    @if(session('success'))
                        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-4">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif
                    
                    <!-- Données des étapes précédentes -->
                    <input type="hidden" name="email" value="{{ session('registration_data.email') }}">
                    <input type="hidden" name="password" value="{{ session('registration_data.password') }}">
                    <input type="hidden" name="password_confirmation" value="{{ session('registration_data.password_confirmation') }}">
                    @if(session('registration_data.profile_picture'))
                        <input type="hidden" name="profile_picture" value="{{ session('registration_data.profile_picture') }}">
                    @endif

                    <!-- Prénom -->
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700">
                            Prénom *
                        </label>
                        <div class="mt-1">
                            <input id="first_name" name="first_name" type="text" autocomplete="given-name" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('first_name') border-red-500 @enderror"
                                value="{{ old('first_name') }}">
                        </div>
                        @error('first_name')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Nom -->
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700">
                            Nom *
                        </label>
                        <div class="mt-1">
                            <input id="last_name" name="last_name" type="text" autocomplete="family-name" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('last_name') border-red-500 @enderror"
                                value="{{ old('last_name') }}">
                        </div>
                        @error('last_name')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Date de naissance -->
                    <div>
                        <label for="date_of_birth" class="block text-sm font-medium text-gray-700">
                            Date de naissance *
                        </label>
                        <div class="mt-1">
                            <input id="date_of_birth" name="date_of_birth" type="date" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('date_of_birth') border-red-500 @enderror"
                                value="{{ old('date_of_birth') }}">
                        </div>
                        @error('date_of_birth')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Catégorie -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700">
                            Catégorie *
                        </label>
                        <div class="mt-1">
                            <select id="category" name="category" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('category') border-red-500 @enderror">
                                <option value="">Sélectionnez une catégorie</option>
                                <option value="current_profiles" {{ old('category') == 'current_profiles' ? 'selected' : '' }}>Profils actuels</option>
                                <option value="retired" {{ old('category') == 'retired' ? 'selected' : '' }}>Retraités</option>
                                <option value="migrants" {{ old('category') == 'migrants' ? 'selected' : '' }}>Migrants</option>
                                <option value="students" {{ old('category') == 'students' ? 'selected' : '' }}>Étudiants</option>
                            </select>
                        </div>
                        @error('category')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Téléphone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700">
                            Téléphone *
                        </label>
                        <div class="mt-1">
                            <input id="phone" name="phone" type="tel" autocomplete="tel" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('phone') border-red-500 @enderror"
                                value="{{ old('phone') }}">
                        </div>
                        @error('phone')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="flex items-center justify-between">
                        <a href="{{ route('candidate.registerStep1') }}" 
                           class="text-primary hover:text-primary-dark text-sm font-medium">
                            ← Précédent
                        </a>
                        <button type="submit"
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            Suivant
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-guest-layout> 