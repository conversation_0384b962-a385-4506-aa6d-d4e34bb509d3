<x-guest-layout>
    <x-slot name="title">Inscription Candidat - Étape 3/7</x-slot>

    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">
                    Où travaillez-vous légalement ?
                </h2>
                <p class="text-gray-600">
                    Informations légales et de localisation
                </p>
            </div>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <!-- Progress Bar -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">Étape 3 sur 7</span>
                        <span class="text-sm text-gray-500">43%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full" style="width: 43%"></div>
                    </div>
                </div>

                <form class="space-y-6" action="{{ route('candidate.registerStore') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="step" value="3">
                    
                    <!-- Message de succès -->
                    @if(session('success'))
                        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-4">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif
                    
                    <!-- Données des étapes précédentes -->
                    @foreach(['email', 'password', 'password_confirmation', 'profile_picture', 'first_name', 'last_name', 'date_of_birth', 'category', 'phone'] as $field)
                        @if(session("registration_data.$field"))
                            <input type="hidden" name="{{ $field }}" value="{{ session("registration_data.$field") }}">
                        @endif
                    @endforeach

                    <!-- Statut légal -->
                    <div>
                        <label for="residence" class="block text-sm font-medium text-gray-700">
                            Statut légal *
                        </label>
                        <div class="mt-1">
                            <select id="residence" name="residence" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('residence') border-red-500 @enderror">
                                <option value="">Sélectionnez votre statut</option>
                                <option value="suisse" {{ old('residence') == 'suisse' ? 'selected' : '' }}>Suisse</option>
                                @foreach($residencePermits as $permit)
                                    <option value="{{ $permit->id }}" {{ old('residence') == $permit->id ? 'selected' : '' }}>
                                        {{ $permit->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @error('residence')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Nom -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">
                            Nom *
                        </label>
                        <div class="mt-1">
                            <input id="name" name="name" type="text" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('name') border-red-500 @enderror"
                                value="{{ old('name') }}">
                        </div>
                        @error('name')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Pays de résidence -->
                    <div>
                        <label for="country_of_residence" class="block text-sm font-medium text-gray-700">
                            Pays de résidence *
                        </label>
                        <div class="mt-1">
                            <select id="country_of_residence" name="country_of_residence" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('country_of_residence') border-red-500 @enderror">
                                <option value="">Sélectionnez un pays</option>
                                @foreach($countries as $country)
                                    <option value="{{ $country->id }}" {{ old('country_of_residence') == $country->id ? 'selected' : '' }}>
                                        {{ $country->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @error('country_of_residence')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Canton/Région -->
                    <div>
                        <label for="commune" class="block text-sm font-medium text-gray-700">
                            Canton/Région *
                        </label>
                        <div class="mt-1">
                            <input id="commune" name="commune" type="text" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('commune') border-red-500 @enderror"
                                value="{{ old('commune') }}">
                        </div>
                        @error('commune')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Casier judiciaire -->
                    <div>
                        <label for="criminal_record" class="block text-sm font-medium text-gray-700">
                            Casier judiciaire *
                        </label>
                        <div class="mt-1">
                            <select id="criminal_record" name="criminal_record" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('criminal_record') border-red-500 @enderror">
                                <option value="">Sélectionnez une option</option>
                                <option value="yes" {{ old('criminal_record') == 'yes' ? 'selected' : '' }}>Oui</option>
                                <option value="no" {{ old('criminal_record') == 'no' ? 'selected' : '' }}>Non</option>
                                <option value="skip" {{ old('criminal_record') == 'skip' ? 'selected' : '' }}>Préfère ne pas dire</option>
                            </select>
                        </div>
                        @error('criminal_record')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="flex items-center justify-between">
                        <a href="{{ route('candidate.registerStep2') }}" 
                           class="text-primary hover:text-primary-dark text-sm font-medium">
                            ← Précédent
                        </a>
                        <button type="submit"
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            Suivant
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-guest-layout> 