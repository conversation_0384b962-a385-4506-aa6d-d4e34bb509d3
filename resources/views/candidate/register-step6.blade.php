<x-guest-layout>
    <x-slot name="title">Inscription Candidat - Étape 6/7</x-slot>

    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">
                    Votre disponibilité
                </h2>
                <p class="text-gray-600">
                    Quand pouvez-vous travailler ?
                </p>
            </div>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <!-- Progress Bar -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">Étape 6 sur 7</span>
                        <span class="text-sm text-gray-500">86%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full" style="width: 86%"></div>
                    </div>
                </div>

                <form class="space-y-6" action="{{ route('candidate.registerStore') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="step" value="6">
                    
                    <!-- Message de succès -->
                    @if(session('success'))
                        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-4">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif
                    
                    <!-- Données des étapes précédentes -->
                    @foreach(['email', 'password', 'password_confirmation', 'profile_picture', 'first_name', 'last_name', 'date_of_birth', 'category', 'phone', 'residence', 'name', 'country_of_residence', 'commune', 'criminal_record', 'vehicle', 'permits', 'profession_1', 'duration_1', 'activity_fields', 'professions_list', 'formations', 'job_types', 'contract_type'] as $field)
                        @if(session("registration_data.$field"))
                            <input type="hidden" name="{{ $field }}" value="{{ session("registration_data.$field") }}">
                        @endif
                    @endforeach

                    <!-- Disponibilité -->
                    <div>
                        <label for="availability" class="block text-sm font-medium text-gray-700">
                            Disponibilité *
                        </label>
                        <div class="mt-1">
                            <select id="availability" name="availability" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('availability') border-red-500 @enderror">
                                <option value="">Sélectionnez votre disponibilité</option>
                                @foreach($responsibilities as $responsibility)
                                    <option value="{{ $responsibility->id }}" {{ old('availability') == $responsibility->id ? 'selected' : '' }}>
                                        {{ $responsibility->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @error('availability')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Taux d'activité -->
                    <div>
                        <label for="work_rate" class="block text-sm font-medium text-gray-700">
                            Taux d'activité *
                        </label>
                        <div class="mt-1">
                            <select id="work_rate" name="work_rate" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('work_rate') border-red-500 @enderror">
                                <option value="">Sélectionnez votre taux d'activité</option>
                                <option value="100%" {{ old('work_rate') == '100%' ? 'selected' : '' }}>100%</option>
                                <option value="80%" {{ old('work_rate') == '80%' ? 'selected' : '' }}>80%</option>
                                <option value="60%" {{ old('work_rate') == '60%' ? 'selected' : '' }}>60%</option>
                                <option value="50%" {{ old('work_rate') == '50%' ? 'selected' : '' }}>50%</option>
                                <option value="40%" {{ old('work_rate') == '40%' ? 'selected' : '' }}>40%</option>
                                <option value="30%" {{ old('work_rate') == '30%' ? 'selected' : '' }}>30%</option>
                                <option value="20%" {{ old('work_rate') == '20%' ? 'selected' : '' }}>20%</option>
                                <option value="Autre" {{ old('work_rate') == 'Autre' ? 'selected' : '' }}>Autre</option>
                            </select>
                        </div>
                        @error('work_rate')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Langues -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Langues
                        </label>
                        
                        <!-- Langue maternelle -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Langue maternelle</label>
                            <div class="space-y-2">
                                @foreach(['Français', 'Allemand', 'Italien', 'Anglais', 'Espagnol', 'Portugais', 'Autre'] as $lang)
                                    <div class="flex items-center">
                                        <input id="native_{{ $lang }}" name="native_language[]" type="checkbox" value="{{ $lang }}"
                                            class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                            {{ in_array($lang, old('native_language', [])) ? 'checked' : '' }}>
                                        <label for="native_{{ $lang }}" class="ml-2 block text-sm text-gray-900">{{ $lang }}</label>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Langues parlées couramment -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Langues parlées couramment</label>
                            <div class="space-y-2">
                                @foreach(['Français', 'Allemand', 'Italien', 'Anglais', 'Espagnol', 'Portugais', 'Autre'] as $lang)
                                    <div class="flex items-center">
                                        <input id="fluent_{{ $lang }}" name="fluent_languages[]" type="checkbox" value="{{ $lang }}"
                                            class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                            {{ in_array($lang, old('fluent_languages', [])) ? 'checked' : '' }}>
                                        <label for="fluent_{{ $lang }}" class="ml-2 block text-sm text-gray-900">{{ $lang }}</label>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Langues de niveau intermédiaire -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Langues de niveau intermédiaire</label>
                            <div class="space-y-2">
                                @foreach(['Français', 'Allemand', 'Italien', 'Anglais', 'Espagnol', 'Portugais', 'Autre'] as $lang)
                                    <div class="flex items-center">
                                        <input id="intermediate_{{ $lang }}" name="intermediate_languages[]" type="checkbox" value="{{ $lang }}"
                                            class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                            {{ in_array($lang, old('intermediate_languages', [])) ? 'checked' : '' }}>
                                        <label for="intermediate_{{ $lang }}" class="ml-2 block text-sm text-gray-900">{{ $lang }}</label>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Langues de niveau basique -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Langues de niveau basique</label>
                            <div class="space-y-2">
                                @foreach(['Français', 'Allemand', 'Italien', 'Anglais', 'Espagnol', 'Portugais', 'Autre'] as $lang)
                                    <div class="flex items-center">
                                        <input id="basic_{{ $lang }}" name="basic_languages[]" type="checkbox" value="{{ $lang }}"
                                            class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                            {{ in_array($lang, old('basic_languages', [])) ? 'checked' : '' }}>
                                        <label for="basic_{{ $lang }}" class="ml-2 block text-sm text-gray-900">{{ $lang }}</label>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <a href="{{ route('candidate.registerStep5') }}" 
                           class="text-primary hover:text-primary-dark text-sm font-medium">
                            ← Précédent
                        </a>
                        <button type="submit"
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            Suivant
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-guest-layout> 