@if(\App\Helpers\UsedUpFunction::isCandidate())
    <x-candidate-dashboard-layout>
        <!-- Title -->
        <div class="border-l-4 rounded-sm border-primary px-6 py-2 mb-8">
            <h1 class="text-2xl font-semibold text-primary">
                {!! __('candidate_update-password.changer_le_mot_de_passe') !!}
            </h1>
        </div>

        <!-- Profile Form -->
        <section class="space-y-6">
            <form method="POST" action="{{ route('candidate.update-password.put') }}" enctype="multipart/form-data"
                class="flex justify-center">
                @csrf
                @method('PUT')

                <!-- Delete Profile -->
                <div class="border border-gray-300 rounded p-10 max-w-3xl">

                    <!-- Session Status -->
                    <x-auth-session-status class="mb-4" :status="session('status')" />

                    <div class="mb-6">
                        <x-input-label for="current_password"> Actuel mot de passe </x-input-label>
                        <x-text-input id="current_password" class="block mt-1 w-full" type="password"
                            name="current_password" :value="old('current_password')" required />
                        <x-input-error :messages="$errors->get('current_password')" class="mt-2" />
                    </div>

                    <div class="mb-6">
                        <x-input-label for="password"> Nouveau mot de passe </x-input-label>
                        <x-text-input id="password" class="block mt-1 w-full" type="password" name="password"
                            :value="old('password')" required />
                        <x-input-error :messages="$errors->get('password')" class="mt-2" />
                    </div>

                    <div class="mb-10">
                        <x-input-label for="password_confirmation"> Confirmer le nouveau mot de passe </x-input-label>
                        <x-text-input id="password_confirmation" class="block mt-1 w-full" type="password" :value="old('password_confirmation')"
                            name="password_confirmation" required />
                        <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
                    </div>

                    <div class="flex justify-end mt-10">
                        <x-primary-button type="submit">
                            {!! __('candidate_update-password.changer_le_mot_de_passe') !!}
                        </x-primary-button>
                    </div>

            </form>
            <div class="feedback-button fixed bottom-4 right-4">
                <x-feedback-button-guest active="true" />
               
            </div>
            <x-feedback-modal />
                        @isset($priorities, $severities)
                            <x-feedback-modal :priorities="$priorities" :severities="$severities" :user="auth()->user()" />
                        @endisset
        </section>
    </x-candidate-dashboard-layout>
@elseif(\App\Helpers\UsedUpFunction::isRecruter())
    <x-recruter-dashboard-layout>
        <!-- Title -->
        <div class="border-l-4 rounded-sm border-primary px-6 py-2 mb-8">
            <h1 class="text-2xl font-semibold text-primary">
                {!! __('candidate_update-password.changer_le_mot_de_passe') !!}
            </h1>
        </div>

        <!-- Profile Form -->
        <section class="space-y-6">
            <form method="POST" action="{{ route('candidate.update-password.put') }}" enctype="multipart/form-data"
                class="flex justify-center">
                @csrf
                @method('PUT')

                <!-- Delete Profile -->
                <div class="border border-gray-300 rounded p-10 max-w-3xl">

                    <!-- Session Status -->
                    <x-auth-session-status class="mb-4" :status="session('status')" />

                    <div class="mb-6">
                        <x-input-label for="current_password"> Actuel mot de passe </x-input-label>
                        <x-text-input id="current_password" class="block mt-1 w-full" type="password"
                            name="current_password" :value="old('current_password')" required />
                        <x-input-error :messages="$errors->get('current_password')" class="mt-2" />
                    </div>

                    <div class="mb-6">
                        <x-input-label for="password"> Nouveau mot de passe </x-input-label>
                        <x-text-input id="password" class="block mt-1 w-full" type="password" name="password"
                            :value="old('password')" required />
                        <x-input-error :messages="$errors->get('password')" class="mt-2" />
                    </div>

                    <div class="mb-10">
                        <x-input-label for="password_confirmation"> Confirmer le nouveau mot de passe </x-input-label>
                        <x-text-input id="password_confirmation" class="block mt-1 w-full" type="password" :value="old('password_confirmation')"
                            name="password_confirmation" required />
                        <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
                    </div>

                    <div class="flex justify-end mt-10">
                        <x-primary-button type="submit">
                            {!! __('candidate_update-password.changer_le_mot_de_passe') !!}
                        </x-primary-button>
                    </div>

            </form>
            <div class="feedback-button fixed bottom-4 right-4">
                <x-feedback-button-guest active="true" />
            </div>
            <x-feedback-modal />
                        @isset($priorities, $severities)
                            <x-feedback-modal :priorities="$priorities" :severities="$severities" :user="auth()->user()" />
                        @endisset
        </section>
    </x-recruter-dashboard-layout>
@endif
