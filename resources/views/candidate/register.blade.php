<x-guest-layout title="Cyclone Placement | Inscription Candidat">
    <div class="flex justify-center items-center w-full py-6">
        <div class="sm:w-[70rem] max-w-3xl bg-white p-10 rounded-sm">
            <!-- Session Status -->
            <x-auth-session-status class="mb-4" :status="session('status')" />

            <!-- Debug errors -->
            @if ($errors->any())
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <!-- Progress Bar -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Progression</span>
                    <span class="text-sm text-gray-500" id="progress-text">1/7</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-primary h-2 rounded-full transition-all duration-300" id="progress-bar" style="width: 14%"></div>
                </div>
            </div>

            <!-- Step Content -->
            <div id="step-content">
                <!-- Step 1: Account Creation -->
                <div id="step-1" class="step active">
                    <div class="text-center mb-8">
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">
                            {{-- Créez votre compte en quelques secondes --}}
                        </h2>
                        <p class="text-gray-600">
                            {{-- Commencez votre parcours professionnel avec nous --}}
                        </p>
                    </div>

                    <form method="POST" action="{{ route('candidate.registerStore') }}" enctype="multipart/form-data" novalidate>
                        @csrf
                        <input type="hidden" name="step" value="1">

                        <!-- Email -->
                        <div class="mt-6">
                            <x-input-label for="email" value="Email *" />
                            <x-text-input id="email" class="block mt-1 w-full" type="email" name="email"
                                :value="old('email')" required autocomplete="username" placeholder="<EMAIL>" />
                            <x-input-error :messages="$errors->get('email')" class="mt-2" />
                        </div>

                        <!-- Password -->
                        <div class="mt-6">
                            <x-input-label for="password" value="Mot de passe *" />
                            <x-text-input id="password" class="block mt-1 w-full" type="password" name="password" required
                                autocomplete="new-password" placeholder="Créez un mot de passe sécurisé" />
                            <p class="text-xs text-gray-500 mt-1">Utilisez au moins 8 caractères avec lettres et chiffres</p>
                            <x-input-error :messages="$errors->get('password')" class="mt-2" />
                        </div>

                        <!-- Confirm Password -->
                        <div class="mt-6">
                            <x-input-label for="password_confirmation" value="Confirmer le mot de passe *" />
                            <x-text-input id="password_confirmation" class="block mt-1 w-full" type="password"
                                name="password_confirmation" required autocomplete="new-password" placeholder="Confirmez votre mot de passe" />
                            <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
                        </div>

                        <!-- Profile Picture (optionnel) -->
                        <div class="mt-6 flex flex-col items-center justify-center">
                            <x-input-label for="profile_picture" value="Photo de profil (optionnel)" />
                            <div class="w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-6 mt-2">
                            <x-upload-profile :id="'profile_picture'" />
                                <p class="text-xs text-gray-500 mt-2">Glissez votre photo ici ou <span class="text-primary font-medium cursor-pointer">parcourez</span><br>JPG, PNG jusqu'à 5MB</p>
                            </div>
                            <x-input-error :messages="$errors->get('profile_picture')" class="mt-2" />
                        </div>

                        <div class="flex items-center justify-end gap-4 mt-8">
                            <button type="button" onclick="saveProgress()" class="border border-gray-300 text-gray-700 px-6 py-2 rounded-md text-sm font-medium bg-white hover:bg-gray-100">Sauvegarder</button>
                            <button type="button" onclick="nextStep()" class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-md text-sm font-medium">Suivant</button>
                        </div>
                    </form>
                </div>

                <!-- Step 2: Personal Information -->
                <div id="step-2" class="step hidden">
                    <div class="text-center mb-8">
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">
                            {{-- Dites-nous qui vous êtes --}}
                        </h2>
                        <p class="text-gray-600">
                            {{-- Quelques informations personnelles pour mieux vous connaître --}}
                        </p>
                    </div>

                    <form method="POST" action="{{ route('candidate.registerStore') }}" enctype="multipart/form-data" novalidate>
                        @csrf
                        <input type="hidden" name="step" value="2">

                        <!-- First Name -->
                        <div class="mt-6">
                            <x-input-label for="first_name" value="{!! __('candidate_register.prenom_') !!}" />
                            <x-text-input id="first_name" class="block mt-1 w-full" type="text" name="first_name"
                                :value="old('first_name')" required placeholder="Votre prénom" />
                            <x-input-error :messages="$errors->get('first_name')" class="mt-2" />
                        </div>

                        <!-- Last Name -->
                        <div class="mt-6">
                            <x-input-label for="last_name" value="{!! __('candidate_register.nom_') !!}" />
                            <x-text-input id="last_name" class="block mt-1 w-full" type="text" name="last_name"
                                :value="old('last_name')" required placeholder="Votre nom" />
                            <x-input-error :messages="$errors->get('last_name')" class="mt-2" />
                        </div>

                        <!-- Date of Birth -->
                        <div class="mt-6">
                            <x-input-label for="date_of_birth" value="{!! __('candidate_register.date_de_naissance_') !!}" />
                            <x-text-input id="date_of_birth" class="block mt-1 w-full" type="text" name="date_of_birth"
                                :value="old('date_of_birth')" required placeholder="jj/mm/aaaa" />
                            <x-input-error :messages="$errors->get('date_of_birth')" class="mt-2" />
                        </div>

                        <!-- Category -->
                        <div class="mt-6">
                            <x-input-label for="category" value="Catégorie *" />
                            <div class="grid grid-cols-2 gap-4 mt-2">
                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="category" value="current_profiles" class="mr-3" @checked(old('category') == 'current_profiles')>
                                    <span class="text-sm">Profil courant</span>
                                </label>
                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="category" value="retired" class="mr-3" @checked(old('category') == 'retired')>
                                    <span class="text-sm">Retraité</span>
                                </label>
                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="category" value="migrants" class="mr-3" @checked(old('category') == 'migrants')>
                                    <span class="text-sm">Migrant</span>
                                </label>
                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="category" value="students" class="mr-3" @checked(old('category') == 'students')>
                                    <span class="text-sm">Étudiant</span>
                                </label>
                            </div>
                            <x-input-error :messages="$errors->get('category')" class="mt-2" />
                        </div>

                        <!-- Phone Number -->
                        <div class="mt-6">
                            <x-input-label for="phone" value="{!! __('candidate_register.numxro_de_txlxphone_') !!}" />
                            <x-text-input id="phone" class="block mt-1 w-full" type="tel" name="phone"
                                :value="old('phone')" required placeholder="+41 XX XXX XX XX" />
                            <x-input-error :messages="$errors->get('phone')" class="mt-2" />
                        </div>

                        <div class="flex items-center justify-between mt-8">
                            <button type="button" onclick="previousStep()" 
                                class="text-primary hover:text-primary-dark text-sm font-medium">
                                ← Précédent
                            </button>
                            <div class="flex gap-3">
                                <button type="button" onclick="saveProgress()" 
                                    class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium">
                                    Sauvegarder
                                </button>
                                <button type="button" onclick="nextStep()" 
                                    class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-md text-sm font-medium">
                                    Continuer
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Step 3: Legal & Location -->
                <div id="step-3" class="step hidden">
                    <div class="text-center mb-8">
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">
                            {{-- Où travaillez-vous légalement ? --}}
                        </h2>
                        <p class="text-gray-600">
                            {{-- Informations légales et de localisation --}}
                        </p>
                    </div>

                    <form method="POST" action="{{ route('candidate.registerStore') }}" enctype="multipart/form-data" novalidate>
                        @csrf
                        <input type="hidden" name="step" value="3">

                        <!-- Residence Permit -->
                        <div class="mt-6">
                            <x-input-label for="residence" value="{!! __('candidate_register.suisse_permis_de_sxjour_') !!}" />
                            <x-select-input id="residence" name="residence" class="block mt-1 w-full" required>
                                <option value="suisse" @selected(old('residence') == 'suisse')>{!! __('candidate_register.suisse') !!}</option>
                                @foreach ($residencePermits as $item)
                                    <option value="{{ $item->id }}" @selected(old('residence') == $item->id)>{{ $item->name }}</option>
                                @endforeach
                            </x-select-input>
                            <x-input-error :messages="$errors->get('residence')" class="mt-2" />
                        </div>

                        <!-- Country and Region on same line -->
                        <div class="mt-6 grid grid-cols-2 gap-4">
                            <!-- Country of Residence -->
                            <div>
                                <x-input-label for="country_of_residence" value="{!! __('candidate_register.pays_de_rxsidence_') !!}" />
                                <x-select-input id="country_of_residence" name="country_of_residence" class="block mt-1 w-full" required>
                                    <option value="" disabled selected>{{ __('Sélectionnez un Pays') }}</option>
                                    @foreach ($countries as $item)
                                        <option value="{{ $item->id }}" @if (old('country_of_residence') == $item->id) selected @endif
                                            id-country="{{ $item->id }}">
                                            {{ $item->name }}
                                        </option>
                                    @endforeach
                                </x-select-input>
                                <x-input-error :messages="$errors->get('country_of_residence')" class="mt-2" />
                            </div>

                            <!-- Region/Canton -->
                            <div>
                                <x-input-label for="commune" value="Région/Canton de domicile *" />
                                <x-select-input id="commune" name="commune" class="block mt-1 w-full" required>
                                    <option value="" disabled selected>{{ __('Sélectionner Région ou Canton') }}</option>
                                </x-select-input>
                                <x-input-error :messages="$errors->get('commune')" class="mt-2" />
                            </div>
                        </div>

                        <!-- Address -->
                        <div class="mt-6">
                            <x-input-label for="address" value="Adresse de domicile (Rue,NPA,Ville) *" />
                            <x-text-input-address-completion id="address" name="address" class="block mt-1 w-full"
                                type="text" :value="old('address')" :disabled="old('commune') ? true : false" :latitude_longitude="old('latitude_longitude')" required />
                            <x-input-error :messages="$errors->get('address')" class="mt-2" />
                            <x-input-error :messages="$errors->get('latitude_longitude')" class="mt-2" />
                        </div>

                        <!-- Criminal Record -->
                        <div class="mt-6">
                            <x-input-label for="criminal_record" value="Casier judiciaire vierge (facultatif)" />
                            <x-select-input id="criminal_record" name="criminal_record" class="block mt-1 w-full">
                                <option value="yes" @selected(old('criminal_record') == 'yes')>Oui</option>
                                <option value="no" @selected(old('criminal_record') == 'no')>Non</option>
                                <option value="skip" @selected(old('criminal_record') == 'skip')>{!! __('candidate_register.je_passe_cette_xtape') !!}</option>
                            </x-select-input>
                            <x-input-error :messages="$errors->get('criminal_record')" class="mt-2" />
                        </div>

                        <div class="flex items-center justify-between mt-8">
                            <button type="button" onclick="previousStep()" 
                                class="text-primary hover:text-primary-dark text-sm font-medium">
                                ← Précédent
                            </button>
                            <button type="button" onclick="nextStep()" 
                                class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-md text-sm font-medium">
                                Suivant
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Step 4: Transportation -->
                <div id="step-4" class="step hidden">
                    <div class="text-center mb-8">
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">
                            {{-- Comment vous déplacez-vous ? --}}
                        </h2>
                        <p class="text-gray-600">
                            {{-- Vos moyens de transport pour le travail --}}
                        </p>
                    </div>

                    <form method="POST" action="{{ route('candidate.registerStore') }}" enctype="multipart/form-data" novalidate>
                        @csrf
                        <input type="hidden" name="step" value="4">

                        <!-- Vehicle Holder -->
                        <div class="mt-6">
                            <x-input-label for="vehicle" value="{!! __('candidate_register.titulaire_d_un_vxhicule') !!}" />
                            <x-select-input id="vehicle" name="vehicle" class="your-select-class block mt-1 w-full">
                                <option value="yes" @selected(old('vehicle') == 'yes')>Oui</option>
                                <option value="no" @selected(old('vehicle') == 'no')>Non</option>
                            </x-select-input>
                            <x-input-error :messages="$errors->get('vehicle')" class="mt-2" />
                        </div>

                        <!-- Driver's License -->
                        <div class="mt-6">
                            <x-input-label for="permits" value="{!! __('candidate_register.permis_de_conduire_') !!}" />
                            <x-select-input id="permits" name="permits[]" class="block mt-1 w-full" multiple>
                                @foreach ($permits as $item)
                                    <option value="{{ $item->id }}" @selected(in_array($item->id, old('permits', [])))>{{ $item->name }}</option>
                                @endforeach
                            </x-select-input>
                            <x-input-error :messages="$errors->get('permits.*')" class="mt-2" />
                        </div>

                        <div class="flex items-center justify-between mt-8">
                            <button type="button" onclick="previousStep()" 
                                class="text-primary hover:text-primary-dark text-sm font-medium">
                                ← Précédent
                            </button>
                            <button type="button" onclick="nextStep()" 
                                class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-md text-sm font-medium">
                                Suivant
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Step 5: Professional Background -->
                <div id="step-5" class="step hidden">
                    <div class="text-center mb-8">
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">
                            Domaine recherché
                        </h2>
                        <p class="text-gray-600">
                            {{-- Partagez votre expérience et vos compétences --}}
                        </p>
                    </div>

                    <form method="POST" action="{{ route('candidate.registerStore') }}" enctype="multipart/form-data" novalidate>
                        @csrf
                        <input type="hidden" name="step" value="5">

                        <!-- Dynamic Activity Fields and Professions -->
                        <div id="activity-profession-container">
                            <div class="activity-profession-row mt-6 grid grid-cols-3 gap-4">
                                <!-- Activity Fields -->
                                <div>
                                    <x-input-label for="activity_fields_1" value="Domaine(s) d'activité *" />
                                    <x-select-input id="activity_fields_1" name="activity_fields[]" class="block mt-1 w-full" multiple>
                                        @foreach ($fieldActivities as $item)
                                            <option value="{{ $item->id }}" id-activity="{{ $item->id }}">{{ $item->name }}</option>
                                        @endforeach
                                    </x-select-input>
                                    <x-input-error :messages="$errors->get('activity_fields.*')" class="mt-2" />
                                </div>

                                <!-- Professions -->
                                <div>
                                    <x-input-label for="professions_list_1" value="Profession(s) recherchée(s) *" />
                                    <select id="professions_list_1" name="professions_list[]" class="block mt-1 w-full" multiple>
                                        <option value="infirmier">Infirmier</option>
                                        <option value="developpeur">Développeur</option>
                                        <option value="comptable">Comptable</option>
                                        <option value="enseignant">Enseignant</option>
                                        <option value="technicien">Technicien</option>
                                        <option value="autre">Autre</option>
                                    </select>
                                    <x-input-error :messages="$errors->get('professions_list')" class="mt-2" />
                                </div>

                                <!-- Formation -->
                                <div>
                                    <x-input-label for="formations_1" value="Formations *" />
                                    <select id="formations_1" name="formations[]" class="block mt-1 w-full" multiple>
                                        @foreach ($formations as $item)
                                            <option value="{{ $item->id }}">{{ $item->name }}</option>
                                        @endforeach
                                    </select>
                                    <x-input-error :messages="$errors->get('formations.*')" class="mt-2" />
                                </div>
                            </div>
                        </div>

                        <!-- Add Row Button -->
                        <div class="mt-4 flex gap-2">
                            <button type="button" onclick="addActivityProfessionRow()" 
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                                + Ajouter une ligne
                            </button>
                            <button type="button" onclick="removeLastRow()" 
                                class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                                - Supprimer la dernière ligne
                            </button>
                        </div>

                        <!-- Job Type -->
                        <div class="mt-6">
                            <x-input-label for="job_types" value="{!! __('candidate_register.type_de_poste_recherchx_') !!}" />
                            <x-select-input id="job_types" name="job_types[]" class="block mt-1 w-full" multiple>
                                @foreach ($typeProfessions as $item)
                                    <option value="{{ $item->id }}" @selected(in_array($item->id, old('job_types', [])))>{{ $item->name }}</option>
                                @endforeach
                            </x-select-input>
                            <x-input-error :messages="$errors->get('job_types.*')" class="mt-2" />
                        </div>

                        <!-- Contract Type -->
                        <div class="mt-6">
                            <x-input-label for="contract_type" value="{!! __('candidate_register.je_recherche_contrat_de_travail_') !!}" />
                            <x-select-input id="contract_type" name="contract_type[]" class="block mt-1 w-full" multiple>
                                <option value="call" @selected(in_array('call', old('contract_type', [])))>Travail sur appel</option>
                                <option value="cdi" @selected(in_array('cdi', old('contract_type', [])))>Contrat à durée indéterminée/fixe (CDI)</option>
                                <option value="cdd" @selected(in_array('cdd', old('contract_type', [])))>Contrat à durée déterminée (CDD)</option>
                            </x-select-input>
                            <x-input-error :messages="$errors->get('contract_type.*')" class="mt-2" />
                        </div>

                        <div class="flex items-center justify-between mt-8">
                            <button type="button" onclick="previousStep()" 
                                class="text-primary hover:text-primary-dark text-sm font-medium">
                                ← Précédent
                            </button>
                            <button type="button" onclick="nextStep()" 
                                class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-md text-sm font-medium">
                                Suivant
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Step 6: Availability -->
                <div id="step-6" class="step hidden">
                    <div class="text-center mb-8">
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">
                            {{-- Votre disponibilité --}}
                        </h2>
                        <p class="text-gray-600">
                            {{-- Quand pouvez-vous travailler ? --}}
                        </p>
                    </div>

                    <form method="POST" action="{{ route('candidate.registerStore') }}" enctype="multipart/form-data" novalidate>
                        @csrf
                        <input type="hidden" name="step" value="6">

                        <!-- Availability -->
                        <div class="mt-6">
                            <x-input-label for="availability" value="{!! __('candidate_register.disponibilitx_') !!}" />
                            <x-select-input id="availability" name="availability" class="block mt-1 w-full" required>
                                @foreach ($responsibilities as $item)
                                    <option value="{{ $item->id }}" @selected(old('availability') == $item->id)>{{ $item->name }}</option>
                                @endforeach
                            </x-select-input>
                            <x-input-error :messages="$errors->get('availability')" class="mt-2" />
                        </div>

                        <!-- Work Rate -->
                        <div class="mt-6">
                            <x-input-label for="work_rate" value="{!! __('candidate_register.taux_d_activitx_') !!}" />
                            <x-select-input id="work_rate" name="work_rate" class="block mt-1 w-full" required>
                                @foreach (['100', '90-100', '80-100', '90', '80-90', '70-90', '80', '70-80', '60-80', '70', '60-70', '50-70', '60', '50-60', '40-60', '50', '40-50', '30-50', '40', '30-40', '20-40', '20-30', '30', '10-30', '20', '10-20', '10'] as $rate)
                                    <option value="{{ $rate }}" @selected(old('work_rate') == $rate)>{{ $rate }}%</option>
                                @endforeach
                            </x-select-input>
                            <x-input-error :messages="$errors->get('work_rate')" class="mt-2" />
                        </div>

                        <!-- Languages -->
                        <div class="mt-6">
                            <x-input-label for="native_language" value="{!! __('candidate_register.langue_s_maternelle') !!}" />
                            <x-select-input id="native_language" name="native_language[]" class="block mt-1 w-full" multiple>
                                @foreach ($languages as $item)
                                    <option value="{{ $item->id }}" @selected(in_array($item->id, old('native_language', [])))>{{ $item->name }}</option>
                                @endforeach
                            </x-select-input>
                            <x-input-error :messages="$errors->get('native_language.*')" class="mt-2" />
                        </div>

                        <div class="mt-6">
                            <x-input-label for="fluent_languages" value="{!! __('candidate_register.langue_s_parlxe_s_couramment') !!}" />
                            <x-select-input id="fluent_languages" name="fluent_languages[]" class="block mt-1 w-full" multiple>
                                @foreach ($languages as $item)
                                    <option value="{{ $item->id }}" @selected(in_array($item->id, old('fluent_languages', [])))>{{ $item->name }}</option>
                                @endforeach
                            </x-select-input>
                            <x-input-error :messages="$errors->get('fluent_languages.*')" class="mt-2" />
                        </div>

                        <div class="mt-6">
                            <x-input-label for="intermediate_languages" value="{!! __('candidate_register.langue_s_parlxe_s_avec_notion_intermxdiaire') !!}" />
                            <x-select-input id="intermediate_languages" name="intermediate_languages[]" class="block mt-1 w-full" multiple>
                                @foreach ($languages as $item)
                                    <option value="{{ $item->id }}" @selected(in_array($item->id, old('intermediate_languages', [])))>{{ $item->name }}</option>
                                @endforeach
                            </x-select-input>
                            <x-input-error :messages="$errors->get('intermediate_languages.*')" class="mt-2" />
                        </div>

                        <div class="mt-6">
                            <x-input-label for="basic_languages" value="{!! __('candidate_register.langue_s_parlxe_s_avec_notion_de_base') !!}" />
                            <x-select-input id="basic_languages" name="basic_languages[]" class="block mt-1 w-full" multiple>
                                @foreach ($languages as $item)
                                    <option value="{{ $item->id }}" @selected(in_array($item->id, old('basic_languages', [])))>{{ $item->name }}</option>
                                @endforeach
                            </x-select-input>
                            <x-input-error :messages="$errors->get('basic_languages.*')" class="mt-2" />
                        </div>

                        <div class="flex items-center justify-between mt-8">
                            <button type="button" onclick="previousStep()" 
                                class="text-primary hover:text-primary-dark text-sm font-medium">
                                ← Précédent
                            </button>
                            <button type="button" onclick="nextStep()" 
                                class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-md text-sm font-medium">
                                Suivant
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Step 7: Finalization -->
                <div id="step-7" class="step hidden">
                    <div class="text-center mb-8">
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">
                        Professions Exercées
                        </h2>
                        <p class="text-gray-600">
                            {{-- Dernière étape avant de rejoindre notre communauté --}}
                        </p>
                    </div>

                    <form method="POST" action="{{ route('candidate.registerStore') }}" enctype="multipart/form-data" novalidate>
                        @csrf
                        <input type="hidden" name="step" value="7">

                        <!-- Dynamic Work Experience and Formation -->
                        <div id="work-experience-container">
                            <div class="work-experience-row mt-6 grid grid-cols-3 gap-4">
                                <!-- Work Experience -->
                                <div>
                                    <x-input-label for="profession_1" value="Profession exercée 1" />
                                    <x-text-input id="profession_1" class="block mt-1 w-full" type="text"
                                        name="profession_1" :value="old('profession_1')" />
                                    <x-input-error :messages="$errors->get('profession_1')" class="mt-2" />
                                </div>

                                <!-- Duration -->
                                <div>
                                    <x-input-label for="duration_1" value="Durée de la profession exercée 1" />
                                    <x-select-input id="duration_1" name="duration_1" class="block mt-1 w-full">
                                        <option value=""></option>
                                        @foreach ([
                                            '1 semaine', '2 semaine', '3 semaine',
                                            'Entre 1 mois et 6 mois', 'Entre 6 mois et 12 mois',
                                            'Entre 2 ans et 5 ans', 'Entre 6 ans et 10 ans', 'Plus de 11 ans'] as $duration)
                                            <option value="{{ $duration }}" @selected(old('duration_1') == $duration)>{{ $duration }}</option>
                                        @endforeach
                                    </x-select-input>
                                    <x-input-error :messages="$errors->get('duration_1')" class="mt-2" />
                                </div>

                                <!-- Formation -->
                                <div>
                                    <x-input-label for="formations_final_1" value="Formations *" />
                                    <x-select-input id="formations_final_1" name="formations_final[]" class="block mt-1 w-full" multiple>
                                        @foreach ($formations as $item)
                                            <option value="{{ $item->id }}">{{ $item->name }}</option>
                                        @endforeach
                                    </x-select-input>
                                    <x-input-error :messages="$errors->get('formations_final.*')" class="mt-2" />
                                </div>
                            </div>
                        </div>

                        <!-- Add Row Button -->
                        <div class="mt-4 flex gap-2">
                            <button type="button" onclick="addWorkExperienceRow()" 
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                                + Ajouter une ligne
                            </button>
                            <button type="button" onclick="removeLastWorkExperienceRow()" 
                                class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                                - Supprimer la dernière ligne
                            </button>
                        </div>

                        <!-- Upload CV -->
                        <div class="mt-6">
                            <x-input-label for="cv" value="CV joint" />
                            <x-upload-file :id="'cv'" />
                            <x-input-error :messages="$errors->get('cv')" class="mt-2" />
                        </div>

                        <!-- Certificates -->
                        <div class="mt-6">
                            <x-input-label for="certificates" value="{{ __('Certificats de travail et/ou d\'études (PDF, DOC, DOCX)') }}" />
                            <x-upload-multi-select-file id="certificates" name="certificates[]" accept=".pdf,.doc,.docx" />
                            <x-input-error :messages="$errors->get('certificates.*')" class="mt-2" />
                        </div>

                        <!-- Recaptcha -->
                        <div class="mt-6">
                            <x-recaptcha />
                            <x-input-error :messages="$errors->get('g-recaptcha-response')" class="mt-2" />
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="mt-6">
                            <div class="flex space-x-2 items-center">
                                <input type="checkbox" name="terms" id="terms" required>
                                <x-input-label for="terms">
                                    {!! __('candidate_register.accepter_les') !!} <x-a for="terms" :href="route('generalConditions')"
                                        target="_bank">{!! __('candidate_register.conditions_gxnxrales_d_utilisation') !!}</x-a>
                                </x-input-label>
                            </div>
                            <p class="text-xs mt-2">
                            <ul class="list-disc">
                                <li>{!! __('candidate_register.dans_le_cadre_de_la_lxgislation_') !!}</li>
                                <li>{!! __('candidate_register.l_utilisation_de_not_rofil_professionnel_') !!}</li>
                                <li>{!! __('candidate_register.seuls_les_employeurs_fins_de_recrutement_') !!}</li>
                                <li><strong>{!! __('candidate_register.afin_de_maintenir_l_me_du_dxlai_lxgal_') !!}</strong></li>
                            </ul>
                            </p>
                            <x-input-error :messages="$errors->get('terms')" class="mt-2" />
                        </div>

                        <div class="flex items-center justify-between mt-8">
                            <button type="button" onclick="previousStep()" 
                                class="text-primary hover:text-primary-dark text-sm font-medium">
                                ← Précédent
                            </button>
                            <button type="submit" 
                                class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-md text-sm font-medium">
                                Finaliser l'inscription
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        window.formationsList = @json($formations->map(function($item) {
            return [
                'id' => $item->id,
                'name' => $item->name,
            ];
        }));

        let currentStep = 1;
        const totalSteps = 7;
        
        // Variable globale pour sauvegarder l'état de l'étape 5
        let step5State = {
            rows: [] // Chaque élément est un objet { activityFields: [], professions: [], formations: [] }
        };

        // Variable globale pour sauvegarder l'état de l'étape 7
        let step7State = {
            rows: [] // Chaque élément est un objet { profession: '', duration: '', formations: [] }
        };

        function updateProgress() {
            const percentage = (currentStep / totalSteps) * 100;
            
            document.getElementById('progress-bar').style.width = percentage + '%';
            document.getElementById('progress-text').textContent = currentStep + '/' + totalSteps;
        }

        function showStep(step) {
            // Si on quitte l'étape 5, sauvegarder et détruire les composants
            if (currentStep === 5) {
                saveStep5State();
                destroyMultiSelectTagsForStep5();
            }
            
            // Si on quitte l'étape 7, sauvegarder et détruire les composants
            if (currentStep === 7) {
                saveStep7State();
                destroyMultiSelectTagsForStep7();
            }
            
            // Cacher toutes les étapes
            document.querySelectorAll('.step').forEach(s => s.classList.add('hidden'));
            // Afficher l'étape courante
            document.getElementById('step-' + step).classList.remove('hidden');
            updateProgress();
            
            // Si on arrive à l'étape 5, restaurer et recréer les composants
            if (step === 5) {
                restoreStep5State();
                initializeMultiSelectTagsForStep5();
            }

            // Si on arrive à l'étape 7, restaurer et recréer les composants
            if (step === 7) {
                restoreStep7State();
                initializeMultiSelectTagsForStep7();
            }
            
            // Réinitialiser les événements pour l'étape 3
            if (step === 3) {
                initializeStep3Events();
            }
        }

        function initializeStep3Events() {
            const countrySelect = document.getElementById('country_of_residence');
            if (countrySelect) {
                // Supprimer l'ancien event listener s'il existe
                countrySelect.removeEventListener('change', handleCountryChange);
                // Ajouter le nouvel event listener
                countrySelect.addEventListener('change', handleCountryChange);
            }
        }

        function handleCountryChange() {
            const selectedOption = this.options[this.selectedIndex];
            const countryId = selectedOption.getAttribute('id-country');

            if (countryId) {
                fetch(`/get-all-region?ids=${countryId}`)
                    .then(response => response.json())
                    .then(data => {
                        const communeSelect = document.getElementById('commune');
                        communeSelect.innerHTML = '';

                        const defaultOption = document.createElement('option');
                        defaultOption.value = '';
                        defaultOption.disabled = true;
                        defaultOption.selected = true;
                        defaultOption.textContent = '{{ __('Sélectionner Région ou Canton') }}';
                        communeSelect.appendChild(defaultOption);

                        data.forEach(region => {
                            const option = document.createElement('option');
                            option.value = region.name;
                            option.textContent = region.name;
                            communeSelect.appendChild(option);
                        });
                    })
                    .catch(error => {
                        console.error('Erreur lors de la récupération des régions:', error);
                    });
            } else {
                // Si aucun pays n'est sélectionné, vider la liste des régions
                const communeSelect = document.getElementById('commune');
                communeSelect.innerHTML = '';
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.disabled = true;
                defaultOption.selected = true;
                defaultOption.textContent = '{{ __('Sélectionner Région ou Canton') }}';
                communeSelect.appendChild(defaultOption);
            }
        }

        function validateStep(step) {
            const currentForm = document.querySelector('#step-' + step + ' form');
            const requiredFields = currentForm.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('border-red-500');
                    isValid = false;
                } else {
                    field.classList.remove('border-red-500');
                }
            });

            return isValid;
        }

        function nextStep() {
            if (validateStep(currentStep)) {
                if (currentStep < totalSteps) {
                    currentStep++;
                    showStep(currentStep);
                }
            } else {
                alert('Veuillez remplir tous les champs obligatoires.');
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
            }
        }

        function saveProgress() {
            const currentForm = document.querySelector('#step-' + currentStep + ' form');
            const formData = new FormData(currentForm);
            
            fetch('{{ route("candidate.registerStore") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Progression sauvegardée !');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
            });
        }

        // Initialize MultiSelectTag components
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize date picker
            if (typeof flatpickr !== 'undefined') {
                flatpickr("#date_of_birth");
            }

            // Initialize MultiSelectTag for permits
            if (typeof MultiSelectTag !== 'undefined') {
                new MultiSelectTag("permits", {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });

                new MultiSelectTag("native_language", {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });

                new MultiSelectTag("fluent_languages", {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });

                new MultiSelectTag("intermediate_languages", {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });

                new MultiSelectTag("basic_languages", {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });

                new MultiSelectTag("formations", {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });

                new MultiSelectTag("job_types", {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });

                new MultiSelectTag("contract_type", {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });

                // Initialize MultiSelectTag for the first activity fields and professions
                initializeActivityProfessionRow(1);

                // Initialize MultiSelectTag for the first work experience row formations
                new MultiSelectTag("formations_final_1", {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });
                
                // Initialiser l'étape 5 si elle est active au chargement
                if (document.getElementById('step-5').classList.contains('active')) {
                    initializeMultiSelectTagsForStep5();
                }
                
                // Initialiser l'étape 7 si elle est active au chargement
                if (document.getElementById('step-7').classList.contains('active')) {
                    initializeMultiSelectTagsForStep7();
                }
            }

            // Country change handler
            document.getElementById('country_of_residence').addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                const countryId = selectedOption.getAttribute('id-country');

                if (countryId) {
                    fetch(`/get-all-region?ids=${countryId}`)
                        .then(response => response.json())
                        .then(data => {
                            const communeSelect = document.getElementById('commune');
                            communeSelect.innerHTML = '';

                            const defaultOption = document.createElement('option');
                            defaultOption.value = '';
                            defaultOption.disabled = true;
                            defaultOption.selected = true;
                            defaultOption.textContent = '{{ __('Sélectionner Région ou Canton') }}';
                            communeSelect.appendChild(defaultOption);

                            data.forEach(region => {
                                const option = document.createElement('option');
                                option.value = region.name;
                                option.textContent = region.name;
                                communeSelect.appendChild(option);
                            });
                        })
                        .catch(error => {
                            console.error('Erreur lors de la récupération des régions:', error);
                        });
                } else {
                    // Si aucun pays n'est sélectionné, vider la liste des régions
                    const communeSelect = document.getElementById('commune');
                    communeSelect.innerHTML = '';
                    const defaultOption = document.createElement('option');
                    defaultOption.value = '';
                    defaultOption.disabled = true;
                    defaultOption.selected = true;
                    defaultOption.textContent = '{{ __('Sélectionner Région ou Canton') }}';
                    communeSelect.appendChild(defaultOption);
                }
            });

            // Fonction pour charger les régions
            function loadRegions(countryId) {
                if (countryId) {
                    fetch(`/get-all-region?ids=${countryId}`)
                        .then(response => response.json())
                        .then(data => {
                            const communeSelect = document.getElementById('commune');
                            communeSelect.innerHTML = '';

                            const defaultOption = document.createElement('option');
                            defaultOption.value = '';
                            defaultOption.disabled = true;
                            defaultOption.selected = true;
                            defaultOption.textContent = '{{ __('Sélectionner Région ou Canton') }}';
                            communeSelect.appendChild(defaultOption);

                            data.forEach(region => {
                                const option = document.createElement('option');
                                option.value = region.name;
                                option.textContent = region.name;
                                communeSelect.appendChild(option);
                            });
                        })
                        .catch(error => {
                            console.error('Erreur lors de la récupération des régions:', error);
                        });
                }
            }

            // Charger les régions au chargement de la page si un pays est déjà sélectionné
            const countrySelect = document.getElementById('country_of_residence');
            if (countrySelect && countrySelect.value) {
                loadRegions(countrySelect.value);
            }

            // Initialize
            updateProgress();
            
            // Initialiser les événements pour l'étape 3
            initializeStep3Events();

            if (typeof MultiSelectTag !== 'undefined') {
                new MultiSelectTag('formations_1', {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });
                // Initialisation spéciale pour Profession(s) recherchée(s) de la première ligne
                new MultiSelectTag('professions_list_1', {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });
            }
        });

        // Function to add new activity-profession row
        function addActivityProfessionRow() {
            const container = document.getElementById('activity-profession-container');
            const rowCount = container.children.length + 1;
            const professionOptions = (window.professionsList || []).map(item =>
                `<option value="${item.id}">${item.name}</option>`
            ).join('');
            const formationOptions = (window.formationsList || []).map(item =>
                `<option value="${item.id}">${item.name}</option>`
            ).join('');
            const fieldActivityOptions = (window.fieldActivitiesList || []).map(item =>
                `<option value="${item.id}" id-activity="${item.id}">${item.name}</option>`
            ).join('');

            // Si window.fieldActivitiesList n'est pas défini, fallback sur Blade
            let fieldActivityHtml = fieldActivityOptions;
            if (!fieldActivityHtml) {
                fieldActivityHtml = `@foreach ($fieldActivities as $item)<option value="{{ $item->id }}" id-activity="{{ $item->id }}">{{ $item->name }}</option>@endforeach`;
            }

            const newRow = document.createElement('div');
            newRow.className = 'activity-profession-row mt-6 grid grid-cols-3 gap-4';
            newRow.innerHTML = `
                <div>
                    <x-input-label for="activity_fields_${rowCount}" value="Domaine(s) d'activité *" />
                    <select id="activity_fields_${rowCount}" name="activity_fields[]" class="block mt-1 w-full" multiple>
                        ${fieldActivityHtml}
                    </select>
                </div>
                <div>
                    <x-input-label for="professions_list_${rowCount}" value="Profession(s) recherchée(s) *" />
                    <select id="professions_list_${rowCount}" name="professions_list[]" class="block mt-1 w-full" multiple>
                        ${professionOptions}
                    </select>
                </div>
                <div>
                    <x-input-label for="formations_${rowCount}" value="Formations *" />
                    <select id="formations_${rowCount}" name="formations[]" class="block mt-1 w-full" multiple>
                        ${formationOptions}
                    </select>
                </div>
            `;
            container.appendChild(newRow);

            // Initialiser MultiSelectTag pour chaque champ de la nouvelle ligne
            setTimeout(() => {
                if (typeof MultiSelectTag !== 'undefined') {
                    new MultiSelectTag(`activity_fields_${rowCount}`, {
                        rounded: true,
                        placeholder: 'Filtre ...',
                        tagColor: {
                            textColor: '#0aaedb',
                            borderColor: '#6ddaf8',
                            bgColor: '#e7f9fe',
                        },
                        onChange: function(values) {
                            handleActivityFieldsChange(rowCount, values);
                        }
                    });
                    new MultiSelectTag(`professions_list_${rowCount}`, {
                        rounded: true,
                        placeholder: 'Filtre ...',
                        tagColor: {
                            textColor: '#0aaedb',
                            borderColor: '#6ddaf8',
                            bgColor: '#e7f9fe',
                        }
                    });
                    new MultiSelectTag(`formations_${rowCount}`, {
                        rounded: true,
                        placeholder: 'Filtre ...',
                        tagColor: {
                            textColor: '#0aaedb',
                            borderColor: '#6ddaf8',
                            bgColor: '#e7f9fe',
                        }
                    });
                }
            }, 100);
        }

        // Function to remove the last row
        function removeLastRow() {
            const container = document.getElementById('activity-profession-container');
            const rows = container.querySelectorAll('.activity-profession-row');
            
            if (rows.length > 1) { // Garder au moins une ligne
                const lastRow = rows[rows.length - 1];
                const rowNumber = rows.length;
                
                // Remove existing MultiSelectTag UI before removing the row
                const activitySelect = lastRow.querySelector(`#activity_fields_${rowNumber}`);
                const professionSelect = lastRow.querySelector(`#professions_list_${rowNumber}`);
                const formationSelect = lastRow.querySelector(`#formations_${rowNumber}`);
                
                if (activitySelect) {
                    const parent = activitySelect.parentElement;
                                const multiSelectDivs = parent.querySelectorAll('.multi-select-tag');
                                multiSelectDivs.forEach(div => div.remove());
                }
                
                if (professionSelect) {
                    const parent = professionSelect.parentElement;
                        const multiSelectDivs = parent.querySelectorAll('.multi-select-tag');
                        multiSelectDivs.forEach(div => div.remove());
                }
                
                if (formationSelect) {
                    const parent = formationSelect.parentElement;
                    const multiSelectDivs = parent.querySelectorAll('.multi-select-tag');
                    multiSelectDivs.forEach(div => div.remove());
                }
                
                lastRow.remove();
            }
        }

        // Fonction pour détruire un MultiSelectTag par son id
        function destroyMultiSelectTag(selectId) {
            const select = document.getElementById(selectId);
            if (select) {
                const parent = select.parentElement;
                const multiSelectDivs = parent.querySelectorAll('.multi-select-tag');
                multiSelectDivs.forEach(div => div.remove());
                select.style.display = 'block';
            }
        }

        // Fonction pour détruire tous les MultiSelectTag de l'étape 5
        function destroyMultiSelectTagsForStep5() {
            const rows = document.querySelectorAll('#activity-profession-container .activity-profession-row');
            rows.forEach((row, index) => {
                const rowNumber = index + 1;
                destroyMultiSelectTag(`activity_fields_${rowNumber}`);
                destroyMultiSelectTag(`professions_list_${rowNumber}`);
                destroyMultiSelectTag(`formations_${rowNumber}`);
            });
            
            // Détruire aussi job_types et contract_type
            destroyMultiSelectTag('job_types');
            destroyMultiSelectTag('contract_type');
        }

        // Fonction pour détruire tous les MultiSelectTag de l'étape 7
        function destroyMultiSelectTagsForStep7() {
            const rows = document.querySelectorAll('#work-experience-container .work-experience-row');
            rows.forEach((row, index) => {
                const rowNumber = index + 1;
                destroyMultiSelectTag(`formations_final_${rowNumber}`);
            });
        }

        // Fonction pour sauvegarder l'état de l'étape 5
        function saveStep5State() {
            step5State.rows = [];
            const rows = document.querySelectorAll('#activity-profession-container .activity-profession-row');
            rows.forEach((row, index) => {
                const rowNumber = index + 1;
                const activityFields = document.getElementById(`activity_fields_${rowNumber}`);
                const professionsList = document.getElementById(`professions_list_${rowNumber}`);
                const formations = document.getElementById(`formations_${rowNumber}`);
                
                // Récupérer les valeurs sélectionnées pour chaque champ
                const activityFieldsValues = Array.from(activityFields.selectedOptions).map(opt => opt.value);
                const professionsListValues = Array.from(professionsList.selectedOptions).map(opt => opt.value);
                const formationsValues = Array.from(formations.selectedOptions).map(opt => opt.value);
                
                step5State.rows.push({
                    activityFields: activityFieldsValues,
                    professions: professionsListValues,
                    formations: formationsValues
                });
            });
            
            // Sauvegarder job_types et contract_type
            const jobTypes = document.getElementById('job_types');
            const contractType = document.getElementById('contract_type');
            step5State.jobTypes = Array.from(jobTypes.selectedOptions).map(opt => opt.value);
            step5State.contractType = Array.from(contractType.selectedOptions).map(opt => opt.value);
        }

        // Fonction pour sauvegarder l'état de l'étape 7
        function saveStep7State() {
            step7State.rows = [];
            const rows = document.querySelectorAll('#work-experience-container .work-experience-row');
            rows.forEach((row, index) => {
                const rowNumber = index + 1;
                const profession = document.getElementById(`profession_${rowNumber}`);
                const duration = document.getElementById(`duration_${rowNumber}`);
                const formations = document.getElementById(`formations_final_${rowNumber}`);
                
                // Récupérer les valeurs pour chaque champ
                const professionValue = profession ? profession.value : '';
                const durationValue = duration ? duration.value : '';
                const formationsValues = Array.from(formations.selectedOptions).map(opt => opt.value);
                
                step7State.rows.push({
                    profession: professionValue,
                    duration: durationValue,
                    formations: formationsValues
                });
            });
        }

        // Fonction pour restaurer l'état de l'étape 5
        function restoreStep5State() {
            const container = document.getElementById('activity-profession-container');
            
            // Vider le conteneur sauf la première ligne
            while (container.children.length > 1) {
                container.removeChild(container.lastChild);
            }
            
            // S'il y a plus d'une ligne dans step5State, les ajouter
            for (let i = 1; i < step5State.rows.length; i++) {
                addActivityProfessionRow();
            }
            
            // Maintenant, pour chaque ligne, assigner les valeurs sauvegardées
            step5State.rows.forEach((row, index) => {
                const rowNumber = index + 1;
                const activityFields = document.getElementById(`activity_fields_${rowNumber}`);
                const professionsList = document.getElementById(`professions_list_${rowNumber}`);
                const formations = document.getElementById(`formations_${rowNumber}`);
                
                // Restaurer les valeurs pour activity_fields
                setSelectedOptions(activityFields, row.activityFields);
                // Restaurer les valeurs pour formations
                setSelectedOptions(formations, row.formations);
            });
            
            // Restaurer job_types et contract_type
            const jobTypes = document.getElementById('job_types');
            const contractType = document.getElementById('contract_type');
            setSelectedOptions(jobTypes, step5State.jobTypes || []);
            setSelectedOptions(contractType, step5State.contractType || []);
        }

        // Fonction pour restaurer l'état de l'étape 7
        function restoreStep7State() {
            const container = document.getElementById('work-experience-container');
            
            // Vider le conteneur sauf la première ligne
            while (container.children.length > 1) {
                container.removeChild(container.lastChild);
            }
            
            // S'il y a plus d'une ligne dans step7State, les ajouter
            for (let i = 1; i < step7State.rows.length; i++) {
                addWorkExperienceRow();
            }
            
            // Maintenant, pour chaque ligne, assigner les valeurs sauvegardées
            step7State.rows.forEach((row, index) => {
                const rowNumber = index + 1;
                const profession = document.getElementById(`profession_${rowNumber}`);
                const duration = document.getElementById(`duration_${rowNumber}`);
                const formations = document.getElementById(`formations_final_${rowNumber}`);
                
                // Restaurer les valeurs
                if (profession) profession.value = row.profession;
                if (duration) duration.value = row.duration;
                setSelectedOptions(formations, row.formations);
            });
        }

        // Fonction pour assigner des options sélectionnées
        function setSelectedOptions(selectElement, values) {
            Array.from(selectElement.options).forEach(option => {
                option.selected = values.includes(option.value);
            });
        }

        // Fonction pour initialiser tous les MultiSelectTag de l'étape 5
        function initializeMultiSelectTagsForStep5() {
            step5State.rows.forEach((row, index) => {
                const rowNumber = index + 1;
                
                // Initialiser le MultiSelectTag pour activity_fields
                new MultiSelectTag(`activity_fields_${rowNumber}`, {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    },
                    onChange: function(values) {
                        handleActivityFieldsChange(rowNumber, values);
                    }
                });
                
                // Charger les professions pour cette ligne en fonction des activity_fields sauvegardés
                handleActivityFieldsChange(rowNumber, row.activityFields, function() {
                    // Une fois que les professions sont chargées, initialiser le MultiSelectTag pour professions_list
                    new MultiSelectTag(`professions_list_${rowNumber}`, {
                        rounded: true,
                        placeholder: 'Filtre ...',
                        tagColor: {
                            textColor: '#0aaedb',
                            borderColor: '#6ddaf8',
                            bgColor: '#e7f9fe',
                        }
                    });
                    
                    // Assigner les valeurs sauvegardées pour professions_list
                    const professionsList = document.getElementById(`professions_list_${rowNumber}`);
                    setSelectedOptions(professionsList, row.professions);
                    
                    // Initialiser le MultiSelectTag pour formations
                    new MultiSelectTag(`formations_${rowNumber}`, {
                        rounded: true,
                        placeholder: 'Filtre ...',
                        tagColor: {
                            textColor: '#0aaedb',
                            borderColor: '#6ddaf8',
                            bgColor: '#e7f9fe',
                        }
                    });
                });
            });
            
            // Initialiser job_types et contract_type
            new MultiSelectTag("job_types", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                }
            });

            new MultiSelectTag("contract_type", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                }
            });
        }

        // Fonction pour initialiser tous les MultiSelectTag de l'étape 7
        function initializeMultiSelectTagsForStep7() {
            step7State.rows.forEach((row, index) => {
                const rowNumber = index + 1;
                
                // Initialiser le MultiSelectTag pour formations
                new MultiSelectTag(`formations_final_${rowNumber}`, {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });
            });
        }

        // Fonction pour gérer le changement de domaines d'activité dans une ligne
        function handleActivityFieldsChange(rowNumber, selectedValues) {
            const activityIds = selectedValues.map(value => parseInt(value));
            if (activityIds.length > 0) {
                fetch(`/get-all-profession?ids=${activityIds.join(',')}`)
                    .then(response => response.json())
                    .then(data => {
                        const select = document.getElementById(`professions_list_${rowNumber}`);
                        select.innerHTML = '';
                        data.forEach(item => {
                            const option = document.createElement("option");
                            option.value = item.id;
                            option.textContent = item.name + (item.field_activity_id === null ? ' (hors domaine)' : '');
                            if (item.field_activity_id === null) {
                                option.style.backgroundColor = '#ffe4b2';
                                option.style.color = '#b45309';
                                option.style.fontStyle = 'italic';
                            }
                            select.appendChild(option);
                        });
                        // Réinitialiser le MultiSelectTag
                        destroyMultiSelectTag(`professions_list_${rowNumber}`);
                        new MultiSelectTag(`professions_list_${rowNumber}`, {
                            rounded: true,
                            placeholder: 'Filtre ...',
                            tagColor: {
                                textColor: '#0aaedb',
                                borderColor: '#6ddaf8',
                                bgColor: '#e7f9fe',
                            }
                        });
                    })
                    .catch(error => console.error('Erreur:', error));
            } else {
                // Vider la liste si aucun domaine sélectionné
                const select = document.getElementById(`professions_list_${rowNumber}`);
                select.innerHTML = '';
                destroyMultiSelectTag(`professions_list_${rowNumber}`);
                new MultiSelectTag(`professions_list_${rowNumber}`, {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });
            }
        }

        // Fonction pour initialiser MultiSelectTag pour une ligne dynamique
        function initializeActivityProfessionRow(rowNumber) {
            if (typeof MultiSelectTag !== 'undefined') {
                // Domaines d'activité
                new MultiSelectTag(`activity_fields_${rowNumber}`, {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    },
                    onChange: function(values) {
                        handleActivityFieldsChange(rowNumber, values);
                    }
                });
                // Professions
                new MultiSelectTag(`professions_list_${rowNumber}`, {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });
                // Formations
                new MultiSelectTag(`formations_${rowNumber}`, {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });
                // Charger les professions initiales si besoin
                const activitySelect = document.getElementById(`activity_fields_${rowNumber}`);
                const selectedValues = Array.from(activitySelect.selectedOptions).map(opt => opt.value);
                if (selectedValues.length > 0) {
                    handleActivityFieldsChange(rowNumber, selectedValues);
                }
            }
        }

        // Function to add new work experience row
        function addWorkExperienceRow() {
            const container = document.getElementById('work-experience-container');
            const rowCount = container.children.length + 1;
            
            const newRow = document.createElement('div');
            newRow.className = 'work-experience-row mt-6 grid grid-cols-3 gap-4';
            newRow.innerHTML = `
                <div>
                    <x-input-label for="profession_${rowCount}" value="Profession exercée ${rowCount}" />
                    <x-text-input id="profession_${rowCount}" class="block mt-1 w-full" type="text"
                        name="profession_${rowCount}" />
                    <x-input-error :messages="$errors->get('profession_${rowCount}')" class="mt-2" />
                </div>
                <div>
                    <x-input-label for="duration_${rowCount}" value="Durée de la profession exercée ${rowCount}" />
                    <x-select-input id="duration_${rowCount}" name="duration_${rowCount}" class="block mt-1 w-full">
                        <option value=""></option>
                        @foreach ([
                            '1 semaine', '2 semaine', '3 semaine',
                            'Entre 1 mois et 6 mois', 'Entre 6 mois et 12 mois',
                            'Entre 2 ans et 5 ans', 'Entre 6 ans et 10 ans', 'Plus de 11 ans'] as $duration)
                            <option value="{{ $duration }}" @selected(old('duration_${rowCount}') == $duration)>{{ $duration }}</option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('duration_${rowCount}')" class="mt-2" />
                </div>
                <div>
                    <x-input-label for="formations_final_${rowCount}" value="Formations *" />
                    <x-select-input id="formations_final_${rowCount}" name="formations_final[]" class="block mt-1 w-full" multiple>
                        @foreach ($formations as $item)
                            <option value="{{ $item->id }}">{{ $item->name }}</option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('formations_final.*')" class="mt-2" />
                </div>
            `;
            
            container.appendChild(newRow);
            
            // Initialiser les MultiSelectTag pour la nouvelle ligne avec un délai pour s'assurer que le DOM est prêt
            setTimeout(() => {
                // Re-initialize MultiSelectTag for the new row's formations
                new MultiSelectTag(`formations_final_${rowCount}`, {
                    rounded: true,
                    placeholder: 'Filtre ...',
                    tagColor: {
                        textColor: '#0aaedb',
                        borderColor: '#6ddaf8',
                        bgColor: '#e7f9fe',
                    }
                });
                // Re-initialize MultiSelectTag for the new row's professions
                if (typeof MultiSelectTag !== 'undefined') {
                    new MultiSelectTag(`professions_list_${rowCount}`, {
                        rounded: true,
                        placeholder: 'Filtre ...',
                        tagColor: {
                            textColor: '#0aaedb',
                            borderColor: '#6ddaf8',
                            bgColor: '#e7f9fe',
                        }
                    });
                }
            }, 100);
        }

        // Function to remove the last work experience row
        function removeLastWorkExperienceRow() {
            const container = document.getElementById('work-experience-container');
            const rows = container.querySelectorAll('.work-experience-row');
            
            if (rows.length > 1) { // Garder au moins une ligne
                const lastRow = rows[rows.length - 1];
                const rowNumber = rows.length;
                
                // Remove existing MultiSelectTag UI before removing the row
                const formationSelect = lastRow.querySelector(`#formations_final_${rowNumber}`);
                
                if (formationSelect) {
                    const parent = formationSelect.parentElement;
                    const multiSelectDivs = parent.querySelectorAll('.multi-select-tag');
                    multiSelectDivs.forEach(div => div.remove());
                }
                
                lastRow.remove();
            }
        }

        // Détruit tous les MultiSelectTag de l'étape 5
        function destroyStep5MultiSelects() {
            const multiSelects = document.querySelectorAll('#step-5 .multi-select-tag');
            multiSelects.forEach(el => el.remove());
            const selects = document.querySelectorAll('#step-5 select');
            selects.forEach(select => {
                select.style.display = 'block';
            });
        }

        // Initialise tous les MultiSelectTag de l'étape 5
        function initializeStep5MultiSelects() {
            if (typeof MultiSelectTag === 'undefined') return;
            destroyStep5MultiSelects();
            const container = document.getElementById('activity-profession-container');
            const rows = container.querySelectorAll('.activity-profession-row');
            for (let i = 0; i < rows.length; i++) {
                const rowNumber = i + 1;
                initializeActivityProfessionRow(rowNumber);
            }
            new MultiSelectTag("job_types", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                }
            });
            new MultiSelectTag("contract_type", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                }
            });
        }
    </script>
</x-guest-layout>
