<x-guest-layout>
    <x-slot name="title">Inscription Candidat - Étape 5/7</x-slot>

    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">
                    Votre parcours professionnel
                </h2>
                <p class="text-gray-600">
                    Partagez votre expérience et vos compétences
                </p>
            </div>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-lg">
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <!-- Progress Bar -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">Étape 5 sur 7</span>
                        <span class="text-sm text-gray-500">71%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full" style="width: 71%"></div>
                    </div>
                </div>

                <form class="space-y-6" action="{{ route('candidate.registerStore') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="step" value="5">
                    
                    <!-- Message de succès -->
                    @if(session('success'))
                        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-4">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif
                    
                    <!-- Données des étapes précédentes -->
                    @foreach(['email', 'password', 'password_confirmation', 'profile_picture', 'first_name', 'last_name', 'date_of_birth', 'category', 'phone', 'residence', 'name', 'country_of_residence', 'commune', 'criminal_record', 'vehicle', 'permits'] as $field)
                        @if(session("registration_data.$field"))
                            <input type="hidden" name="{{ $field }}" value="{{ session("registration_data.$field") }}">
                        @endif
                    @endforeach

                    <!-- Expériences professionnelles -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Expériences professionnelles
                        </label>
                        <div class="space-y-4">
                            <div class="border border-gray-300 rounded-md p-4">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Expérience 1</h4>
                                <div class="grid grid-cols-1 gap-4">
                                    <div>
                                        <label for="profession_1" class="block text-sm font-medium text-gray-700">Profession exercée 1</label>
                                        <input type="text" name="profession_1" id="profession_1" 
                                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                                               value="{{ old('profession_1') }}">
                                    </div>
                                    <div>
                                        <label for="duration_1" class="block text-sm font-medium text-gray-700">Durée de la profession exercée 1</label>
                                        <input type="text" name="duration_1" id="duration_1" 
                                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                                               value="{{ old('duration_1') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Domaine d'activité -->
                    <div>
                        <label for="activity_fields" class="block text-sm font-medium text-gray-700">
                            Domaine d'activité *
                        </label>
                        <div class="mt-1">
                            <select id="activity_fields" name="activity_fields[]" multiple required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('activity_fields') border-red-500 @enderror">
                                @foreach($fieldActivities as $field)
                                    <option value="{{ $field->id }}" {{ in_array($field->id, old('activity_fields', [])) ? 'selected' : '' }}>
                                        {{ $field->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @error('activity_fields')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Profession -->
                    <div>
                        <label for="professions_list" class="block text-sm font-medium text-gray-700">
                            Profession *
                        </label>
                        <div class="mt-1">
                            <select id="professions_list" name="professions_list[]" multiple required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('professions_list') border-red-500 @enderror">
                                @foreach($professions as $profession)
                                    <option value="{{ $profession->id }}" {{ in_array($profession->id, old('professions_list', [])) ? 'selected' : '' }}>
                                        {{ $profession->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @error('professions_list')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Formation -->
                    <div>
                        <label for="formations" class="block text-sm font-medium text-gray-700">
                            Formation *
                        </label>
                        <div class="mt-1">
                            <select id="formations" name="formations[]" multiple required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('formations') border-red-500 @enderror">
                                @foreach($formations as $formation)
                                    <option value="{{ $formation->id }}" {{ in_array($formation->id, old('formations', [])) ? 'selected' : '' }}>
                                        {{ $formation->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @error('formations')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Type de postes recherchés -->
                    <div>
                        <label for="job_types" class="block text-sm font-medium text-gray-700">
                            Type de(s) poste(s) recherché(s) *
                        </label>
                        <div class="mt-1">
                            <select id="job_types" name="job_types[]" multiple required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('job_types') border-red-500 @enderror">
                                @foreach($typeProfessions as $type)
                                    <option value="{{ $type->id }}" {{ in_array($type->id, old('job_types', [])) ? 'selected' : '' }}>
                                        {{ $type->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @error('job_types')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Contrat recherché -->
                    <div>
                        <label for="contract_type" class="block text-sm font-medium text-gray-700">
                            Je recherche (contrat de travail) *
                        </label>
                        <div class="mt-1 space-y-2">
                            <div class="flex items-center">
                                <input id="contract_call" name="contract_type[]" type="checkbox" value="call"
                                    class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded @error('contract_type') border-red-500 @enderror"
                                    {{ in_array('call', old('contract_type', [])) ? 'checked' : '' }}>
                                <label for="contract_call" class="ml-2 block text-sm text-gray-900">Appel</label>
                            </div>
                            <div class="flex items-center">
                                <input id="contract_cdi" name="contract_type[]" type="checkbox" value="cdi"
                                    class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded @error('contract_type') border-red-500 @enderror"
                                    {{ in_array('cdi', old('contract_type', [])) ? 'checked' : '' }}>
                                <label for="contract_cdi" class="ml-2 block text-sm text-gray-900">CDI</label>
                            </div>
                            <div class="flex items-center">
                                <input id="contract_cdd" name="contract_type[]" type="checkbox" value="cdd"
                                    class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded @error('contract_type') border-red-500 @enderror"
                                    {{ in_array('cdd', old('contract_type', [])) ? 'checked' : '' }}>
                                <label for="contract_cdd" class="ml-2 block text-sm text-gray-900">CDD</label>
                            </div>
                        </div>
                        @error('contract_type')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="flex items-center justify-between">
                        <a href="{{ route('candidate.registerStep4') }}" 
                           class="text-primary hover:text-primary-dark text-sm font-medium">
                            ← Précédent
                        </a>
                        <button type="submit"
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            Suivant
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-guest-layout> 