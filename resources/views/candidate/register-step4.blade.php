<x-guest-layout>
    <x-slot name="title">Inscription Candidat - Étape 4/7</x-slot>

    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">
                    Comment vous déplacez-vous ?
                </h2>
                <p class="text-gray-600">
                    Vos moyens de transport pour le travail
                </p>
            </div>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <!-- Progress Bar -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">Étape 4 sur 7</span>
                        <span class="text-sm text-gray-500">57%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full" style="width: 57%"></div>
                    </div>
                </div>

                <form class="space-y-6" action="{{ route('candidate.registerStore') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="step" value="4">
                    
                    <!-- Message de succès -->
                    @if(session('success'))
                        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-4">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif
                    
                    <!-- Données des étapes précédentes -->
                    @foreach(['email', 'password', 'password_confirmation', 'profile_picture', 'first_name', 'last_name', 'date_of_birth', 'category', 'phone', 'residence', 'name', 'country_of_residence', 'commune', 'criminal_record'] as $field)
                        @if(session("registration_data.$field"))
                            <input type="hidden" name="{{ $field }}" value="{{ session("registration_data.$field") }}">
                        @endif
                    @endforeach

                    <!-- Véhicule -->
                    <div>
                        <label for="vehicle" class="block text-sm font-medium text-gray-700">
                            Possédez-vous un véhicule ? *
                        </label>
                        <div class="mt-1">
                            <select id="vehicle" name="vehicle" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('vehicle') border-red-500 @enderror">
                                <option value="">Sélectionnez une option</option>
                                <option value="yes" {{ old('vehicle') == 'yes' ? 'selected' : '' }}>Oui</option>
                                <option value="no" {{ old('vehicle') == 'no' ? 'selected' : '' }}>Non</option>
                            </select>
                        </div>
                        @error('vehicle')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Permis -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Permis de conduire
                        </label>
                        <div class="space-y-2">
                            @foreach($permits as $permit)
                                <div class="flex items-center">
                                    <input id="permit_{{ $permit->id }}" name="permits[]" type="checkbox" value="{{ $permit->id }}"
                                        class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded @error('permits') border-red-500 @enderror"
                                        {{ in_array($permit->id, old('permits', [])) ? 'checked' : '' }}>
                                    <label for="permit_{{ $permit->id }}" class="ml-2 block text-sm text-gray-900">
                                        {{ $permit->name }}
                                    </label>
                                </div>
                            @endforeach
                        </div>
                        @error('permits')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="flex items-center justify-between">
                        <a href="{{ route('candidate.registerStep3') }}" 
                           class="text-primary hover:text-primary-dark text-sm font-medium">
                            ← Précédent
                        </a>
                        <button type="submit"
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            Suivant
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-guest-layout> 