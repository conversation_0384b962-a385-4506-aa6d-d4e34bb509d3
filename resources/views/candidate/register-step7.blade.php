<x-guest-layout>
    <x-slot name="title">Inscription Candidat - Étape 7/7</x-slot>

    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">
                    Finalisez votre profil
                </h2>
                <p class="text-gray-600">
                    Dernière étape avant de rejoindre notre communauté
                </p>
            </div>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-lg">
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <!-- Progress Bar -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">Étape 7 sur 7</span>
                        <span class="text-sm text-gray-500">100%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full" style="width: 100%"></div>
                    </div>
                </div>

                <form class="space-y-6" action="{{ route('candidate.registerStore') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="step" value="7">
                    
                    <!-- Message de succès -->
                    @if(session('success'))
                        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-4">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif
                    
                    <!-- Données des étapes précédentes -->
                    @foreach(['email', 'password', 'password_confirmation', 'profile_picture', 'first_name', 'last_name', 'date_of_birth', 'category', 'phone', 'residence', 'name', 'country_of_residence', 'commune', 'criminal_record', 'vehicle', 'permits', 'profession_1', 'duration_1', 'activity_fields', 'professions_list', 'formations', 'job_types', 'contract_type', 'availability', 'work_rate', 'native_language', 'fluent_languages', 'intermediate_languages', 'basic_languages'] as $field)
                        @if(session("registration_data.$field"))
                            <input type="hidden" name="{{ $field }}" value="{{ session("registration_data.$field") }}">
                        @endif
                    @endforeach

                    <!-- Professions exercées supplémentaires -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Professions exercées supplémentaires
                        </label>
                        <div class="space-y-4">
                            @for($i = 2; $i <= 5; $i++)
                                <div class="border border-gray-300 rounded-md p-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Expérience {{ $i }}</h4>
                                    <div class="grid grid-cols-1 gap-4">
                                        <div>
                                            <label for="profession_{{ $i }}" class="block text-sm font-medium text-gray-700">Profession exercée {{ $i }}</label>
                                            <input type="text" name="profession_{{ $i }}" id="profession_{{ $i }}" 
                                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                                                   value="{{ old("profession_$i") }}">
                                        </div>
                                        <div>
                                            <label for="duration_{{ $i }}" class="block text-sm font-medium text-gray-700">Durée de la profession exercée {{ $i }}</label>
                                            <input type="text" name="duration_{{ $i }}" id="duration_{{ $i }}" 
                                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                                                   value="{{ old("duration_$i") }}">
                                        </div>
                                    </div>
                                </div>
                            @endfor
                        </div>
                    </div>

                    <!-- Formation -->
                    <div>
                        <label for="formations_final" class="block text-sm font-medium text-gray-700">
                            Formation *
                        </label>
                        <div class="mt-1">
                            <select id="formations_final" name="formations_final[]" multiple required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('formations_final') border-red-500 @enderror">
                                @foreach($formations as $formation)
                                    <option value="{{ $formation->id }}" {{ in_array($formation->id, old('formations_final', [])) ? 'selected' : '' }}>
                                        {{ $formation->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @error('formations_final')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- CV joint -->
                    <div>
                        <label for="cv" class="block text-sm font-medium text-gray-700">
                            CV joint
                        </label>
                        <div class="mt-1">
                            <input id="cv" name="cv" type="file" accept=".pdf,.doc,.docx"
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('cv') border-red-500 @enderror">
                        </div>
                        <p class="mt-1 text-sm text-gray-500">
                            Formats acceptés: PDF, DOC, DOCX. Taille maximale: 2MB
                        </p>
                        @error('cv')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Certificats de travail -->
                    <div>
                        <label for="certificates" class="block text-sm font-medium text-gray-700">
                            Certificat(s) de travail(s)
                        </label>
                        <div class="mt-1">
                            <input id="certificates" name="certificates[]" type="file" accept=".pdf,.doc,.docx" multiple
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('certificates') border-red-500 @enderror">
                        </div>
                        <p class="mt-1 text-sm text-gray-500">
                            Formats acceptés: PDF, DOC, DOCX. Taille maximale: 2MB par fichier
                        </p>
                        @error('certificates')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Certificats d'étude/Diplômes -->
                    <div>
                        <label for="diplomas" class="block text-sm font-medium text-gray-700">
                            Certificat(s) d'étude(s)/Diplôme(s)
                        </label>
                        <div class="mt-1">
                            <input id="diplomas" name="diplomas[]" type="file" accept=".pdf,.doc,.docx" multiple
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm @error('diplomas') border-red-500 @enderror">
                        </div>
                        <p class="mt-1 text-sm text-gray-500">
                            Formats acceptés: PDF, DOC, DOCX. Taille maximale: 2MB par fichier
                        </p>
                        @error('diplomas')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Captcha -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Captcha *
                        </label>
                        <div class="mt-1">
                            {!! NoCaptcha::renderJs() !!}
                            {!! NoCaptcha::display() !!}
                        </div>
                        @error('g-recaptcha-response')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Conditions générales -->
                    <div>
                        <div class="flex items-center">
                            <input id="terms" name="terms" type="checkbox" required
                                class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded @error('terms') border-red-500 @enderror"
                                {{ old('terms') ? 'checked' : '' }}>
                            <label for="terms" class="ml-2 block text-sm text-gray-900">
                                J'accepte les <a href="#" class="text-primary hover:underline">conditions générales d'utilisation</a> *
                            </label>
                        </div>
                        @error('terms')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="flex items-center justify-between">
                        <a href="{{ route('candidate.registerStep6') }}" 
                           class="text-primary hover:text-primary-dark text-sm font-medium">
                            ← Précédent
                        </a>
                        <button type="submit"
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            Finaliser l'inscription
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-guest-layout> 