@php
    use App\Models\Country;
    $countries = Country::with(['regions' => function($query) {
        $query->where('is_active', true);
    }])->where('is_active', true)->get();
@endphp

<style>
    .feedback-button {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        /* Pour s'assurer qu'il   est au-dessus des autres éléments */
    }
</style>

<x-candidate-dashboard-layout>
    <!-- Title -->
    <div class="border-l-4 rounded-sm border-primary px-6 py-2 mb-8">
        <h1 class="text-2xl font-semibold text-primary">MODIFIER LE PROFIL</h1>
    </div>

    <!-- Session Status -->
    <x-auth-session-status class="mb-4" :status="session('status')" />

    <!-- Profile Form -->
    <section class="space-y-6">
        <form method="POST" action="{{ route('candidate.profile.update') }}" enctype="multipart/form-data" novalidate>
            @csrf
            @method('PUT')

            <!-- Profile Photo and Visibility -->
            <div class="border-b border-gray-200 pb-4">
                <h2 class="text-lg font-semibold text-primary">Profil</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <div>
                        <x-input-label for="profile_picture" value="{!! __('candidate_register.photo_de_profil_facultatif_') !!}" />
                        <x-upload-profile id="profile_picture" :profileImage="optional($civility->photo)->url" />
                        <x-input-error :messages="$errors->get('profile_picture')" class="mt-2" />
                    </div>
                    <div>
                        <x-input-label for="visibility" value="Afficher mon profil *" />
                        <x-select-input id="visibility" name="visibility" class="block mt-1 w-full" required>
                            <option value="1" @selected(old('visibility', $civility->visibility ?? '1') == '1')>Afficher</option>
                            <option value="0" @selected(old('visibility', $civility->visibility ?? '1') == '0')>Masquer</option>
                        </x-select-input>
                        <x-input-error :messages="$errors->get('visibility')" class="mt-2" />
                    </div>
                </div>
            </div>

            <!-- Personal Information Section -->
            <div class="border-b border-gray-200 pb-4">
                <h2 class="text-lg font-semibold text-primary">Données personnelles</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <!-- First Name -->
                    <div>
                        <x-input-label for="first_name" value="{!! __('candidate_register.prenom_') !!}" />
                        <x-text-input id="first_name" class="block mt-1 w-full" type="text" name="first_name"
                            :value="old('first_name', $civility->first_name ?? '')" required />
                        <x-input-error :messages="$errors->get('first_name')" class="mt-2" />
                    </div>
                    <!-- Last Name -->
                    <div>
                        <x-input-label for="last_name" value="{!! __('candidate_register.nom_') !!}" />
                        <x-text-input id="last_name" class="block mt-1 w-full" type="text" name="last_name"
                            :value="old('last_name', $civility->last_name ?? '')" required />
                        <x-input-error :messages="$errors->get('last_name')" class="mt-2" />
                    </div>
                    <!-- Date of Birth -->
                    <div>
                        <x-input-label for="date_of_birth" value="{!! __('candidate_register.date_de_naissance_') !!}" />
                        <x-text-input id="date_of_birth" class="block mt-1 w-full" type="date" name="date_of_birth"
                            :value="old('date_of_birth', $civility->date_of_birth ?? '')" required />
                        <x-input-error :messages="$errors->get('date_of_birth')" class="mt-2" />
                    </div>
                    <!-- Category -->
                    <div>
                        <x-input-label for="category" value="Catégorie *" />
                        <x-select-input id="category" class="block mt-1 w-full" name="category" required>
                            <option value="current_profiles" @selected(old('category', $civility->category ?? '') == 'current_profiles')>Profils courants</option>
                            <option value="retired" @selected(old('category', $civility->category ?? '') == 'retired')>Retraité(e)s</option>
                            <option value="migrants" @selected(old('category', $civility->category ?? '') == 'migrants')>Migrant(e)s</option>
                            <option value="students" @selected(old('category', $civility->category ?? '') == 'students')>Étudiant(e)s</option>
                        </x-select-input>
                        <x-input-error :messages="$errors->get('category')" class="mt-2" />
                    </div>
                    <!-- Phone -->
                    <div>
                        <x-input-label for="phone" value="{!! __('candidate_register.numxro_de_txlxphone_') !!}" />
                        <x-text-input id="phone" class="block mt-1 w-full" type="tel" name="phone"
                            :value="old('phone', $phones->first()->number ?? '')" required />
                        <x-input-error :messages="$errors->get('phone')" class="mt-2" />
                    </div>
                    <!-- Email -->
                    <div>
                        <x-input-label for="email" value="{!! __('candidate_register.email_') !!}" />
                        <x-text-input id="email" disabled class="block mt-1 w-full" type="email" name="email"
                            :value="old('email', $user->email)" required autocomplete="username" />
                        <x-input-error :messages="$errors->get('email')" class="mt-2" />
                    </div>
                    <!-- Vehicle -->
                    <div>
                        <x-input-label for="vehicle" value="{!! __('candidate_register.titulaire_d_un_vxhicule') !!}" />
                        <x-select-input id="vehicle" name="vehicle" class="block mt-1 w-full">
                            <option value="yes" @selected(old('vehicle', $civility->vehicle ? 'yes' : 'no') == 'yes')>Oui</option>
                            <option value="no" @selected(old('vehicle', $civility->vehicle ? 'yes' : 'no') == 'no')>Non</option>
                        </x-select-input>
                        <x-input-error :messages="$errors->get('vehicle')" class="mt-2" />
                    </div>
                    <!-- Permit -->
                    <div>
                        <x-input-label for="permits" value="{!! __('candidate_register.permis_de_conduire_') !!}" />
                        <x-select-input id="permits" name="permits[]" class="block mt-1 w-full" multiple>
                            {{-- <option value="without-permit" @selected(old('permits', $civility->permit_id ?? '') == 'without-permit')>{!! __('candidate_register.sans_permis') !!}</option> --}}
                            @foreach ($permits as $item)
                                <option value="{{ $item->id }}" @if (in_array($item->id, old('permits', $userPermits ?? []))) selected @endif>
                                    {{ $item->name }}</option>
                            @endforeach
                        </x-select-input>
                        <x-input-error :messages="$errors->get('permits')" class="mt-2" />
                    </div>
                    <!-- Residence -->
                    <div>
                        <x-input-label for="residence" value="{!! __('candidate_register.suisse_permis_de_sxjour_') !!}" />
                        <x-select-input id="residence" name="residence" class="block mt-1 w-full" required>
                            <option value="suisse" @selected(old('residence', $civility->residence_permit_id ?? '') == 'suisse')>{!! __('candidate_register.suisse') !!}</option>
                            @foreach ($residencePermits as $item)
                                <option value="{{ $item->id }}" @selected(old('residence', $civility->residence_permit_id ?? '') == $item->id)>{{ $item->name }}
                                </option>
                            @endforeach
                        </x-select-input>
                        <x-input-error :messages="$errors->get('residence')" class="mt-2" />
                    </div>
                    <!-- Criminal Record -->
                    <div>
                        <x-input-label for="criminal_record" value="{!! __('candidate_register.casier_judiciaire_vierge_facultatif_') !!}" />
                        <x-select-input id="criminal_record" name="criminal_record" class="block mt-1 w-full">
                            <option value="yes" @selected(old('criminal_record', $civility->criminal_record ? 'yes' : 'no') == 'yes')>Oui</option>
                            <option value="no" @selected(old('criminal_record', $civility->criminal_record ? 'yes' : 'no') == 'no')>Non</option>
                            <option value="skip" @selected(old('criminal_record', $civility->criminal_record ? 'yes' : 'skip') == 'skip')>{!! __('candidate_register.je_passe_cette_xtape') !!}</option>
                        </x-select-input>
                        <x-input-error :messages="$errors->get('criminal_record')" class="mt-2" />
                    </div>
                </div>
            </div>

            <!-- Location Information -->
            <div class="border-b border-gray-200 pb-4">
                <h2 class="text-lg font-semibold text-primary">Lieu de domicile</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <!-- Country of Residence -->
                    <div>
                        <x-input-label for="country_of_residence" value="{!! __('candidate_register.pays_de_rxsidence_') !!}" />
                        <x-select-input id="country_of_residence" name="country_of_residence"
                            class="block mt-1 w-full" required>
                            @foreach ($countries as $item)
                                <option value="{{ $item->id }}" @selected(old('country_of_residence', $civility->country_of_residence_country_id ?? '') == $item->id)  id-country="{{ $item->id }}">{{ $item->name }}</option>
                            @endforeach
                        </x-select-input>
                        <x-input-error :messages="$errors->get('country_of_residence')" class="mt-2" />
                    </div>
                    <!-- Commune -->
                    {{-- <div>
                        <x-input-label for="commune" value="{!! __('candidate_register.commune_de_domicile') !!}" />
                        <x-text-input id="commune" name="commune" class="block mt-1 w-full" type="text"
                            :value="old('commune', $civility->commune ?? '')" required />
                        <x-input-error :messages="$errors->get('commune')" class="mt-2" />
                        <x-input-error :messages="$errors->get('latitude_longitude')" class="mt-2" />
                    </div> --}}

                    <div>
                        <x-input-label for="commune" value="{!! __('candidate_register.commune_de_domicile') !!}" />
                        <x-select-input id="commune" name="commune" class="block mt-1 w-full" required>
                            <!-- Les options seront injectées par JS -->
                        </x-select-input>
                        <x-input-error :messages="$errors->get('commune')" class="mt-2" />
                    </div>
                </div>
                <div class="grid grid-cols-1 gap-6 mt-4">
                    <div>
                        <x-input-label for="address" value="Adresse de domicile (Rue,NPA,Ville) *" />
                        <x-text-input-address-completion id="address" name="address" class="block mt-1 w-full"
                            type="text" :value="old('address', $user->getAddress()->name ?? '')"
                            latitude_longitude="{{ $user->getAddress()->lat . ',' . $user->getAddress()->log }}"
                            required />
                        <x-input-error :messages="$errors->get('address')" class="mt-2" />
                    </div>
                </div>
                <div class="mt-6 flex justify-center items-center overflow-x-auto overflow-y-hidden">
                    <x-show-point-map :address="$user->getAddress()->name" :latitude="$user->getAddress()->lat" :longitude="$user->getAddress()->log" />
                </div>
            </div>

            <!-- Desired Profession Section -->
            <div class="border-b border-gray-200 pb-4">
                <h2 class="text-lg font-semibold text-primary">Domaine(s) & profession(s) recherché(e)(s)</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <!-- Activity Fields -->
                    <div>
                        <x-input-label for="activity_fields" value="{!! __('candidate_register.domaine_s_d_activitx_') !!}" />
                        <x-select-input id="activity_fields" name="activity_fields[]" class="block mt-1 w-full"
                            multiple required>
                            @foreach ($fieldActivities as $item)
                                <option value="{{ $item->id }}" id-activity-u="{{ $item->id }}"
                                    @if (in_array($item->id, old('activity_fields', $userFieldActivities ?? []))) selected @endif>
                                    {{ $item->name }}</option>
                            @endforeach
                        </x-select-input>
                        <x-input-error :messages="$errors->get('activity_fields')" class="mt-2" />
                    </div>
                    <!-- Desired Professions -->
                    <div class="desired_professions {{ empty($userProfessions) ? 'hiddenc' : '' }}">
                        <x-input-label for="desired_professions" value="{!! __('candidate_register.profession_s_recherchxe_s_') !!}" />
                        <x-select-input id="desired_professions" name="desired_professions[]"
                            class="block mt-1 w-full" multiple required>
                            @foreach ($professions as $item)
                                <option value="{{ $item->id }}" id-activity="{{ $item->field_activity_id }}"
                                    @if (in_array($item->id, old('desired_professions', $userProfessions ?? []))) selected @endif>
                                    {{ $item->name }}</option>
                            @endforeach
                        </x-select-input>
                        <x-input-error :messages="$errors->get('desired_professions')" class="mt-2" />
                    </div>
                    <!-- Open to All Professions -->
                    <div>
                        <x-input-label for="open_professions" value="{!! __('candidate_register.ouvert_x_toutes_professions_facultatif_') !!}" />
                        <x-select-input id="open_professions" name="open_professions" class="block mt-1 w-full"
                            required>
                            <option value="yes" @selected(old('open_professions', $civility->open_professions ? 'yes' : 'no') == 'yes')>Oui</option>
                            <option value="no" @selected(old('open_professions', $civility->open_professions ? 'yes' : 'no') == 'no')>Non</option>
                        </x-select-input>
                        <x-input-error :messages="$errors->get('open_professions')" class="mt-2" />
                    </div>
                </div>
            </div>

            <!-- Professional Information Section -->
            <div class="border-b border-gray-200 pb-4">
                <h2 class="text-lg font-semibold text-primary">Informations professionnelles</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <!-- Job Type -->
                    <div>
                        <x-input-label for="job_types" value="{!! __('candidate_register.type_de_poste_recherchx_') !!}" />
                        <x-select-input id="job_types" name="job_types[]" class="block mt-1 w-full" required
                            multiple>
                            @foreach ($typeProfessions as $item)
                                <option value="{{ $item->id }}" @if (in_array($item->id, old('job_types', $userTypeProfessions ?? []))) selected @endif>
                                    {{ $item->name }}</option>
                            @endforeach
                        </x-select-input>
                        <x-input-error :messages="$errors->get('job_types')" class="mt-2" />
                    </div>
                    <!-- Contract Type -->
                    <div>
                        <x-input-label for="contract_type" value="{!! __('candidate_register.je_recherche_contrat_de_travail_') !!}" />
                        <x-select-input id="contract_type" name="contract_type[]" class="block mt-1 w-full" required
                            multiple>
                            <option value="call" @if (in_array('call', is_array($civility->contract_type) ? $civility->contract_type : explode(',', $civility->contract_type ?? ''))) selected @endif>Travail sur appel
                            </option>
                            <option value="cdi" @if (in_array('cdi', is_array($civility->contract_type) ? $civility->contract_type : explode(',', $civility->contract_type ?? ''))) selected @endif>Contrat à durée
                                indéterminée/fixe (CDI)</option>
                            <option value="cdd" @if (in_array('cdd', is_array($civility->contract_type) ? $civility->contract_type : explode(',', $civility->contract_type ?? ''))) selected @endif>Contrat à durée
                                déterminée (CDD)</option>
                        </x-select-input>
                        <x-input-error :messages="$errors->get('contract_type')" class="mt-2" />
                    </div>
                    <!-- Availability -->
                    <div>
                        <x-input-label for="availability" value="{!! __('candidate_register.disponibilitx_') !!}" />
                        <x-select-input id="availability" name="availability" class="block mt-1 w-full" required>
                            @foreach ($responsibilities as $item)
                                <option value="{{ $item->id }}" @selected(old('availability', $civility->responsibility_candidate_id ?? '') == $item->id)>{{ $item->name }}
                                </option>
                            @endforeach
                        </x-select-input>
                        <x-input-error :messages="$errors->get('availability')" class="mt-2" />
                    </div>
                    <!-- Work Rate -->
                    <div>
                        <x-input-label for="work_rate" value="{!! __('candidate_register.taux_d_activitx_') !!}" />
                        <x-select-input id="work_rate" name="work_rate" class="block mt-1 w-full" required>
                            @foreach (['100', '90-100', '80-100', '90', '80-90', '70-90', '80', '70-80', '60-80', '70', '60-70', '50-70', '60', '50-60', '40-60', '50', '40-50', '30-50', '40', '30-40', '20-40', '20-30', '30', '10-30', '20', '10-20', '10'] as $rate)
                                <option value="{{ $rate }}" @selected(old('work_rate', $civility->work_rate ?? '') == $rate)>
                                    {{ $rate }}%</option>
                            @endforeach
                        </x-select-input>
                        <x-input-error :messages="$errors->get('work_rate')" class="mt-2" />
                    </div>
                </div>
            </div>

            <!-- Language Section -->
            <div class="border-b border-gray-200 pb-4">
                <h2 class="text-lg font-semibold text-primary">Langues</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <!-- Native Language -->
                    <div>
                        <x-input-label for="native_language" value="{!! __('candidate_register.langue_s_maternelle') !!}" />
                        <x-select-input id="native_language" name="native_language[]" class="block mt-1 w-full"
                            multiple>
                            @foreach ($languages as $item)
                                <option value="{{ $item->id }}" @if (in_array($item->id, old('native_language', $nativeLanguages ?? []))) selected @endif>
                                    {{ $item->name }}</option>
                            @endforeach
                        </x-select-input>
                        <x-input-error :messages="$errors->get('native_language')" class="mt-2" />
                    </div>
                    <!-- Fluent Languages -->
                    <div>
                        <x-input-label for="fluent_languages" value="{!! __('candidate_register.langue_s_parlxe_s_couramment') !!}" />
                        <x-select-input id="fluent_languages" name="fluent_languages[]" class="block mt-1 w-full"
                            multiple>
                            @foreach ($languages as $item)
                                <option value="{{ $item->id }}" @if (in_array($item->id, old('fluent_languages', $fluentLanguages ?? []))) selected @endif>
                                    {{ $item->name }}</option>
                            @endforeach
                        </x-select-input>
                        <x-input-error :messages="$errors->get('fluent_languages')" class="mt-2" />
                    </div>
                    <!-- Intermediate Languages -->
                    <div>
                        <x-input-label for="intermediate_languages" value="{!! __('candidate_register.langue_s_parlxe_s_avec_notion_intermxdiaire') !!}" />
                        <x-select-input id="intermediate_languages" name="intermediate_languages[]"
                            class="block mt-1 w-full" multiple>
                            @foreach ($languages as $item)
                                <option value="{{ $item->id }}" @if (in_array($item->id, old('intermediate_languages', $intermediateLanguages ?? []))) selected @endif>
                                    {{ $item->name }}</option>
                            @endforeach
                        </x-select-input>
                        <x-input-error :messages="$errors->get('intermediate_languages')" class="mt-2" />
                    </div>
                    <!-- Basic Languages -->
                    <div>
                        <x-input-label for="basic_languages" value="{!! __('candidate_register.langue_s_parlxe_s_avec_notion_de_base') !!}" />
                        <x-select-input id="basic_languages" name="basic_languages[]" class="block mt-1 w-full"
                            multiple>
                            @foreach ($languages as $item)
                                <option value="{{ $item->id }}"
                                    @if (in_array($item->id, old('basic_languages', $basicLanguages ?? []))) selected @endif>
                                    {{ $item->name }}</option>
                            @endforeach
                        </x-select-input>
                        <x-input-error :messages="$errors->get('basic_languages')" class="mt-2" />
                    </div>
                </div>
            </div>

            <!-- Formation Section -->
            <div class="border-b border-gray-200 pb-4">
                <h2 class="text-lg font-semibold text-primary">Formations</h2>
                <div class="mt-4">
                    <x-select-input id="formations" name="formations[]" class="block mt-1 w-full" multiple required>
                        @foreach ($formations as $item)
                            <option value="{{ $item->id }}" @if (in_array($item->id, old('formations', $userFormations ?? []))) selected @endif>
                                {{ $item->name }}</option>
                        @endforeach
                    </x-select-input>
                    <x-input-error :messages="$errors->get('formations')" class="mt-2" />
                </div>
            </div>

            <!-- Professional Experience Section -->
            <div class="border-b border-gray-200 pb-4">
                <h2 class="text-lg font-semibold text-primary">Expérience(s) professionnelle(s)</h2>
                @for ($i = 1; $i <= 5; $i++)
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                        <!-- Profession -->
                        <div>
                            <x-input-label for="profession_{{ $i }}"
                                value="{!! __('candidate_register.profession_excercxe') !!} {{ $i }}" />
                            <x-text-input id="profession_{{ $i }}" class="block mt-1 w-full"
                                type="text" name="profession_{{ $i }}" :value="old('profession_' . $i, $civility->{'profession_' . $i} ?? '')" />
                            <x-input-error :messages="$errors->get('profession_' . $i)" class="mt-2" />
                        </div>
                        <!-- Duration -->
                        <div>
                            <x-input-label for="duration_{{ $i }}"
                                value="{!! __('candidate_register.durxe_de_la_profession_excercxe') !!} {{ $i }}" />
                            <x-select-input id="duration_{{ $i }}" name="duration_{{ $i }}"
                                class="block mt-1 w-full">
                                <option></option>
                                @foreach (['1 semaine', '2 semaine', '3 semaine', 'Entre 1 mois et 6 mois', 'Entre 6 mois et 12 mois', 'Entre 2 ans et 5 ans', 'Entre 6 ans et 10 ans', 'Plus de 11 ans'] as $duration)
                                    <option value="{{ $duration }}" @selected(old('duration_' . $i, $civility->{'duration_' . $i} ?? '') == $duration)>
                                        {{ $duration }}</option>
                                @endforeach
                            </x-select-input>
                            <x-input-error :messages="$errors->get('duration_' . $i)" class="mt-2" />
                        </div>
                    </div>
                @endfor
            </div>

            <!-- Documents Section -->
            <div class="border-b border-gray-200 pb-4">
                <h2 class="text-lg font-semibold text-primary">Documents</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <!-- CV -->
                    <div>
                        <x-input-label for="cv" value="CV joint" />
                        <x-upload-file id="cv" :file="optional($civility->cv)->url" :filename="optional($civility->cv)->original_name" />
                        <x-input-error :messages="$errors->get('cv')" class="mt-2" />
                    </div>
                    <!-- Certificats de travail et/ou d'études -->
                    <div>
                        <x-input-label for="certificates" value="Certificats de travail et/ou d'études (PDF, DOC, DOCX)" />
                        {{-- Affichage des fichiers déjà uploadés --}}
                        @if(!empty($civility->certificate_files) && is_iterable($civility->certificate_files))
                            <ul class="list-disc pl-5 mb-2">
                                @foreach($civility->certificate_files as $file)
                                    <li>
                                        <a href="{{ $file->url }}" target="_blank" class="text-blue-600 underline">
                                            {{ $file->original_name ?? $file->name }}
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                        @endif
                        {{-- Upload de nouveaux fichiers --}}
                        <x-upload-multi-select-file id="certificates" name="certificates[]" accept=".pdf,.doc,.docx" />
                        <x-input-error :messages="$errors->get('certificates.*')" class="mt-2" />
                    </div>
                </div>

                <!-- Recaptcha -->
               <!--  <div class="mt-6">
                    <x-recaptcha />
                    @error('g-recaptcha-response')
                        <p class="text-red-600 text-sm mt-2">{{ $message }}</p>
                    @enderror
                </div> -->
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end mt-8">
                <x-primary-button class="ms-4">
                    Enregistrer
                </x-primary-button>
            </div>
        </form>



        <div class="feedback-button">
            <x-feedback-button-guest active="true" />
        </div> 
        <x-feedback-modal />
                        @isset($priorities, $severities)
                            <x-feedback-modal :priorities="$priorities" :severities="$severities" :user="auth()->user()" />
                        @endisset

    </section>


    <!-- Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            flatpickr("#date_of_birth");

            // Pass old values for desired_professions and activity_fields to JavaScript
            const oldDesiredProfessionsList = @json(old('desired_professions', $userProfessions ?? []));
            const oldActivityFields = @json(old('activity_fields', $userFieldActivities ?? []));

            const selectElement = document.getElementById("activity_fields");

            // Define the function outside to be reusable
            function handleActivityFieldsChange() {
                let selectedOptions = Array.from(selectElement.selectedOptions);
                let activityIds = selectedOptions.map(option => option.getAttribute("id-activity-u"));

                const professionContainer = document.querySelector('.desired_professions');

                if (activityIds.length > 0) {
                    professionContainer.classList.remove('hiddenc');

                    // Effectuer la requête AJAX pour récupérer les professions
                    fetch(`/get-all-profession?ids=${activityIds.join(',')}`)
                        .then(response => response.json())
                        .then(data => {
                            let select = document.getElementById("desired_professions");
                            select.innerHTML = ''; // Clear existing options

                            // Ajouter les options récupérées depuis l'API
                            data.forEach(item => {
                                let option = document.createElement("option");
                                option.value = item.id;
                                option.textContent = item.name;
                                // Check if this item was previously selected
                                if (oldDesiredProfessionsList.includes(item.id)) {
                                    option.selected = true;
                                }
                                select.appendChild(option);
                            });

                            // Remove existing MultiSelectTag UI for desired_professions
                            const existingMultiSelectTagDivs = document.querySelectorAll(
                                '.desired_professions .multi-select-tag'
                            );
                            existingMultiSelectTagDivs.forEach(div => div.remove());

                            // Initialize MultiSelectTag for desired_professions
                            new MultiSelectTag("desired_professions", {
                                rounded: true,
                                placeholder: 'Filtre ...',
                                tagColor: {
                                    textColor: '#0aaedb',
                                    borderColor: '#6ddaf8',
                                    bgColor: '#e7f9fe',
                                }
                            });
                        })
                        .catch(error => {
                            console.error('Erreur lors de la récupération des professions:', error);
                        });
                } else {
                    professionContainer.classList.add('hiddenc');
                    // Also clear the professions select if no activity fields are selected
                    let select = document.getElementById("desired_professions");
                    select.innerHTML = '';
                    // Remove existing MultiSelectTag UI
                    const existingMultiSelectTagDivs = document.querySelectorAll(
                        '.desired_professions .multi-select-tag'
                    );
                    existingMultiSelectTagDivs.forEach(div => div.remove());
                    // Reinitialize empty MultiSelectTag to show placeholder
                    new MultiSelectTag("desired_professions", {
                        rounded: true,
                        placeholder: 'Filtre ...',
                        tagColor: {
                            textColor: '#0aaedb',
                            borderColor: '#6ddaf8',
                            bgColor: '#e7f9fe',
                        }
                    });
                }
            }

            // Initial call for activity_fields and desired_professions when the page loads
            if (oldActivityFields && oldActivityFields.length > 0) {
                // Manually set selected options for activity_fields before initializing MultiSelectTag
                Array.from(selectElement.options).forEach(option => {
                    if (oldActivityFields.includes(parseInt(option.value))) {
                        option.selected = true;
                    }
                });
                // Trigger the change handler to load professions
                handleActivityFieldsChange();
            }

            setTimeout(() => {
                // Traduction des jours
                const transDay = {
                    "sun": "Dim",
                    "mon": "Lun",
                    "tue": "Mar",
                    "wed": "Mer",
                    "thu": "Jeu",
                    "fri": "Ven",
                    "sat": "Sam"
                };

                const allDayToFlatpickr = document.querySelectorAll('.flatpickr-weekday');
                // console.log("allDayToFlatpickr", allDayToFlatpickr, transDay);

                allDayToFlatpickr.forEach(day => {
                    const dayText = day.textContent.toLowerCase().trim(); // Normalisation du texte
                    if (transDay[dayText]) { // Vérifie si le jour existe dans la traduction
                        // console.log("Translating day:", day.textContent, "=>", transDay[dayText]);
                        day.textContent = transDay[dayText]; // Applique la traduction
                    }
                });

                // Traduction des mois
                const transMonth = {
                    "january": "Janvier",
                    "february": "Février",
                    "march": "Mars",
                    "april": "Avril",
                    "may": "Mai",
                    "june": "Juin",
                    "july": "Juillet",
                    "august": "Août",
                    "september": "Septembre",
                    "october": "Octobre",
                    "november": "Novembre",
                    "december": "Décembre"
                };

                const allMonthToFlatpickr = document.querySelectorAll('.flatpickr-monthDropdown-month');
                // console.log("allMonthToFlatpickr", allMonthToFlatpickr, transMonth);

                allMonthToFlatpickr.forEach(month => {
                    const monthText = month.textContent.toLowerCase()
                        .trim(); // Normalisation du texte
                    if (transMonth[monthText]) { // Vérifie si le mois existe dans la traduction
                        // console.log("Translating month:", month.textContent, "=>", transMonth[
                        //     monthText]);
                        month.textContent = transMonth[monthText]; // Applique la traduction
                    }
                });
            }, 3000);

            new MultiSelectTag("permits", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
                onChange: function(values) {
                    console.log(values)
                }
            })
            new MultiSelectTag("activity_fields", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
                onChange: handleActivityFieldsChange,
            });
            new MultiSelectTag("native_language", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
            });
            new MultiSelectTag("fluent_languages", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
            });
            new MultiSelectTag("intermediate_languages", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
            });
            new MultiSelectTag("basic_languages", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
            });
            new MultiSelectTag("formations", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
            });
            new MultiSelectTag("job_types", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
                onChange: function(values) {
                    console.log(values)
                }
            });
            new MultiSelectTag("contract_type", {
                rounded: true,
                placeholder: 'Filtre ...',
                tagColor: {
                    textColor: '#0aaedb',
                    borderColor: '#6ddaf8',
                    bgColor: '#e7f9fe',
                },
                onChange: function(values) {
                    console.log(values);
                }
            });



            document.getElementById('country_of_residence').addEventListener('change', function() {
                // Récupère l'option sélectionnée
                console.log("ICI country_of_residence")
                const selectedOption = this.options[this.selectedIndex];

                // Récupère la valeur de l'attribut 'id-country' de l'option sélectionnée
                const countryId = selectedOption.getAttribute('id-country');

                console.log("countryId =====", countryId)


                // Vérifie si un pays a bien été sélectionné
                if (countryId) {
                    // Effectue une requête AJAX pour récupérer les régions par ID du pays
                    fetch(`/get-all-region?ids=${countryId}`)
                        .then(response => response.json())
                        .then(data => {
                            // Récupère le select des communes ou cantons
                            const communeSelect = document.getElementById('commune');
                            communeSelect.innerHTML = ''; // Réinitialise le select des communes

                            // Ajoute une option par défaut
                            const defaultOption = document.createElement('option');
                            defaultOption.value = '';
                            defaultOption.disabled = true;
                            defaultOption.selected = true;
                            defaultOption.textContent = '{{ __('Sélectionner Région ou Canton') }}';
                            communeSelect.appendChild(defaultOption);

                            // Ajoute les options de région au select des communes
                            data.forEach(region => {
                                const option = document.createElement('option');
                                option.value = region.name; // Utilise l'id de la région
                                option.textContent = region.name; // Affiche le nom de la région
                                communeSelect.appendChild(option);
                            });
                        })
                        .catch(error => {
                            console.error('Erreur lors de la récupération des régions:', error);
                        });
                }
            });

            function updateRegionsSelect(countryId, selectedRegion = null) {
                const communeSelect = document.getElementById('commune');
                communeSelect.innerHTML = '';

                // Option par défaut
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.disabled = true;
                defaultOption.selected = true;
                defaultOption.textContent = '{{ __("Sélectionner Région ou Canton") }}';
                communeSelect.appendChild(defaultOption);

                if (regionsByCountry[countryId]) {
                    regionsByCountry[countryId].forEach(region => {
                        const option = document.createElement('option');
                        option.value = region.name;
                        option.textContent = region.name;
                        if (selectedRegion && selectedRegion === region.name) {
                            option.selected = true;
                        }
                        communeSelect.appendChild(option);
                    });
                }
            }

            // Initialisation si un pays est déjà sélectionné (édition)
            const countrySelect = document.getElementById('country_of_residence');
            const initialCountryId = countrySelect.value;
            const initialRegion = "{{ old('commune', $civility->commune ?? '') }}";
            if (initialCountryId) {
                updateRegionsSelect(initialCountryId, initialRegion);
            }

            // Mise à jour lors du changement de pays
            countrySelect.addEventListener('change', function() {
                updateRegionsSelect(this.value);
            });
        });

        // On prépare un objet JS avec les régions par pays
        const regionsByCountry = @json($countries->mapWithKeys(function($country) {
            return [$country->id => $country->regions->map(function($region) {
                return ['id' => $region->id, 'name' => $region->name];
            })];
        }));
    </script>
</x-candidate-dashboard-layout>
