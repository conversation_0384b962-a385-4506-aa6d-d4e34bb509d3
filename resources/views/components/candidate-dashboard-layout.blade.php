<x-app-layout>
    <div class="py-12">
        <div class="flex min-h-screen relative">
            <!-- Hamburger <PERSON><PERSON> for Mobile -->
            <button id="sidebar-toggle" class="md:hidden fixed top-4 left-4 z-50 bg-white p-2 rounded-md shadow-lg">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
                </svg>
            </button>

            <!-- Sidebar -->
            <aside id="sidebar" class="fixed md:static w-64 bg-gray-50 border-r border-gray-200 transform -translate-x-full md:translate-x-0 transition-transform duration-300 ease-in-out h-screen overflow-y-auto z-40">
                <div class="p-4">
                    <nav class="space-y-4">
                        <x-sidebar-link :href="route('candidate.dashboard')" :active="request()->routeIs('candidate.dashboard')">
                            <i class="fas fa-user-circle mr-2"></i> {!! __('components_candidate-dashboard-layout.profil') !!}
                        </x-sidebar-link>

                        <x-sidebar-link :href="route('candidate.messages')" :active="request()->routeIs('candidate.messages')">
                            <i class="fas fa-comments mr-2"></i> {!! __('components_candidate-dashboard-layout.messages') !!}
                        </x-sidebar-link>

                        <x-sidebar-link :href="route('contact')" :active="request()->routeIs('contact')">
                            <i class="fa-brands fa-ideal mr-2"></i> {!! __('layouts_navigation.proposition_s_d_amxlioration_s_') !!}
                        </x-sidebar-link>

                        <x-sidebar-link :href="route('candidate.update-password.index')" :active="request()->routeIs('candidate.update-password.index')">
                            <i class="fas fa-lock mr-2"></i> {!! __('components_candidate-dashboard-layout.mot_de_passe') !!}
                        </x-sidebar-link>

                        <x-sidebar-link :href="route('candidate.profile.delete.index')" :active="request()->routeIs('candidate.profile.delete.index')">
                            <i class="fas fa-trash-alt mr-2"></i> {!! __('components_candidate-dashboard-layout.effacer_le_profil') !!}
                        </x-sidebar-link>

                       

                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <a href="route('logout')" onclick="event.preventDefault(); this.closest('form').submit();"
                                class="flex items-center text-gray-600 hover:text-primary p-4">
                                <i class="fas fa-sign-out-alt mr-2"></i> {!! __('components_candidate-dashboard-layout.dxconnexion') !!}
                            </a>
                        </form>
                    </nav>
                </div>
            </aside>

            <!-- Overlay for mobile -->
            <div id="sidebar-overlay" class="fixed inset-0 bg-black opacity-50 z-30 hidden md:hidden"></div>

            <!-- Main Content -->
            <main class="flex-1 p-8 bg-white md:ml-0 min-h-screen">
                {{ $slot }}
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            function toggleSidebar() {
                const isOpen = sidebar.classList.contains('translate-x-0');
                sidebar.classList.toggle('translate-x-0');
                sidebar.classList.toggle('-translate-x-full');
                overlay.classList.toggle('hidden');

                // Empêcher le défilement du body quand le menu est ouvert sur mobile
                document.body.style.overflow = isOpen ? 'auto' : 'hidden';
            }

            sidebarToggle.addEventListener('click', toggleSidebar);
            overlay.addEventListener('click', toggleSidebar);

            // Fermer le menu si la fenêtre est redimensionnée au-dessus du breakpoint mobile
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 768) { // 768px est le breakpoint md de Tailwind
                    sidebar.classList.remove('translate-x-0');
                    sidebar.classList.add('-translate-x-full');
                    overlay.classList.add('hidden');
                    document.body.style.overflow = 'auto';
                }
            });

            // Ajuster la hauteur du sidebar sur mobile
            function adjustSidebarHeight() {
                if (window.innerWidth < 768) {
                    const windowHeight = window.innerHeight;
                    sidebar.style.height = `${windowHeight}px`;
                } else {
                    sidebar.style.height = '100%';
                }
            }

            // Appeler la fonction au chargement et au redimensionnement
            adjustSidebarHeight();
            window.addEventListener('resize', adjustSidebarHeight);
        });
    </script>
</x-app-layout>
