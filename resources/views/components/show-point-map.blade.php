<div id="map" {!! $attributes->merge(['style' => 'z-index: 2 !important;height: 300px;width:100%;']) !!}></div>

<script>
    function setPositionWithMarkerOnMap(zoomLevel = 13, map, latitude, longitude, address) {
        // Centrer la carte et ajouter un marqueur de l'addresse et lat, lon marquée
        map.setView([latitude, longitude], zoomLevel);
        const marker = L.marker([latitude, longitude]).addTo(map);
        marker.bindPopup(`<b>${address}</b><br>Latitude: ${latitude}<br>Longitude: ${longitude}`).openPopup();
        // center the map on the marker
        map.panTo([latitude, longitude]);
    }

    function getCoordinateLatLon(address) {
        // address is like Ambanja, Madagascar
        const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}`;
        fetch(url)
            .then(response => response.json())
            .then(data => {
                console.log('data', data);
                if (data && data.length > 0) {
                    const {
                        lat,
                        lon
                    } = data[0];
                    // Get meilleure reponse
                    const latitude = parseFloat(lat);
                    const longitude = parseFloat(lon);

                    // setPositionOnMap(map,latitude,longitude,address);
                    return {
                        lat: latitude,
                        lon: longitude
                    };

                } else {
                    return null;
                }
            })
            .catch(error => {
                console.error("Erreur lors de la requête à Nominatim :", error);
                return null;
            });
    }
</script>

<script>
    @php
        // delete special characters
        $address = preg_replace('/[^A-Za-z0-9\-]/', ' ', $address);
    @endphp

    const address_name = "{{ $address }}";

    let zoomDefault = 2;
    let latDefault = 0;
    let longDefault = 0;
    let maxZoom = 19;
    document.addEventListener('DOMContentLoaded', () => {
        // Initialiser la carte Leaflet
        const map = L.map('map').setView([latDefault, longDefault], zoomDefault); // Coordonnées par défaut

        // Ajouter une couche de tuiles OpenStreetMap
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: maxZoom,
            attribution: '© Cyclone-placement'
        }).addTo(map);

        // Fonction pour obtenir la position via Nominatim

        setPositionWithMarkerOnMap(13, map, parseFloat("{{ $latitude }}"), parseFloat(
            "{{ $longitude }}"), limitShowText(address_name, 50));

        document.addEventListener('update-map', (e) => {
            if (e.detail.latitude && e.detail.longitude && e.detail.address) {
                // Supprimer le marqueur précédent
                map.eachLayer(function(layer) {
                    if (layer instanceof L.Marker) {
                        map.removeLayer(layer);
                    }
                });

                // Mettre à jour la carte avec les nouvelles coordonnées
                setPositionWithMarkerOnMap(13, map, parseFloat(e.detail.latitude), parseFloat(e.detail
                    .longitude), e.detail.address);
            }
        });

        // Appeler la fonction avec l'adresse que vous souhaitez afficher
        // getCoordinates("Ambanja");
    });

    function limitShowText(text, limit = 50) {
        return text.length > limit ? text.substring(0, limit) + '...' : text;
    }
</script>
