@props([
    'priorities' => [],
    'severities' => [],
    'user' => auth()->user(),
])

<style>
    /* Dans votre fichier CSS */
    .disabled-email {
        background-color: #f0f0f0;
        /* Grisâtre */
        color: #a0a0a0;
        /* Texte gris */
        cursor: not-allowed;
        /* Curseur pour indiquer que l'élément est désactivé */
    }

    input[readonly] {
        background-color: #f0f0f0;
        /* Grisâtre */
        color: #a0a0a0;
        /* Texte gris */
    }
</style>

<div id="feedbackModal"
    class="fixed inset-0 hidden bg-black bg-opacity-50 flex justify-center items-center transition-opacity z-50">
    <div class="bg-white p-8 rounded-lg w-[600px] shadow-xl relative scale-95 transition-transform">
        <!-- Bouton de fermeture -->
        <button onclick="closeFeedbackModal()"
            class="absolute top-3 right-3 text-gray-600 hover:text-gray-900 text-xl">&times;</button>

        <h2 class="text-xl font-semibold text-gray-800 mb-4">📌 Signaler une anomalie</h2>

        <form id="feedbackForm" class="space-y-4">
            <label class="block text-gray-700 font-medium">Email * :</label>
            <input type="text" id="email" name="email" required
                class="w-full border px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400
                {{ $user ? 'disabled-email' : '' }}"
                placeholder="Email..." value="{{ $user?->email ?? '' }}" {{ $user ? 'readonly' : '' }}>

            {{-- Catégorie --}}
            {{--
            <label class="block text-gray-700 font-medium">Catégorie * :</label>
            <select id="category_id" name="category_id" required
                class="w-full border px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400">
                <option value="" disabled hidden selected>(Choisir)</option>
            </select>
            --}}

            {{-- Priorité --}}
            {{--
            <label class="block text-gray-700 font-medium">Priorité :</label>
            <select id="priority" name="priority"
                class="w-full border px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400">
                @foreach ($priorities as $priority)
                    <option value="{{ $priority->value }}" {{ $priority->is_default ? 'selected' : '' }}>
                        {{ $priority->name }}
                    </option>
                @endforeach
            </select>
            --}}

            {{-- Sévérité --}}
            {{--
            <label class="block text-gray-700 font-medium">Sévérité :</label>
            <select id="severity" name="severity"
                class="w-full border px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400">
                @foreach ($severities as $severity)
                    <option value="{{ $severity->value }}" {{ $severity->is_default ? 'selected' : '' }}>
                        {{ $severity->name }}
                    </option>
                @endforeach
            </select>
            --}}

            <!-- Résumé du problème -->
            <label class="block text-gray-700 font-medium">Résumé du problème * :</label>
            <input type="text" id="summary" name="summary" required
                class="w-full border px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400"
                placeholder="Titre du problème...">

            <!-- Description -->
            <label class="block text-gray-700 font-medium">Description * :</label>
            <textarea id="description" name="description" required
                class="w-full border px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400"
                placeholder="Décrivez le problème rencontré..."></textarea>

            <!-- Boutons -->
            <div class="flex justify-end gap-3">
                <button type="button" onclick="closeFeedbackModal()"
                    class="bg-gray-400 text-white px-4 py-2 rounded-lg hover:bg-gray-500 transition">
                    Annuler
                </button>
                <button type="submit" id="submitFeedback"
                    class="bg-[#0BBBEF] text-white px-6 py-2 rounded-lg shadow-md hover:bg-[#0099cc] transition relative">
                    <span id="submitText">Envoyer</span>
                    <span id="loadingSpinner" class="hidden absolute inset-0 flex items-center justify-center">
                        <svg class="w-6 h-6 animate-spin text-white" fill="none" viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z"></path>
                        </svg>
                    </span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Ajout du script SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- JavaScript pour gérer le modal -->
<script>
    var fetchCategoriesUrl = "{{ url('/mantis/categories') }}";

    function openFeedbackModal() {
        document.getElementById('feedbackModal').classList.remove('hidden');
        document.getElementById('feedbackModal').classList.add('opacity-100', 'scale-100');
        document.body.classList.add('overflow-hidden'); // Empêcher le scroll en arrière-plan

        // Charger les catégories si elles ne sont pas encore chargées
        let select = document.getElementById("category_id");
        if (select.options.length === 1) {
            loadCategories();
        }
    }

    function closeFeedbackModal() {
        document.getElementById('feedbackModal').classList.add('hidden');
        document.getElementById('feedbackModal').classList.remove('opacity-100', 'scale-100');
        document.body.classList.remove('overflow-hidden'); // Réactiver le scroll en arrière-plan
    }

    function loadCategories() {
        fetch(fetchCategoriesUrl)
            .then(response => response.json())
            .then(data => {
                let select = document.getElementById("category_id");
                select.innerHTML =
                    '<option value="" disabled hidden selected>(Choisir)</option>'; // Reset du select

                if (data.categories && data.categories.length > 0) {
                    data.categories.forEach(category => {
                        let option = document.createElement("option");
                        option.value = category.id;
                        option.textContent = category.name;
                        select.appendChild(option);
                    });
                }
            })
            .catch(error => console.error("Erreur lors du chargement des catégories :", error));
    }

    document.getElementById('feedbackForm').addEventListener('submit', function(event) {
        event.preventDefault();

        // 1. Utilisation de FormData pour mieux capturer les valeurs
        const formData = new FormData(this);

        // 2. Conversion en objet et vérification des valeurs
        const data = {
            email: formData.get('email') || '',
            summary: formData.get('summary'),
            description: formData.get('description'),
            // category_id: parseInt(formData.get('category_id')),
            // priority_id: parseInt(formData.get('priority')),
            // severity_id: parseInt(formData.get('severity'))
        };

        console.log('Données avant envoi:', data); // Vérifiez que l'email est bien capturé

        // Gestion de l'état du bouton
        const submitButton = document.getElementById('submitFeedback');
        const submitText = document.getElementById('submitText');
        const loadingSpinner = document.getElementById('loadingSpinner');

        submitButton.disabled = true;
        submitText.classList.add('hidden');
        loadingSpinner.classList.remove('hidden');

        // Envoi de la requête
        fetch("{{ url('/mantis/add-issue') }}", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify(data)
            })
            .then(async response => {
                const contentType = response.headers.get('content-type');

                if (!response.ok) {
                    if (contentType?.includes('application/json')) {
                        const errorData = await response.json();
                        throw new Error(
                            errorData.errors ? Object.values(errorData.errors).join('\n') :
                            errorData.message || 'Erreur inconnue'
                        );
                    }
                    throw new Error(`Erreur ${response.status}: ${await response.text()}`);
                }

                if (!contentType?.includes('application/json')) {
                    throw new Error('Réponse non-JSON reçue');
                }

                return response.json();
            })
            .then(result => {
                if (result.redirect) {
                    window.location.href = result.redirect;
                    return;
                }

                Swal.fire({
                    title: result.success ? "Succès !" : "Erreur",
                    text: result.message || (result.success ?
                        "Anomalie ajoutée avec succès." :
                        "Une erreur s'est produite."),
                    icon: result.success ? "success" : "error",
                    confirmButtonColor: result.success ? "#0BBBEF" : "#d33",
                    timer: result.success ? 3000 : null,
                    showConfirmButton: !result.success
                }).then(() => {
                    if (result.success) {
                        closeFeedbackModal();
                        this.reset();
                    }
                });
            })
            .catch(error => {
                console.error('Erreur:', error);
                Swal.fire({
                    title: "Erreur",
                    text: error.message || "Une erreur inattendue s'est produite",
                    icon: "error",
                    confirmButtonColor: "#d33"
                });
            })
            .finally(() => {
                submitButton.disabled = false;
                submitText.classList.remove('hidden');
                loadingSpinner.classList.add('hidden');
            });
    });

    document.addEventListener("DOMContentLoaded", loadCategories);

    // Script JavaScript pour vérifier si l'utilisateur est connecté et désactiver le champ
    document.addEventListener("DOMContentLoaded", function() {
        const emailInput = document.getElementById('email');

        // Vérifiez si un utilisateur est connecté (par exemple, via une variable globale ou une session PHP)
        const userConnected = {!! json_encode($user) !!}; // Renvoie un booléen ou un objet utilisateur en PHP

        if (userConnected) {
            emailInput.setAttribute('readonly', 'true');
            emailInput.classList.add('disabled-email');
        }
    });
</script>
