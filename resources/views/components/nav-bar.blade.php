<header x-data="{ isOpen: false }" class="bg-white shadow-md py-4 px-5 z-50">
    <div class="container mx-auto flex justify-between items-center">
        <!-- Logo -->
        <div class="flex items-center">
            <a href="{{ url('/') }}" class="text-primary text-2xl font-bold">
                <x-application-logo class=" w-40 text-primary" />
            </a>
        </div>

        <!-- <PERSON>er Icon (for small screens) -->
        <div class="md:hidden flex items-center">
            @guest
                <a href="{{ route('login') }}" class="text-gray-700 hover:text-primary flex items-center mx-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                        fill="none">
                        <path
                            d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                        </path>
                        <path
                            d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                        </path>
                    </svg>
                </a>
            @endguest
            @auth
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <button type="submit" class="text-gray-700 hover:text-primary flex items-center mx-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none">
                            <path
                                d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21"
                                stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                            </path>
                            <path
                                d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z"
                                stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                            </path>
                        </svg>
                        Se déconnecter
                    </button>
                </form>
            @endauth
            <button @click="isOpen = !isOpen" type="button"
                class="text-gray-600 hover:text-primary focus:outline-none focus:text-gray-500">
                <svg x-show="!isOpen" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7">
                    </path>
                </svg>
                <svg x-show="isOpen" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                    </path>
                </svg>
            </button>
        </div>

        <!-- Navigation (for medium and larger screens) -->
        <nav class="hidden md:flex space-x-6">
            <a href="{{ url('/') }}"
                class="uppercase text-lg {{ Route::is('home') ? 'text-gray-600 underline' : 'text-primary' }}">Accueil</a>
            <a href="{{ route('candidate.index') }}"
                class="uppercase text-lg {{ Route::is('candidate.index') ? 'text-gray-600 underline' : 'text-primary' }}">Candidats</a>
            <a href="{{ route('recruiters') }}"
                class="uppercase text-lg {{ Route::is('recruiters') ? 'text-gray-600 underline' : 'text-primary' }}">Recruteurs</a>
        </nav>

        <!-- Authentification (for medium and larger screens) -->
        <div class="hidden md:flex items-center space-x-6">
            @guest
                <a href="{{ route('login') }}" class="text-gray-700 hover:text-primary flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                        fill="none">
                        <path
                            d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                        </path>
                        <path
                            d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                        </path>
                    </svg>
                    Se connecter
                </a>
            @endguest
            @auth
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <button type="submit" class="text-gray-700 hover:text-primary flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none">
                            <path
                                d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21"
                                stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                            </path>
                            <path
                                d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z"
                                stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                            </path>
                        </svg>
                        Se déconnecter
                    </button>
                </form>
            @endauth
        </div>

        <!-- Dropdown Menu (for small screens) -->
        <div class="md:hidden absolute top-16 left-0 right-0 z-50 bg-white shadow-lg rounded-lg" x-show="isOpen"
            @click.away="isOpen = false" x-transition>
            <nav class="flex flex-col divide-y divide-gray-200 p-4">
                <a href="{{ url('/') }}"
                    class="uppercase text-lg py-2 {{ Route::is('home') ? 'text-gray-600 underline' : 'text-primary' }}">Accueil</a>
                <a href="{{ route('candidate.index') }}"
                    class="uppercase text-lg py-2 {{ Route::is('candidates') ? 'text-gray-600 underline' : 'text-primary' }}">Candidats</a>
                <a href="{{ route('recruiters') }}"
                    class="uppercase text-lg py-2 {{ Route::is('recruiters') ? 'text-gray-600 underline' : 'text-primary' }}">Recruteurs</a>
            </nav>
        </div>
    </div>
</header>
