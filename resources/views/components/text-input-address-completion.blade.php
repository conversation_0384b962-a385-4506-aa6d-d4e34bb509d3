@props(['disabled' => false, 'latitude_longitude' => ''])

@php
    $id = $attributes->get('id', $attributes->get('name'));
@endphp

<div class="relative">
    <input id="{{ $id }}" autocomplete="new-address" {{ $disabled ? 'disabled' : '' }} {!! $attributes->merge([
        'class' => 'py-3 text-textNormal border-gray-300 bg-gray-50 focus:bg-white focus:border-primary-500 focus:ring-primary rounded-md transition-all duration-100',
        'placeholder' => $disabled ? 'Veuillez d\'abord sélectionner une région/canton' : ''
    ]) !!}>

    <!-- Spinner pour indiquer le chargement -->
    <div id="{{ $id }}-spinner" class="absolute inset-y-0 right-0 flex items-center pr-3 hidden">
        <svg class="animate-spin h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
    </div>
</div>

<div class="relative">
    <ul id="{{ $id }}-suggestions" class="absolute top-0 z-50 bg-white w-full left-0 border border-gray-200 overflow-hidden rounded-md"></ul>
</div>

<input type="hidden" name="latitude_longitude" id="latitude_longitude" value="{{ $latitude_longitude }}">

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const addressInput = document.getElementById('{{ $id }}');
        const suggestions = document.getElementById('{{ $id }}-suggestions');
        const spinner = document.getElementById('{{ $id }}-spinner');
        const countrySelect = document.getElementById('country_of_residence');
        const communeSelect = document.getElementById('commune');
        let currentBbox = null;
        let debounceTimeout;

        // Désactiver initialement si une commune est déjà sélectionnée
        if (communeSelect && communeSelect.value) {
            addressInput.disabled = true;
            addressInput.placeholder = "Chargement des limites géographiques...";
            updateBbox(); // Lancer le chargement de la BBOX immédiatement
        }

        // Fonction pour récupérer la bbox d'une région
        async function fetchRegionBbox(regionName, countryName) {
            if (!regionName || !countryName) return null;

            try {
                const response = await fetch(`https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(regionName)},${encodeURIComponent(countryName)}&format=json&polygon_geojson=1&limit=1`);
                const data = await response.json();

                if (data.length > 0 && data[0].boundingbox) {
                    const [minLat, maxLat, minLon, maxLon] = data[0].boundingbox;
                    return `${minLon},${minLat},${maxLon},${maxLat}`;
                }
                return null;
            } catch (error) {
                console.error('Erreur lors de la récupération de la bbox:', error);
                return null;
            }
        }

        // Mettre à jour la bbox quand la région change
        async function updateBbox() {
            const countryName = countrySelect?.options[countrySelect.selectedIndex]?.text;
            const regionName = communeSelect?.options[communeSelect.selectedIndex]?.text;

            if (countryName && regionName && regionName !== 'Sélectionner Région ou Canton') {
                console.log(`Recherche des coordonnées pour: ${regionName}, ${countryName}`);
                spinner.classList.remove('hidden');
                addressInput.disabled = true;
                addressInput.placeholder = "Chargement des limites géographiques...";

                currentBbox = await fetchRegionBbox(regionName, countryName);

                console.log('Nouvelle BBOX:', currentBbox);
                spinner.classList.add('hidden');

                if (currentBbox) {
                    addressInput.disabled = false;
                    addressInput.placeholder = "";
                    addressInput.focus();
                } else {
                    addressInput.placeholder = "Impossible de charger les limites géographiques";
                }
            } else {
                addressInput.disabled = true;
                addressInput.placeholder = "Veuillez d'abord sélectionner une région/canton";
            }
        }

        // Écouter les changements de sélection
        countrySelect?.addEventListener('change', updateBbox);
        communeSelect?.addEventListener('change', updateBbox);

        async function fetchAddressSuggestions(query) {
            if (!query || query.length < 2) return [];

            let url = `https://photon.komoot.io/api/?q=${encodeURIComponent(query)}&limit=5&lang=fr`;

            if (currentBbox) {
                url += `&bbox=${currentBbox}`;
            }

            console.log('URL de recherche:', url);

            try {
                const response = await fetch(url);
                const data = await response.json();
                return data.features.map(feature => {
                    const street = feature.properties.street || '';
                    const housenumber = feature.properties.housenumber || '';
                    const city = feature.properties.city || '';
                    const postcode = feature.properties.postcode || '';
                    const district = feature.properties.district || '';
                    const country = feature.properties.country || '';
                    const state = feature.properties.state || '';

                    const formattedAddress = [
                            street && housenumber ? `${street} ${housenumber}` : street,
                            postcode && city ? `${postcode} ${city}` : postcode,
                            district,
                            state,
                            country
                        ]
                        .filter(Boolean)
                        .join(', ');

                    return {
                        description: feature.properties.name,
                        address: formattedAddress,
                        latitude: feature.geometry.coordinates[1],
                        longitude: feature.geometry.coordinates[0]
                    };
                });
            } catch (error) {
                console.error('Request failed:', error);
                return [];
            }
        }

        function displaySuggestions(addresses) {
            suggestions.innerHTML = '';
            addresses.forEach(address => {
                const li = document.createElement('li');
                li.textContent = address.address || address.description;
                li.setAttribute('class', 'cursor-pointer px-3 py-2 hover:bg-gray-200 border-b border-gray-200');
                li.addEventListener('click', async () => {
                    addressInput.value = address.address || address.description;
                    addressInput.setAttribute('latitude', address.latitude);
                    addressInput.setAttribute('longitude', address.longitude);

                    let inputlatitudeLongitude = document.getElementById('latitude_longitude');
                    if (!inputlatitudeLongitude) {
                        inputlatitudeLongitude = document.createElement('input');
                        inputlatitudeLongitude.setAttribute('id', 'latitude_longitude');
                        inputlatitudeLongitude.setAttribute('name', 'latitude_longitude');
                        inputlatitudeLongitude.setAttribute('type', 'hidden');
                        addressInput.parentNode.appendChild(inputlatitudeLongitude);
                    }
                    inputlatitudeLongitude.value = `${address.latitude},${address.longitude}`;

                    suggestions.innerHTML = '';

                    const event = new CustomEvent('update-map', {
                        detail: {
                            latitude: address.latitude,
                            longitude: address.longitude,
                            address: address.address || address.description
                        }
                    });
                    document.dispatchEvent(event);

                    spinner.classList.remove('hidden');

                    setTimeout(() => {
                        console.log('Coordonnées sélectionnées:');
                        console.log('Latitude:', address.latitude);
                        console.log('Longitude:', address.longitude);
                        console.log('Adresse:', address.address);
                        spinner.classList.add('hidden');
                    }, 1000);
                });
                suggestions.appendChild(li);
            });
        }

        addressInput.addEventListener('input', async (event) => {
            clearTimeout(debounceTimeout);
            debounceTimeout = setTimeout(async () => {
                const query = addressInput.value;
                if (query.length < 2) {
                    suggestions.innerHTML = '';
                    spinner.classList.add('hidden');
                    return;
                }
                spinner.classList.remove('hidden');
                try {
                    const addresses = await fetchAddressSuggestions(query);
                    displaySuggestions(addresses);
                } catch (error) {
                    suggestions.innerHTML = '<li class="px-3 py-2 text-red-500">Erreur lors de la récupération des adresses.</li>';
                } finally {
                    spinner.classList.add('hidden');
                }
            }, 300); // 300ms après la dernière saisie
        });
    });
</script>
