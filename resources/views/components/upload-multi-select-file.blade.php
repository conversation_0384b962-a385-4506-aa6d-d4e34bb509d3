@props(['id', 'name' => null, 'accept' => null])

<div class="upload-multi-select-file">
    <div id="{{ $id }}-displayContainer" class="flex flex-col gap-2" style="display: none;">
        <ul id="{{ $id }}-fileList" class="list-disc pl-5 w-full">
            <!-- Les <li> sont générés dynamiquement, il faut ajouter break-all et block dans le JS -->
        </ul>
        <x-secondary-button id="{{ $id }}-clearButton" type="button" class="text-md">Vider la sélection</x-secondary-button>
    </div>
    <div id="{{ $id }}-editContainer" class="flex items-center gap-4">
        <input type="file" id="{{ $id }}-fileInput" name="{{ $name ?? $id }}" accept="{{ $accept ?? '*' }}" multiple style="display: none;">
        <x-primary-button id="{{ $id }}-uploadButton" type="button">
            Sélectionner des fichiers
        </x-primary-button>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const fileInput = document.getElementById("{{ $id }}-fileInput");
        const uploadButton = document.getElementById("{{ $id }}-uploadButton");
        const displayContainer = document.getElementById("{{ $id }}-displayContainer");
        const fileList = document.getElementById("{{ $id }}-fileList");
        const clearButton = document.getElementById("{{ $id }}-clearButton");

        uploadButton.addEventListener('click', function () {
            fileInput.click();
        });

        fileInput.addEventListener('change', function () {
            fileList.innerHTML = '';
            if (fileInput.files.length > 0) {
                displayContainer.style.display = 'flex';
                Array.from(fileInput.files).forEach((file, idx) => {
                    const li = document.createElement('li');
                    li.textContent = file.name + ' (' + Math.round(file.size/1024) + ' Ko)';
                    li.className = "break-all block"; // dans le JS lors de la création des <li>
                    fileList.appendChild(li);
                });
            } else {
                displayContainer.style.display = 'none';
            }
        });

        clearButton.addEventListener('click', function () {
            fileInput.value = '';
            fileList.innerHTML = '';
            displayContainer.style.display = 'none';
        });
    });
</script> 