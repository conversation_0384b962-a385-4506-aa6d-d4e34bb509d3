<!-- resources/views/components/upload-file.blade.php -->
@props(['id'])

<div class="upload-file">
    <!-- Affichage du fichier uploadé et des actions -->
    <div id="{{ $id }}-displayContainer" class="flex items-center gap-4" style="display: none;">
        <div class="w-1/2 flex flex-col items-center gap-2">
            <x-primary-button id="{{ $id }}-modifyButton" type="button"
                class="text-md">Modifier</x-primary-button>
            <x-secondary-button id="{{ $id }}-deleteButton" type="button"
                class="text-md">Supprimer</x-secondary-button>
        </div>
        <div class="w-1/2 flex flex-col justify-center items-center">
            <span id="{{ $id }}-fileName" class="text-gray-700 break-all"></span>
            <!-- Afficher un aperçu si le fichier est une image -->
            <img id="{{ $id }}-filePreview" src="#" alt="Aperçu du fichier"
                class="w-44 h-44 rounded-lg object-cover border-2 border-gray-300 mt-2" style="display: none;">
        </div>
    </div>

    <!-- Bouton Téléverser -->
    <div id="{{ $id }}-editContainer" class="flex flex-col md:flex-row items-start md:items-center gap-2 w-full">
        <input type="file" id="{{ $id }}-fileInput" name="{{ $attributes->get('name') ?? $id }}"
            accept="{{ $attributes->get('accept') || '*' }}" style="display: none;">
        <x-primary-button id="{{ $id }}-uploadButton" type="button">
            Télécharger
        </x-primary-button>

        @if ($attributes->get('file'))
            <x-a href="{{ $attributes->get('file') }}" target="_bank" download>
                <span class="text-blue-400 underline break-all block max-w-xs md:max-w-none">
                    {{ $attributes->get('filename') ?? 'Télécharger' }}
                </span>
            </x-a>
        @endif
    </div>
</div>

<script>
    window.uploadFileConfig = window.uploadFileConfig || [];
    window.uploadFileConfig.push("{{ $id }}");
</script>
