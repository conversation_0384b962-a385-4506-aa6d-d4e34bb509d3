<!-- resources/views/components/upload-profile.blade.php -->
@props(['id', 'profileImage' => null])

<div class="upload-profile">
    <!-- Affichage de l'image recadrée et des actions supplémentaires -->
    <div id="{{ $id }}-displayContainer" class="flex items-center gap-4" style="display: none;">
        <div class="w-1/2 flex flex-col  items-center gap-2">
            <x-primary-button id="{{ $id }}-modifyButton" type="button"
                class="text-md">Modifier</x-primary-button>
            <x-secondary-button id="{{ $id }}-deleteButton" type="button"
                class="text-md">Supprimer</x-secondary-button>
        </div>
        <div class="w-1/2 flex justify-center items-center">
            <img id="{{ $id }}-croppedImage"
                class="w-44 h-44 rounded-lg object-cover border-2 border-gray-300">
        </div>
    </div>

    <!-- Bouton Téléverser -->
    <div id="{{ $id }}-editContainer" class="flex items-center gap-4">
        <div>
            <input type="file" id="{{ $id }}-profileImageInput"
                name="{{ $attributes->get('name') ?? $id }}" accept="image/*" style="display: none;">
            <x-primary-button id="{{ $id }}-uploadButton" type="button">
                Télécharger
            </x-primary-button>
        </div>
        <div id="{{ $id }}-profileImageViewer" class="w-1/2 flex flex-col justify-center items-center">
            @if ($profileImage)
                <img class="w-44 h-44 rounded-lg object-cover border-2 border-gray-300" src="{{$profileImage}}">
                <x-a href="{{ $profileImage }}" target="_bank" download>
                    Télécharger
                </x-a>
            @endif
        </div>
    </div>

    <!-- Modal de recadrage -->
    <div id="{{ $id }}-modal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 w-96 relative">
            <span id="{{ $id }}-closeModal"
                class="absolute top-2 right-2 text-gray-500 hover:text-gray-700 cursor-pointer text-xl">&times;</span>
            <h3 class="text-lg font-semibold mb-4">Recadrer l'image</h3>
            <div id="{{ $id }}-cropContainer">
                <img id="{{ $id }}-imageToCrop" src="{{ $profileImage }}"
                    class="w-full max-h-80 object-contain">
            </div>
            <x-primary-button id="{{ $id }}-cropButton" type="button" class="w-full">Recadrer et
                Afficher</x-primary-button>
        </div>
    </div>
</div>

<script>
    window.profileCropperConfig = window.profileCropperConfig || [];
    window.profileCropperConfig.push("{{ $id }}");
</script>
