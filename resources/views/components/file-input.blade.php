<div class="relative">
    <!-- Cha<PERSON> de fichier masqué -->
    <input
        type="file"
        name="attachment"
        id="attachment"
        class="opacity-0 absolute inset-0 w-full h-full cursor-pointer"
    >
    <!-- Bouton personnalisé -->
    <label for="attachment" class="bg-[#0BBBEF] text-white px-6 py-3 rounded-md shadow-md hover:bg-[#0099cc] transition cursor-pointer inline-block">
        Choisir un fichier
    </label>
    <!-- Texte pour afficher le nom du fichier sélectionné -->
    <span id="file-name-attachment" class="ml-4 text-gray-700">Aucun fichier choisi</span>
</div>

<!-- Script pour mettre à jour le nom du fichier -->
<script>
    document.getElementById('attachment').addEventListener('change', function(event) {
        const fileName = event.target.files[0] ? event.target.files[0].name : 'Aucun fichier choisi';
        console.log(fileName);
        document.getElementById('file-name-attachment').textContent = fileName;
    });
</script>
