<x-guest-layout>
    <x-slot name="title">Accueil</x-slot>
    <!-- Video Background -->
    <div class="relative my-20 flex flex-col items-center justify-center h-full text-center bg-transparent">
        <h1 class="text-4xl italic text-primary font-title">BIENVENUE SUR LA PLATEFORME PREMIUM DU RECRUTEMENT ET DE
            L'EMPLOI.</h1>
        <p class="text-2xl mt-6 italic text-primary">UNIR LES TALENTS, CONSTRUIRE L'AVENIR.</p>
    </div>
    <section class="relative overflow-hidden w-full h-[50vh] md:h-[80vh] my-0">
        <!-- Iframe for YouTube video -->
        <iframe class="absolute md:-top-72 left-0 w-full h-full md:h-[150vh] object-cover"
            src="https://www.youtube.com/embed/2EzNcR4huaY?autoplay=1&mute=1&controls=0&rel=0&playsinline=1&loop=1&playlist=2EzNcR4huaY&showinfo=0"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen></iframe>
    </section>

    <!-- New Section with Accordion - Mobile -->
    <div class="md:hidden">
        <section class="px-10 bg-slate-100  pt-10 pb-10">
            <h2 class="text-center italic text-5xl font-title text-primary mb-10">POURQUOI RECOURIR À L'ENGAGEMENT DES
                CATÉGORIES
                SUIVANTES ?</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 md:px-48">

                <!-- Profils Courants -->
                <div class="text-left">
                    <img src="{{ asset('images/iStock-1478421401-WEB.jpg') }}" alt="Profils Courants"
                        class="w-full h-auto mb-4">
                    <h3 class="text-primary text-2xl font-title">PROFILS COURANTS</h3>
                    <div x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center text-gray-500 text-xl mt-2">
                            <span class="w-4 mr-2 transform" :class="{ 'rotate-90': open }">
                                <svg class="w-full rotate-90" viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M288.662 352H31.338c-17.818 0-26.741-21.543-14.142-34.142l128.662-128.662c7.81-7.81 20.474-7.81 28.284 0l128.662 128.662c12.6 12.599 3.676 34.142-14.142 34.142z">
                                    </path>
                                </svg>
                            </span>
                            Les avantages
                        </button>
                        <div x-show="open" class="mt-4 text-gray-600" x-transition>
                            <p class="text-justify">Dans le monde professionnel, ces personnes se positionnent généralement
                                comme les ressources
                                humaines qui répondent à tous types de besoins pour l'entreprise, sur le moyen long terme.
                                Filtrez, découvrez, sélectionnez celles qui correspondent à vos attentes, à vos critères,
                                parmi un large éventail de profils à différents niveaux de connaissances, de compétences, de
                                qualifications et d'expériences.&nbsp;<strong>Les employés inscrits dans cette rubrique
                                    représentent un atout pour répondre aux besoins des employeurs tous secteurs
                                    confondus</strong>.</p>
                        </div>
                    </div>
                </div>

                <!-- Retraité(e)s -->
                <div class="text-left">
                    <img src="{{ asset('images/iStock-658065260-WEB.jpg') }}" alt="Retraités" class="w-full h-auto mb-4">
                    <h3 class="text-primary text-2xl font-title">RETRAITÉ(E)S</h3>
                    <div x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center text-gray-500 text-xl mt-2">
                            <span class="w-4 mr-2 transform" :class="{ 'rotate-90': open }">
                                <svg class="w-full rotate-90" viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M288.662 352H31.338c-17.818 0-26.741-21.543-14.142-34.142l128.662-128.662c7.81-7.81 20.474-7.81 28.284 0l128.662 128.662c12.6 12.599 3.676 34.142-14.142 34.142z">
                                    </path>
                                </svg>
                            </span>
                            Les avantages
                        </button>
                        <div x-show="open" class="mt-4 text-gray-600" x-transition>
                            <p class="text-justify">Les retraité(e)s actif(ve)s sont des ressources précieuses pour les
                                entreprises. Selon l'OFS,
                                en moyenne 32% des personnes poursuivent une activité professionnelle au-delà de l'âge légal
                                de la retraite. Elles sont généralement employées <b>pour des&nbsp;</b><b>remplacements, du
                                    mentorat, du soutien scolaire, ou encore, des missions de courte ou de moyenne
                                    durée</b>. Expérience, volonté, engagement, expertise, flexibilité sont les
                                qualificatifs qui les caractérisent. Dans bien des cas, l'employeur peut bénéficier
                                d'avantages économiques, notamment sur les cotisations sociales <a
                                    href="#assurances-sociales">(<u>pl</u><u>us d'infos</u>).</a></p>
                        </div>
                    </div>
                </div>

                <!-- Migrant(e)s -->
                <div class="text-left">
                    <img src="{{ asset('images/iStock-1399155965-WEB.jpg') }}" alt="Migrants" class="w-full h-auto mb-4">
                    <h3 class="text-primary text-2xl font-title">MIGRANT(E)S</h3>
                    <div x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center text-gray-500 text-xl mt-2">
                            <span class="w-4 mr-2 transform" :class="{ 'rotate-90': open }">
                                <svg class="w-full rotate-90" viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M288.662 352H31.338c-17.818 0-26.741-21.543-14.142-34.142l128.662-128.662c7.81-7.81 20.474-7.81 28.284 0l128.662 128.662c12.6 12.599 3.676 34.142-14.142 34.142z">
                                    </path>
                                </svg>
                            </span>
                            Les avantages
                        </button>
                        <div x-show="open" class="mt-4 text-gray-600" x-transition>
                            <p class="text-justify">Les personnes issues de la migration sont porteuses de diversité
                                culturelle. Beaucoup d'entre
                                elles sont qualifiées et expérimentées. Au sein des entreprises, elles contribuent à
                                renforcer un environnement professionnel en constante évolution. Elles peuvent, par
                                ailleurs, être accompagnées par les structures d'aide à l'intégration socioprofessionnelle
                                comme l'EVAM ; principal acteur cantonal vaudois en ce domaine. <strong>L'employeur peut
                                    bénéficier de</strong>&nbsp;<strong>certaines</strong>&nbsp;<strong>aides (financières,
                                    formalités administratives, suivi en emploi, formations / mises à niveau de la
                                    personne).</strong></p>
                        </div>
                    </div>
                </div>

                <!-- Étudiant(e)s -->
                <div class="text-left">
                    <img src="{{ asset('images/iStock-1194817399-WEB.jpg') }}" alt="Étudiants" class="w-full h-auto mb-4">
                    <h3 class="text-primary text-2xl font-title">ÉTUDIANT(E)S</h3>
                    <div x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center text-gray-500 text-xl mt-2">
                            <span class="w-4 mr-2 transform" :class="{ 'rotate-90': open }">
                                <svg class="w-full rotate-90" viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M288.662 352H31.338c-17.818 0-26.741-21.543-14.142-34.142l128.662-128.662c7.81-7.81 20.474-7.81 28.284 0l128.662 128.662c12.6 12.599 3.676 34.142-14.142 34.142z">
                                    </path>
                                </svg>
                            </span>
                            Les avantages
                        </button>
                        <div x-show="open" class="mt-4 text-gray-600" x-transition>
                            <p class="text-justify">L'engagement d'un étudiant participe à la diversité et la vitalité des
                                équipes. Selon l'OFS,
                                70% d'entre eux sont en activité au sein d'entreprises, tout comme auprès de particuliers
                                (soutien scolaire et services à domicile). Ils constituent une réelle plus-value pour
                                l'employeur.<br><b>Leur flexibilité, leur enthousiasme et leur soif d'apprendre leur permet
                                    de s'adapter / se former rapidement selon les exigences de la fonction</b>. <br>Beaucoup
                                d'entre eux sont disponibles en soirée ou en week-end pour des engagements de courte,
                                moyenne ou longue durée.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- New Section with Accordion - Web -->
    <div class="hidden md:block">
         <section class="px-10 bg-slate-100  pt-10 pb-10">
        <h2 class="text-center italic text-5xl font-title text-primary mb-10">POURQUOI RECOURIR À L'ENGAGEMENT DES
            CATÉGORIES
            SUIVANTES ?</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 md:px-48">

            <!-- Profils Courants -->
            <div class="text-left">
                <img src="{{ asset('images/iStock-1478421401-WEB.jpg') }}" alt="Profils Courants"
                    class="w-full h-auto mb-4">
                <h3 class="text-primary text-2xl font-title">PROFILS COURANTS</h3>
                <div x-data="{ open: false }"
                    x-on:click="open = !open; $dispatch('toggle-profiles', open); $dispatch('close-others', 'profiles');"
                    x-on:close-others.window="if ($event.detail !== 'profiles') open = false;">
                    <button class="flex items-center text-gray-500 text-xl mt-2">
                        <span class="w-4 mr-2 transform" :class="{ 'rotate-90': open }">
                            <svg class="w-full rotate-90" viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M288.662 352H31.338c-17.818 0-26.741-21.543-14.142-34.142l128.662-128.662c7.81-7.81 20.474-7.81 28.284 0l128.662 128.662c12.6 12.599 3.676 34.142-14.142 34.142z">
                                </path>
                            </svg>
                        </span>
                        Les avantages
                    </button>
                </div>
            </div>

            <!-- Retraité(e)s -->
            <div class="text-left">
                <img src="{{ asset('images/iStock-658065260-WEB.jpg') }}" alt="Retraités" class="w-full h-auto mb-4">
                <h3 class="text-primary text-2xl font-title">RETRAITÉ(E)S</h3>
                <div x-data="{ open: false }"
                    x-on:click="open = !open; $dispatch('toggle-retraites', open); $dispatch('close-others', 'retraites');"
                    x-on:close-others.window="if ($event.detail !== 'retraites') open = false;">
                    <button class="flex items-center text-gray-500 text-xl mt-2">
                        <span class="w-4 mr-2 transform" :class="{ 'rotate-90': open }">
                            <svg class="w-full rotate-90" viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M288.662 352H31.338c-17.818 0-26.741-21.543-14.142-34.142l128.662-128.662c7.81-7.81 20.474-7.81 28.284 0l128.662 128.662c12.6 12.599 3.676 34.142-14.142 34.142z">
                                </path>
                            </svg>
                        </span>
                        Les avantages
                    </button>
                </div>
            </div>

            <!-- Migrant(e)s -->
            <div class="text-left">
                <img src="{{ asset('images/iStock-1399155965-WEB.jpg') }}" alt="Migrants" class="w-full h-auto mb-4">
                <h3 class="text-primary text-2xl font-title">MIGRANT(E)S</h3>
                <div x-data="{ open: false }"
                    x-on:click="open = !open; $dispatch('toggle-migrants', open); $dispatch('close-others', 'migrants');"
                    x-on:close-others.window="if ($event.detail !== 'migrants') open = false;">
                    <button class="flex items-center text-gray-500 text-xl mt-2">
                        <span class="w-4 mr-2 transform" :class="{ 'rotate-90': open }">
                            <svg class="w-full rotate-90" viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M288.662 352H31.338c-17.818 0-26.741-21.543-14.142-34.142l128.662-128.662c7.81-7.81 20.474-7.81 28.284 0l128.662 128.662c12.6 12.599 3.676 34.142-14.142 34.142z">
                                </path>
                            </svg>
                        </span>
                        Les avantages
                    </button>
                </div>
            </div>

            <!-- Étudiant(e)s -->
            <div class="text-left">
                <img src="{{ asset('images/iStock-1194817399-WEB.jpg') }}" alt="Étudiants" class="w-full h-auto mb-4">
                <h3 class="text-primary text-2xl font-title">ÉTUDIANT(E)S</h3>
                <div x-data="{ open: false }"
                    x-on:click="open = !open; $dispatch('toggle-etudiants', open); $dispatch('close-others', 'etudiants');"
                    x-on:close-others.window="if ($event.detail !== 'etudiants') open = false;">
                    <button class="flex items-center text-gray-500 text-xl mt-2">
                        <span class="w-4 mr-2 transform" :class="{ 'rotate-90': open }">
                            <svg class="w-full rotate-90" viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M288.662 352H31.338c-17.818 0-26.741-21.543-14.142-34.142l128.662-128.662c7.81-7.81 20.474-7.81 28.284 0l128.662 128.662c12.6 12.599 3.676 34.142-14.142 34.142z">
                                </path>
                            </svg>
                        </span>
                        Les avantages
                    </button>
                </div>
            </div>
        </div>
        <div x-data="{
            profiles: false,
            retraites: false,
            migrants: false,
            etudiants: false
        }"
            x-on:toggle-profiles.window="profiles = $event.detail"
            x-on:toggle-retraites.window="retraites = $event.detail"
            x-on:toggle-migrants.window="migrants = $event.detail"
            x-on:toggle-etudiants.window="etudiants = $event.detail"
            x-on:close-others.window="
                const except = $event.detail;
                if (except !== 'profiles') profiles = false;
                if (except !== 'retraites') retraites = false;
                if (except !== 'migrants') migrants = false;
                if (except !== 'etudiants') etudiants = false;
            ">

            <div x-show="profiles" class="mt-4 text-gray-600 px-10 md:px-48" x-transition>
                <p class="text-justify">Dans le monde professionnel, ces personnes se positionnent généralement
                    comme les ressources
                    humaines qui répondent à tous types de besoins pour l'entreprise, sur le moyen long terme.
                    Filtrez, découvrez, sélectionnez celles qui correspondent à vos attentes, à vos critères,
                    parmi un large éventail de profils à différents niveaux de connaissances, de compétences, de
                    qualifications et d'expériences.&nbsp;<strong>Les employés inscrits dans cette rubrique
                        représentent un atout pour répondre aux besoins des employeurs tous secteurs
                        confondus</strong>.</p>
            </div>
            <div x-show="retraites" class="mt-4 text-gray-600 px-10 md:px-48" x-transition>
                <p class="text-justify">Les retraité(e)s actif(ve)s sont des ressources précieuses pour les
                    entreprises. Selon l'OFS,
                    en moyenne 32% des personnes poursuivent une activité professionnelle au-delà de l'âge légal
                    de la retraite. Elles sont généralement employées <b>pour des&nbsp;</b><b>remplacements, du
                        mentorat, du soutien scolaire, ou encore, des missions de courte ou de moyenne
                        durée</b>. Expérience, volonté, engagement, expertise, flexibilité sont les
                    qualificatifs qui les caractérisent. Dans bien des cas, l'employeur peut bénéficier
                    d'avantages économiques, notamment sur les cotisations sociales <a
                        href="#assurances-sociales">(<u>pl</u><u>us d'infos</u>).</a></p>
            </div>
            <div x-show="migrants" class="mt-4 text-gray-600 px-10 md:px-48" x-transition>
                <p class="text-justify">Les personnes issues de la migration sont porteuses de diversité
                    culturelle. Beaucoup d'entre
                    elles sont qualifiées et expérimentées. Au sein des entreprises, elles contribuent à
                    renforcer un environnement professionnel en constante évolution. Elles peuvent, par
                    ailleurs, être accompagnées par les structures d'aide à l'intégration socioprofessionnelle
                    comme l'EVAM ; principal acteur cantonal vaudois en ce domaine. <strong>L'employeur peut
                        bénéficier de</strong>&nbsp;<strong>certaines</strong>&nbsp;<strong>aides (financières,
                        formalités administratives, suivi en emploi, formations / mises à niveau de la
                        personne).</strong></p>
            </div>
            <div x-show="etudiants" class="mt-4 text-gray-600 px-10 md:px-48" x-transition>
                <p class="text-justify">L'engagement d'un étudiant participe à la diversité et la vitalité des
                    équipes. Selon l'OFS,
                    70% d'entre eux sont en activité au sein d'entreprises, tout comme auprès de particuliers
                    (soutien scolaire et services à domicile). Ils constituent une réelle plus-value pour
                    l'employeur.<br><b>Leur flexibilité, leur enthousiasme et leur soif d'apprendre leur permet
                        de s'adapter / se former rapidement selon les exigences de la fonction</b>. <br>Beaucoup
                    d'entre eux sont disponibles en soirée ou en week-end pour des engagements de courte,
                    moyenne ou longue durée.</p>
            </div>
        </div>
    </section>
    </div>

     <!-- New Section: Candidat & Recruteur -->
     <section class="my-20 px-10 bg-transparent container mx-auto">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-10 text-center">
            <!-- Candidat -->
            <div class="col-span-1 flex flex-col items-center">
                <h3 class="text-primary text-4xl  italic font-title mb-4">Candidat</h3>
                <div class=" w-28 border-t-[3px] border-primary my-4"></div>
                <div class="w-full items-center">
                    <!-- Step 1 -->
                    <div class="mb-8">
                        <div
                            class="mt-10 mb-8 text-gray-500 text-2xl font-bold  w-10 h-10 mx-auto flex items-center justify-center rounded-full border-[3px] border-primary">
                            1</div>
                        <p class="mt-2 text-2xl">Inscrivez-vous GRATUITEMENT</p>
                        <div class="mt-2">
                            <!-- Icon for step 1 -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-28 mx-auto" id="Layer_1"
                                data-name="Layer 1" viewBox="0 0 161.57 161.57">
                                <defs>
                                    <style>
                                        .cls-1 {
                                            fill: #706f6f;
                                        }

                                        .cls-1,
                                        .cls-2 {
                                            stroke-width: 0px;
                                        }

                                        .cls-2 {
                                            fill: #15bbee;
                                        }
                                    </style>
                                </defs>
                                <path class="cls-2"
                                    d="M82.25,79.96c.04-.06.06-.12.1-.18.05-.08.1-.16.13-.25.03-.08.05-.16.08-.24.02-.07.05-.14.07-.22.06-.32.06-.66,0-.98-.02-.08-.04-.15-.07-.22-.02-.08-.04-.16-.08-.24-.04-.09-.09-.17-.13-.25-.03-.06-.06-.12-.1-.18-.09-.14-.2-.27-.31-.38l-17.28-17.28c-.98-.98-2.56-.98-3.54,0s-.98,2.56,0,3.54l13.02,13.02H17.39c-1.38,0-2.5,1.12-2.5,2.5s1.12,2.5,2.5,2.5h56.75l-13.02,13.02c-.98.98-.98,2.56,0,3.54.49.49,1.13.73,1.77.73s1.28-.24,1.77-.73l17.28-17.28c.12-.12.22-.25.31-.38Z">
                                </path>
                                <path class="cls-1"
                                    d="M128.75,18.23H49.36c-4.2,0-7.62,3.42-7.62,7.62v30.69h4.66v-30.69c0-1.63,1.33-2.96,2.96-2.96h79.39c1.63,0,2.96,1.33,2.96,2.96v111.26c0,1.63-1.33,2.96-2.96,2.96H49.36c-1.63,0-2.96-1.33-2.96-2.96v-32.45h-4.66v32.45c0,4.2,3.42,7.62,7.62,7.62h79.39c4.2,0,7.62-3.42,7.62-7.62V25.85c0-4.2-3.42-7.62-7.62-7.62Z">
                                </path>
                            </svg>
                        </div>
                    </div>
                    <div class="w-full border-t border-primary my-4"></div>
                    <!-- Step 2 -->
                    <div class="mb-8">
                        <div
                            class="mt-10 mb-8 text-gray-500 text-2xl font-bold  w-10 h-10 mx-auto flex items-center justify-center rounded-full border-[3px] border-primary">
                            2</div>
                        <p class="mt-2 text-2xl">Remplissez votre profil</p>
                        <div class="mt-2">
                            <!-- Icon for step 2 -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-28 mx-auto" id="Layer_1"
                                data-name="Layer 1" viewBox="0 0 161.57 161.57">
                                <defs>
                                    <style>
                                        .cls-1 {
                                            fill: #706f6f;
                                        }

                                        .cls-1,
                                        .cls-2 {
                                            stroke-width: 0px;
                                        }

                                        .cls-2 {
                                            fill: #15bbee;
                                        }
                                    </style>
                                </defs>
                                <path class="cls-2"
                                    d="M130.63,46.35l3.03,3.03-17.06,17.06c-.92.92-.92,2.41,0,3.33s2.41.92,3.33,0l18.73-18.73c.92-.92.92-2.41,0-3.33l-4.69-4.7,6.36-6.36c.81-.81.91-2.06.3-2.97l2.61-2.61c3-3.01,3-7.9,0-10.9l-.46-.46c-2.91-2.92-7.99-2.91-10.9,0l-2.61,2.61c-.9-.6-2.2-.48-2.97.3L56.73,92.18s0,0,0,0c-.2.2-.35.42-.47.66l-10.68,21.35c-.45.91-.27,2,.44,2.72.45.45,1.06.69,1.67.69.36,0,.72-.08,1.05-.25l21.36-10.68c.23-.11.45-.26.65-.45h0s0,0,0-.01M52.95,109.99l6.08-12.16,6.08,6.08-12.16,6.08ZM69.09,101.22l-7.37-7.37,58.21-58.21,7.37,7.37-58.21,58.21ZM130.63,39.69l-7.37-7.37,4.69-4.7,7.37,7.37-4.7,4.69ZM139.89,27.74l-2.58,2.58-4.69-4.69,2.58-2.58c1.13-1.13,3.1-1.13,4.23,0l.46.46c1.17,1.17,1.17,3.07,0,4.24Z">
                                </path>
                                <path class="cls-1"
                                    d="M108.08,136.25c0,1.67-1.36,3.03-3.03,3.03H23.89c-1.67,0-3.03-1.36-3.03-3.03V46.65h21.97c1.32,0,2.38-1.07,2.38-2.38v-21.97h59.84c1.67,0,3.03,1.36,3.03,3.03v4.46h4.77v-4.46c0-4.3-3.5-7.79-7.79-7.79H40.26c-.72,0-1.39.28-1.92.8l-21.45,21.45c-.51.51-.8,1.19-.8,1.92v94.55c0,4.3,3.5,7.79,7.79,7.79h81.16c4.3,0,7.79-3.5,7.79-7.79l-1.32-58.49h-4.77l1.32,58.49ZM40.45,22.96v18.92h-18.92l18.92-18.92Z">
                                </path>
                            </svg>
                        </div>
                    </div>
                    <div class="w-full border-t border-primary my-4"></div>
                    <!-- Step 3 -->
                    <div class="mb-8">
                        <div
                            class="mt-10 mb-8 text-gray-500 text-2xl font-bold  w-10 h-10 mx-auto flex items-center justify-center rounded-full border-[3px] border-primary">
                            3</div>
                        <p class="mt-2 text-2xl">Les recruteurs vous contactent</p>
                        <div class="mt-2">
                            <!-- Icon for step 3 -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-28 mx-auto" id="Layer_1"
                                data-name="Layer 1" viewBox="0 0 161.57 161.57">
                                <defs>
                                    <style>
                                        .cls-1 {
                                            fill: #706f6f;
                                        }

                                        .cls-1,
                                        .cls-2 {
                                            stroke-width: 0px;
                                        }

                                        .cls-2 {
                                            fill: #15bbee;
                                        }
                                    </style>
                                </defs>
                                <path class="cls-1"
                                    d="M117.92,117.87c-.37-1.05-1.16-1.9-2.16-2.35l-.35-.15h0s-18.58-7.79-18.58-7.79c-.07-.03-.14-.06-.23-.08-1.46-.51-3.1-.13-4.18.96l-3.87,3.87c-1.08,1.08-2.77,1.34-4.12.62-7.59-4.09-14.5-9.22-20.53-15.25-6.04-6.04-11.18-12.95-15.26-20.53-.71-1.34-.45-3.03.63-4.11l3.87-3.87c1.1-1.09,1.47-2.74.95-4.21-.02-.07-.05-.14-.07-.21l-7.89-18.79s-.03-.08-.06-.16c-.45-1-1.3-1.79-2.34-2.16-1.04-.37-2.21-.29-3.21.21l-18.83,10.55c-1.92,1.08-3.33,2.93-3.87,5.08-.54,2.16-.17,4.38,1.02,6.24,9.84,15.32,21.41,29.67,34.38,42.65,12.95,12.95,27.3,24.52,42.65,34.39,1.28.82,2.75,1.25,4.26,1.25.66,0,1.32-.08,1.97-.24,2.14-.54,3.99-1.95,5.08-3.87l10.48-18.7.06-.12h0s0-.02.01-.03c.5-.98.57-2.15.21-3.19ZM44.73,79.25c4.29,7.97,9.69,15.23,16.03,21.57,6.34,6.34,13.6,11.73,21.57,16.03,3.05,1.62,6.89,1.05,9.34-1.4l3.61-3.72,18.29,7.67-10.27,18.32c-.49.88-1.31,1.5-2.29,1.74-.96.24-1.94.08-2.76-.44-15.08-9.7-29.18-21.06-41.91-33.79-12.75-12.75-24.11-26.85-33.79-41.91-.53-.83-.69-1.81-.45-2.78.25-.98.86-1.79,1.74-2.28l18.33-10.27,7.65,18.22-3.69,3.69c-2.49,2.49-3.05,6.24-1.4,9.33Z">
                                </path>
                                <path class="cls-2"
                                    d="M122.62,38.95c-13.79-13.79-32.1-21.39-51.54-21.39-1.47.01-2.62,1.1-2.62,2.47,0,1.36,1.11,2.46,2.46,2.46h.16c18.12,0,35.19,7.08,48.05,19.95,12.86,12.86,19.95,29.98,19.95,48.21,0,1.36,1.11,2.46,2.46,2.46s2.46-1.11,2.46-2.46c0-19.54-7.6-37.9-21.39-51.7Z">
                                </path>
                                <g>
                                    <path class="cls-2"
                                        d="M106.88,54.7c-9.58-9.58-22.35-14.86-35.95-14.86-1.36,0-2.46,1.11-2.46,2.46s1.11,2.46,2.46,2.46c12.29,0,23.82,4.76,32.47,13.41,8.65,8.65,13.41,20.18,13.41,32.47,0,1.36,1.11,2.46,2.46,2.46s2.46-1.11,2.46-2.46c0-13.6-5.28-26.37-14.85-35.95Z">
                                    </path>
                                    <path class="cls-2"
                                        d="M70.92,62.12c-1.36,0-2.46,1.11-2.46,2.46s1.11,2.46,2.46,2.46c6.35,0,12.29,2.44,16.72,6.88,4.43,4.44,6.88,10.37,6.88,16.72,0,1.36,1.11,2.46,2.46,2.46s2.46-1.11,2.46-2.46c0-7.67-2.96-14.84-8.32-20.21-5.37-5.37-12.54-8.32-20.21-8.32Z">
                                    </path>
                                </g>
                            </svg>
                        </div>
                    </div>
                    <div class="my-16">
                        <a href="{{ route('candidate.index') }}"
                            class="  bg-gray-600 text-white px-10 py-5 rounded-sm text-xl border-2 border-gray-600 uppercase hover:bg-transparent hover:border-gray-600 hover:text-gray-600">
                            En savoir plus
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recruteur -->
            <div class="col-span-1 flex flex-col items-center">
                <h3 class="text-primary text-4xl  italic font-title mb-4">Recruteur</h3>
                <div class=" w-28 border-t-[3px] border-primary my-4"></div>
                <div class="w-full items-center">
                    <!-- Step 1 -->
                    <div class="mb-8">
                        <div
                            class="mt-10 mb-8 text-gray-500 text-2xl font-bold  w-10 h-10 mx-auto flex items-center justify-center rounded-full border-[3px] border-primary">
                            1</div>
                        <p class="mt-2 text-2xl">Affichez les résultats de votre recherche</p>
                        <div class="mt-2">
                            <!-- Icon for step 1 -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-28 mx-auto" id="Layer_1"
                                data-name="Layer 1" viewBox="0 0 161.6 161.6">
                                <defs>
                                    <style>
                                        .cls-1 {
                                            fill: #15bbee;
                                        }

                                        .cls-1,
                                        .cls-2 {
                                            stroke-width: 0px;
                                        }

                                        .cls-2 {
                                            fill: #fff;
                                        }
                                    </style>
                                </defs>
                                <path class="cls-2"
                                    d="M52.6,65v-9.6c0-8.5-5.2-16.2-13.1-19.4,7-6.1,7.7-16.8,1.6-23.8-6.1-7-16.8-7.7-23.8-1.6-7,6.1-7.7,16.8-1.6,23.8.5.6,1,1.1,1.6,1.6-7.9,3.2-13.1,10.8-13.1,19.4v9.6c0,1.6,1.3,2.9,2.9,2.9h42.6c1.6,0,2.9-1.3,2.9-2.9ZM28.4,12.4c6.1,0,11.1,4.9,11.1,11,0,1.4-.2,2.7-.7,4-1.7,4.3-5.8,7.1-10.4,7.1h0c-6.1,0-11.1-5-11.1-11.1,0-6.1,5-11.1,11.1-11.1ZM46.9,62.2H9.9v-6.7c0-8.4,6.8-15.2,15.2-15.2h6.6c8.4,0,15.2,6.8,15.2,15.2v6.7Z">
                                </path>
                                <path class="cls-1"
                                    d="M106.3,39.1h0c-10.6,0-20.8,4.2-28.3,11.7-13.4,13.4-15.5,34.4-5.2,50.3,1.5,2.3,3.2,4.4,5.2,6.3.9.9,1.7,1.6,2.5,2.3h0c1.2,1,2.4,2,3.7,2.8,14.8,9.7,34.1,8.5,47.7-2.8,0,0,0,0,0,0h0c.8-.7,1.6-1.4,2.5-2.3,15.6-15.6,15.6-41,0-56.6-7.5-7.5-17.7-11.7-28.3-11.7ZM87.5,107.8c-.7-.4-1.3-.9-1.9-1.4,2.5-11.5,13.7-18.8,25.2-16.3,4,.9,7.7,2.9,10.6,5.8,2.9,2.9,4.9,6.5,5.7,10.5-11.6,8.8-27.5,9.4-39.6,1.4ZM106.3,83.9c-7.1,0-13-5.6-13.1-12.8,0-7.1,5.6-13,12.8-13.1,0,0,.2,0,.3,0,7.1,0,12.9,5.7,13,12.9,0,1.6-.3,3.2-.8,4.6-1.9,5-6.8,8.3-12.1,8.3ZM131.8,102.1c-2.5-7.1-7.8-12.9-14.7-15.9,8.4-6,10.4-17.6,4.5-26-6-8.4-17.6-10.4-26-4.5-8.4,6-10.4,17.6-4.5,26,1.2,1.7,2.8,3.3,4.5,4.5-6.9,3-12.2,8.8-14.7,15.8-1.2-1.3-2.2-2.6-3.2-4.1-8.9-13.6-7.1-31.6,4.4-43.1,13.4-13.4,35.1-13.5,48.5-.1,13,12.9,13.5,33.8,1.2,47.3Z">
                                </path>
                                <path class="cls-2"
                                    d="M157.2,76.6c-1.4-28.1-25.3-49.8-53.4-48.4-26.2,1.3-47.2,22.2-48.4,48.4,0,.8,0,1.6,0,2.3,0,6.5,1.2,12.9,3.6,19,1,2.6,2.3,5.1,3.8,7.6l-29.1,29.1c-4.7,4.8-4.7,12.5,0,17.3,4.8,4.8,12.5,4.8,17.2,0,0,0,0,0,0,0l29.1-29.1c20.1,12.1,45.8,9,62.4-7.6,4.9-4.9,8.8-10.8,11.3-17.2.8-1.9,1.4-3.9,2-6,1.1-4.4,1.7-8.9,1.6-13.4,0-.7,0-1.3,0-2ZM150.2,90.5c-.5,1.8-1.1,3.6-1.7,5.3-2.3,5.7-5.7,11-10.1,15.3-15.3,15.3-39.2,17.6-57.2,5.6-1.1-.8-2.6-.6-3.6.4l-30.7,30.7c-2.5,2.5-6.6,2.6-9.2,0,0,0,0,0,0,0-2.5-2.5-2.5-6.7,0-9.2l30.7-30.7c1-1,1.1-2.5.4-3.6-5-7.5-7.7-16.3-7.6-25.3,0-.7,0-1.3,0-2,1.2-25,22.4-44.2,47.4-43,23.3,1.1,41.9,19.8,43,43h0c0,.6,0,1.1,0,1.7,0,4-.4,8-1.4,11.9Z">
                                </path>
                            </svg>
                        </div>
                    </div>
                    <div class="w-full border-t border-primary my-4"></div>
                    <!-- Step 2 -->
                    <div class="mb-8">
                        <div
                            class="mt-10 mb-8 text-gray-500 text-2xl font-bold  w-10 h-10 mx-auto flex items-center justify-center rounded-full border-[3px] border-primary">
                            2</div>
                        <p class="mt-2 text-2xl">Choisissez votre plan à CHF 99.–</p>
                        <div class="mt-2">
                            <!-- Icon for step 2 -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-28 mx-auto" id="Layer_1"
                                data-name="Layer 1" viewBox="0 0 161.57 161.57">
                                <defs>
                                    <style>
                                        .cls-1 {
                                            fill: #706f6f;
                                        }

                                        .cls-1,
                                        .cls-2 {
                                            stroke-width: 0px;
                                        }

                                        .cls-2 {
                                            fill: #15bbee;
                                        }
                                    </style>
                                </defs>
                                <g>
                                    <path class="cls-1"
                                        d="M89.72,37.75c0,1.53-.93,2.6-2.33,2.67-1.46.08-2.44-.76-2.63-2.34-.19-1.6-1.01-2.67-2.53-3.17-1.63-.54-3.11-.25-4.36.92-.83.78-1.2,1.77-.81,2.93.46,1.4,1.49,2.14,2.88,2.39,1.13.2,2.31.22,3.42.51,3.53.92,6.15,4.02,6.35,7.38.21,3.58-1.89,6.79-5.41,8.2-.59.24-1,.47-1.2,1.18-.3,1.05-1.14,1.56-2.26,1.57-1.18,0-1.88-.6-2.4-1.62-.22-.44-.64-.89-1.08-1.09-2.85-1.24-4.7-3.28-5.33-6.35-.09-.44-.12-.9-.1-1.35.05-1.39,1.1-2.45,2.42-2.48,1.3-.04,2.43.86,2.49,2.23.12,2.42,2.31,4.07,5.22,3.39,1.6-.37,2.78-1.91,2.66-3.31-.14-1.63-1.64-2.89-3.5-3.11-1.34-.16-2.73-.3-3.97-.77-3.31-1.26-5.34-4.29-5.34-7.62,0-3.24,2.06-6.2,5.29-7.53.68-.28,1.08-.55,1.26-1.38.24-1.14,1.15-1.77,2.39-1.75,1.17.01,2.19.75,2.37,1.94.1.66.4.86.94,1.06,3.31,1.27,5.57,4.35,5.56,7.49Z">
                                    </path>
                                    <path class="cls-1"
                                        d="M55.03,91.69c2.23,0,4.46-.01,6.68,0,1.68.01,2.7,1,2.71,2.69.03,4.5.03,9,0,13.5-.01,1.68-1.05,2.63-2.76,2.64-4.46,0-8.91,0-13.37,0-1.76,0-2.72-.86-2.74-2.62-.05-4.5-.04-9,0-13.5.02-1.84.96-2.7,2.8-2.71,2.23-.01,4.46,0,6.68,0ZM59.3,105.52v-8.77h-8.67v8.77h8.67Z">
                                    </path>
                                    <path class="cls-1"
                                        d="M55.06,115.49c2.23,0,4.46-.01,6.68,0,1.61.01,2.66.98,2.67,2.6.03,4.54.03,9.08,0,13.62-.01,1.62-1.06,2.6-2.67,2.6-4.54.01-9.08.02-13.62,0-1.55,0-2.54-.86-2.56-2.41-.06-4.66-.06-9.33,0-13.99.02-1.55,1.01-2.4,2.56-2.42,2.31-.02,4.62,0,6.93,0ZM50.62,129.28h8.71v-8.77h-8.71v8.77Z">
                                    </path>
                                    <path class="cls-1"
                                        d="M95.05,67.9c6.15,0,12.3,0,18.44,0,1.27,0,2.25.72,2.48,1.81.25,1.16-.04,2.17-1.11,2.77-.44.25-1.01.35-1.53.35-12.17.02-24.34.02-36.51.01-1.62,0-2.67-.96-2.69-2.42-.03-1.51,1.04-2.52,2.72-2.52,6.07,0,12.13,0,18.2,0Z">
                                    </path>
                                    <path class="cls-1"
                                        d="M94.99,96.64c-5.98,0-11.97,0-17.95,0-1.83,0-2.9-.91-2.92-2.45-.01-1.55,1.07-2.5,2.87-2.5,12.05,0,24.1,0,36.14,0,1.83,0,2.9.91,2.91,2.45.01,1.56-1.05,2.49-2.86,2.5-6.07,0-12.13,0-18.2,0Z">
                                    </path>
                                    <path class="cls-1"
                                        d="M95.18,115.48c5.98,0,11.97,0,17.95,0,1.84,0,2.9.9,2.92,2.44.02,1.6-1.06,2.52-2.97,2.52-12.01,0-24.01,0-36.02,0-1.83,0-2.91-.9-2.93-2.44-.02-1.59,1.06-2.51,2.98-2.51,6.02,0,12.05,0,18.07,0Z">
                                    </path>
                                    <path class="cls-1"
                                        d="M89.84,83.76c-4.29,0-8.59,0-12.88,0-1.74,0-2.8-.92-2.83-2.42-.03-1.55,1.05-2.53,2.84-2.53,8.55,0,17.09,0,25.64,0,1.75,0,2.93,1.02,2.93,2.49,0,1.47-1.18,2.46-2.94,2.47-4.25,0-8.5,0-12.76,0Z">
                                    </path>
                                    <path class="cls-1"
                                        d="M89.84,107.55c-4.29,0-8.59,0-12.88,0-1.74,0-2.8-.92-2.83-2.42-.03-1.55,1.05-2.53,2.84-2.53,8.55,0,17.09,0,25.64,0,1.75,0,2.93,1.02,2.93,2.49,0,1.47-1.18,2.46-2.94,2.47-4.25,0-8.5,0-12.76,0Z">
                                    </path>
                                    <path class="cls-1"
                                        d="M89.85,126.39c4.25,0,8.5,0,12.76,0,1.75,0,2.93,1.02,2.93,2.48,0,1.47-1.18,2.47-2.94,2.47-8.55,0-17.09,0-25.64,0-1.73,0-2.8-.93-2.83-2.42-.03-1.56,1.05-2.52,2.84-2.53,4.29,0,8.59,0,12.88,0Z">
                                    </path>
                                    <path class="cls-2"
                                        d="M52.33,76.43c2.53-2.53,5.07-5.11,7.66-7.66,1.54-1.52,3.84-1.04,4.26.96.16.75-.15,1.88-.67,2.44-3.1,3.26-6.3,6.42-9.51,9.57-1.19,1.17-2.53,1.13-3.73-.02-1.34-1.29-2.65-2.6-3.94-3.94-1.09-1.13-1.13-2.67-.14-3.65,1.05-1.04,2.44-1.01,3.64.1.75.7,1.48,1.43,2.22,2.15.02.02.07.02.2.05Z">
                                    </path>
                                </g>
                                <path class="cls-1"
                                    d="M121.37,17.53H56.58c-.72,0-1.39.28-1.92.8l-21.45,21.45c-.51.51-.8,1.19-.8,1.92v94.55c0,4.3,3.5,7.79,7.79,7.79h81.16c4.3,0,7.79-3.5,7.79-7.79V25.33c0-4.3-3.5-7.79-7.79-7.79ZM56.76,22.96v18.92h-18.92l18.92-18.92ZM124.4,29.79v106.46c0,1.67-1.36,3.03-3.03,3.03H40.21c-1.67,0-3.03-1.36-3.03-3.03V46.65h21.97c1.32,0,2.38-1.07,2.38-2.38v-21.97h59.84c1.67,0,3.03,1.36,3.03,3.03v4.46Z">
                                </path>
                            </svg>
                        </div>
                    </div>
                    <div class="w-full border-t border-primary my-4"></div>
                    <!-- Step 3 -->
                    <div class="mb-8">
                        <div
                            class="mt-10 mb-8 text-gray-500 text-2xl font-bold  w-10 h-10 mx-auto flex items-center justify-center rounded-full border-[3px] border-primary">
                            3</div>
                        <p class="mt-2 text-2xl">Obtenez les profils complets et contactez directement les candidats
                        </p>
                        <div class="mt-2">
                            <!-- Icon for step 3 -->
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-28 mx-auto" id="Layer_1"
                                data-name="Layer 1" viewBox="0 0 161.57 161.57">
                                <defs>
                                    <style>
                                        .cls-1 {
                                            fill: #706f6f;
                                        }

                                        .cls-1,
                                        .cls-2 {
                                            stroke-width: 0px;
                                        }

                                        .cls-2 {
                                            fill: #15bbee;
                                        }
                                    </style>
                                </defs>
                                <path class="cls-1"
                                    d="M132.05,54.68c1.09,0,1.97-.88,1.97-1.97v-13.48c0-1.09-.88-1.97-1.97-1.97h-4.61v-12.89c0-3.68-3-6.68-6.68-6.68H29.52c-1.09,0-1.97.88-1.97,1.97v122.27c0,1.09.88,1.97,1.97,1.97h91.25c3.68,0,6.68-2.99,6.68-6.68v-12.89h4.61c1.09,0,1.97-.88,1.97-1.97v-13.48c0-1.09-.88-1.97-1.97-1.97h-4.61v-5.79h4.61c1.09,0,1.97-.88,1.97-1.97v-13.48c0-1.09-.88-1.97-1.97-1.97h-4.61v-5.79h4.61c1.09,0,1.97-.88,1.97-1.97v-13.48c0-1.09-.88-1.97-1.97-1.97h-4.61v-5.79h4.61ZM127.45,41.2h2.63v9.54h-2.63v-9.54ZM127.45,110.84h2.63v9.54h-2.63v-9.54ZM127.45,87.63h2.63v9.54h-2.63v-9.54ZM127.45,64.41h2.63v9.54h-2.63v-9.54ZM31.49,21.62h4.86v118.34h-4.86V21.62ZM120.77,139.96H40.28V21.62h80.48c1.51,0,2.74,1.23,2.74,2.74v14.84s0,.02,0,.02v13.48s0,.02,0,.02v9.69s0,.02,0,.02v13.48s0,.02,0,.02v9.69s0,.02,0,.02v13.48s0,.02,0,.02v9.69s0,.02,0,.02v13.48s0,.02,0,.02v14.84c0,1.51-1.23,2.74-2.74,2.74Z">
                                </path>
                                <path class="cls-2"
                                    d="M115.54,95.63h0s-.09-.04-.14-.06l-13.1-5.5c-.06-.03-.12-.05-.18-.07,0,0,0,0,0,0-1.17-.4-2.48-.1-3.35.77l-2.7,2.7c-.62.63-1.61.77-2.37.36-5.27-2.84-10.06-6.4-14.25-10.59-4.19-4.19-7.76-8.99-10.59-14.25-.41-.77-.26-1.74.37-2.37l2.7-2.71c.88-.88,1.18-2.21.76-3.38v-.02c-.02-.05-.04-.1-.06-.15l-5.51-13.13s-.03-.08-.05-.12h0c-.36-.81-1.05-1.44-1.89-1.74-.84-.29-1.78-.23-2.58.17h0s-.06.04-.09.05l-13.07,7.32c-1.45.81-2.51,2.2-2.91,3.81-.4,1.6-.12,3.31.77,4.69,6.89,10.73,15,20.78,24.08,29.87,9.07,9.07,19.12,17.18,29.87,24.08.97.62,2.09.94,3.23.94.49,0,.98-.06,1.45-.18,1.61-.4,3-1.46,3.82-2.91l7.32-13.07h0s.03-.05.04-.07c0-.01.01-.02.02-.03.41-.79.47-1.73.17-2.58-.3-.84-.93-1.53-1.74-1.89ZM106.29,111.33c-.29.51-.76.87-1.33,1.01-.55.14-1.12.05-1.6-.25-10.51-6.75-20.34-14.68-29.21-23.55-8.89-8.89-16.81-18.72-23.55-29.21-.31-.48-.4-1.05-.26-1.61.14-.57.5-1.04,1.01-1.33l12.39-6.94,5.05,12.03-2.37,2.37c-1.87,1.87-2.3,4.69-1.06,7.02,3.02,5.6,6.81,10.71,11.28,15.17,4.45,4.45,9.56,8.25,15.17,11.27,2.29,1.22,5.19.79,7.02-1.05l2.37-2.37,12.02,5.04-6.94,12.39Z">
                                </path>
                            </svg>
                        </div>
                    </div>
                    <div class="my-16">
                        <a href="{{ route('recruiters') }}"
                            class="  bg-gray-600 text-white px-10 py-5 rounded-sm text-xl border-2 border-gray-600 uppercase hover:bg-transparent hover:border-gray-600 hover:text-gray-600">
                            En savoir plus
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- New Section: S'inscrire -->
    <section class="my-20 px-10 text-center container mx-auto">
        <!-- Section: S'inscrire -->
        <div>
            <h2 class="text-3xl font-title mb-4">S'INSCRIRE</h2>
            <div class="w-28 border-t-[3px] border-primary mx-auto mb-6"></div>
            <div class="grid grid-cols-1 md:grid-cols-2 md:gap-12 pt-5">
                <!-- Candidat -->
                <button onclick="window.location.href='{{ route('candidate.registerIndex') }}'"
                    class="bg-gray-700 text-white py-4 px-8 flex items-center justify-center hover:bg-primary text-2xl rounded-3xl mb-5">
                    <svg xmlns="http://www.w3.org/2000/svg" id="Layer_1" data-name="Layer 1" class="w-28 mx-5"
                        viewBox="0 0 161.57 161.57">
                        <path
                            d="M131.94,14.92c-10.84,0-19.66,8.82-19.66,19.66s8.82,19.66,19.66,19.66,19.66-8.82,19.66-19.66-8.82-19.66-19.66-19.66ZM131.94,17.87c9.25,0,16.71,7.47,16.71,16.71s-7.47,16.71-16.71,16.71-16.71-7.47-16.71-16.71,7.47-16.71,16.71-16.71ZM131.91,24.73c-.81.01-1.46.68-1.45,1.5v6.88h-6.88c-.81-.01-1.48.64-1.5,1.45-.01.81.64,1.48,1.45,1.5.01,0,.03,0,.04,0h6.88v6.88c-.01.81.64,1.48,1.45,1.5.81.01,1.48-.64,1.5-1.45,0-.01,0-.03,0-.04v-6.88h6.88c.81.01,1.48-.64,1.5-1.45.01-.81-.64-1.48-1.45-1.5-.01,0-.03,0-.04,0h-6.88v-6.88c.01-.81-.64-1.48-1.45-1.5-.01,0-.03,0-.04,0Z"
                            style="fill: #fff; stroke-width: 0px;"></path>
                        <g>
                            <path
                                d="M69.73,56.46c-1.22,0-2.21.99-2.21,2.21v2.21c0,1.22.99,2.21,2.21,2.21s2.21-.99,2.21-2.21v-2.21c0-1.22-.99-2.21-2.21-2.21Z"
                                style="fill: #fff; stroke-width: 0px;"></path>
                            <path
                                d="M91.85,63.09c1.22,0,2.21-.99,2.21-2.21v-2.21c0-1.22-.99-2.21-2.21-2.21s-2.21.99-2.21,2.21v2.21c0,1.22.99,2.21,2.21,2.21Z"
                                style="fill: #fff; stroke-width: 0px;"></path>
                            <path
                                d="M111.7,107l-16.36-3.85-3.5-4.67h0,0l-2.21-2.95v-3.02c10.29-3.66,17.69-13.46,17.69-24.99v-26.54c0-14.63-11.91-26.54-26.54-26.54s-26.54,11.91-26.54,26.54v26.54c0,.76.04,1.5.1,2.24.01.16.04.32.06.47.06.59.13,1.17.23,1.74.03.19.08.38.12.57.1.53.21,1.06.35,1.58.06.24.13.47.2.7.13.45.27.91.42,1.35.1.3.22.59.33.88.14.35.28.7.43,1.05.16.37.33.73.51,1.09.11.23.23.46.35.68.24.45.48.89.74,1.32.05.09.11.17.16.25.63,1.03,1.32,2.02,2.09,2.96.03.04.08.05.11.09,3.02,3.61,6.99,6.41,11.51,8.01v3.02l-2.21,2.95h0,0l-3.5,4.67-16.36,3.85c-13.05,3.07-22.17,14.58-22.17,27.99v9.94c0,1.22.99,2.21,2.21,2.21h101.74c1.22,0,2.21-.99,2.21-2.21v-9.94c0-13.41-9.12-24.92-22.17-27.99ZM80.79,108.57l3.04,3.65-3.04,3.04-3.04-3.04,3.04-3.65ZM71.12,113.27l-1.26-7.58,4.3-5.73,3.82,5.09-6.85,8.22ZM83.61,105.04l3.82-5.09,4.3,5.73-1.26,7.58-6.85-8.22ZM62.46,79.88s-.02-.03-.03-.05c-.36-.53-.69-1.08-1-1.65-.1-.18-.19-.38-.29-.56-.21-.4-.4-.8-.58-1.21-.12-.27-.22-.54-.33-.81-.14-.35-.27-.7-.39-1.06-.1-.31-.2-.61-.29-.93-.1-.35-.19-.7-.27-1.05-.07-.31-.15-.63-.21-.94-.08-.4-.13-.81-.19-1.22-.04-.27-.08-.54-.11-.81-.06-.69-.11-1.38-.11-2.08v-13.27h20.66c2.95,0,5.73-1.15,7.82-3.24l4.04-4.04c.86-.86.86-2.26,0-3.13s-2.26-.86-3.13,0l-4.04,4.04c-1.24,1.23-2.94,1.94-4.69,1.94h-20.66v-8.85c0-12.2,9.92-22.12,22.12-22.12s22.12,9.92,22.12,22.12v8.85h-2.97c-.96,0-1.88-.2-2.75-.6-1.12-.51-2.42-.02-2.93,1.09-.51,1.11-.02,2.42,1.09,2.93,1.45.66,2.99.99,4.58.99h2.97v13.27c0,12.2-9.92,22.12-22.12,22.12-7.62,0-14.35-3.87-18.33-9.75ZM76.36,95.53v-1.87c1.31.22,2.65.36,4.02.38h0c.13,0,.27.02.4.02s.27-.02.4-.02h0c1.37-.03,2.71-.16,4.02-.38v1.87l-1.4,1.87h0l-3.02,4.03-3.02-4.03h0l-1.4-1.87ZM72.23,142.72h-22.41v-6.64c0-1.22-.99-2.21-2.21-2.21s-2.21.99-2.21,2.21v6.64h-13.27v-7.73c0-11.35,7.71-21.08,18.76-23.68l14.84-3.49,1.82,10.94c.14.85.77,1.54,1.6,1.77.19.05.39.08.58.08.65,0,1.27-.28,1.7-.8l3.48-4.17,1.05,1.05-3.72,26.04ZM76.7,142.72l3.19-22.32c.29.13.59.21.9.21s.61-.08.9-.21l3.19,22.32h-8.17ZM129.45,142.72h-13.27v-6.64c0-1.22-.99-2.21-2.21-2.21s-2.21.99-2.21,2.21v6.64h-22.41l-3.72-26.04,1.05-1.05,3.48,4.17c.43.51,1.05.8,1.7.8.19,0,.39-.02.58-.08.83-.23,1.46-.92,1.6-1.77l1.82-10.94,14.84,3.49c11.04,2.6,18.76,12.34,18.76,23.68v7.73Z"
                                style="fill: #fff; stroke-width: 0px;"></path>
                            <path
                                d="M88.65,80.42c1.02-.68,1.29-2.05.61-3.07-.51-.76-1.41-1.11-2.25-.95-.28.05-.56.16-.81.33-3.69,2.46-7.13,2.46-10.82,0-.25-.17-.53-.28-.81-.33-.85-.16-1.75.19-2.25.95-.68,1.02-.4,2.39.61,3.07,2.57,1.71,5.22,2.57,7.86,2.57s5.29-.86,7.86-2.57Z"
                                style="fill: #fff; stroke-width: 0px;"></path>
                        </g>
                    </svg>
                    Candidat
                </button>

                <!-- Recruteur -->
                <button onclick="window.location.href='{{ route('register') }}'"
                    class="bg-gray-700 text-white py-4 px-8 flex items-center justify-center hover:bg-primary text-2xl rounded-3xl mb-5">
                    <svg xmlns="http://www.w3.org/2000/svg" id="Layer_1" data-name="Layer 1" class="w-28 mx-5"
                        viewBox="0 0 161.57 161.57">
                        <g>
                            <path
                                d="M109.35,107.19l-5.78-1.28c2.33-9.52,2.3-19.16-.11-28.67,1.19-3.01,1.87-6.28,1.87-9.71h2.21c6.1,0,11.06-4.96,11.06-11.06v-4.42c0-3.6-1.75-6.77-4.42-8.79v-2.27c0-19.51-15.88-35.39-35.39-35.39s-35.39,15.88-35.39,35.39v2.27c-2.67,2.02-4.42,5.19-4.42,8.79v4.42c0,3.6,1.76,6.78,4.43,8.8,0,.02,0,.03,0,.05v13.27c0,8.54,6.95,15.48,15.48,15.48h2.62c.92,2.57,3.35,4.42,6.23,4.42h2.21v.89c-1.49.83-4.01,2.36-6.48,4.43l-15.24,3.38c-13.26,2.95-22.52,14.49-22.52,28.07v9.67c0,1.22.99,2.21,2.21,2.21h101.74c1.22,0,2.21-.99,2.21-2.21v-9.67c0-13.58-9.26-25.12-22.52-28.07ZM114.17,52.04v4.42c0,3.66-2.98,6.64-6.64,6.64h-2.21v-17.69h2.21c3.66,0,6.64,2.98,6.64,6.64ZM47.82,40.98c0-17.07,13.89-30.96,30.96-30.96s30.96,13.89,30.96,30.96v.22c-.71-.15-1.45-.22-2.21-.22h-2.21c0-14.63-11.91-26.54-26.54-26.54s-26.54,11.91-26.54,26.54h-2.21c-.76,0-1.5.08-2.21.22v-.22ZM100.9,40.98v8.76c-10.18-.45-20.15-3.97-28.31-10.08l-3.54-2.65c-.57-.43-1.31-.55-1.99-.34-.68.21-1.21.74-1.44,1.41l-1.63,4.88c-1.13,3.4-3.95,5.84-7.33,6.61v-8.57c0-12.2,9.92-22.12,22.12-22.12s22.12,9.92,22.12,22.12ZM43.4,56.46v-4.42c0-3.66,2.98-6.64,6.64-6.64h2.21v17.69h-2.21c-3.66,0-6.64-2.98-6.64-6.64ZM61.5,89.63h-2.62c-6.1,0-11.06-4.96-11.06-11.06v-11.28c.71.15,1.45.22,2.21.22h2.21c0,6.15,2.16,12.15,6.07,16.89.44.53,1.07.8,1.71.8.5,0,.99-.17,1.41-.51.94-.78,1.07-2.17.3-3.11-3.26-3.95-5.06-8.95-5.06-14.07v-13.45c5.31-.82,9.79-4.52,11.53-9.73l.65-1.96,1.08.81c8.92,6.69,19.84,10.51,30.96,10.97v13.36c0,12.2-9.92,22.12-22.12,22.12-.14,0-.27-.02-.41-.02-.92-2.56-3.35-4.4-6.22-4.4h-4.42c-2.88,0-5.31,1.85-6.23,4.42ZM99.26,104.94l-5.14-1.14c-2.47-2.07-4.99-3.6-6.48-4.43v-6.86c5.06-1.8,9.41-5.08,12.53-9.33,1.17,7.24.87,14.54-.91,21.76ZM74.36,98.07c1.88-.67,3.36-2.15,4.02-4.03.13,0,.27.02.4.02,1.51,0,2.98-.16,4.42-.4l-.66,20.29c-1.35,1.46-2.61,2.23-3.76,2.23h-.02c-1.14,0-2.4-.78-3.74-2.23l-.66-15.87ZM75.32,119.73c1.12.56,2.26.86,3.42.87h.05c1.18,0,2.33-.3,3.47-.87l-1.15,22.99h-4.64l-1.15-22.99ZM67.73,94.06c-1.22,0-2.21-.99-2.21-2.21s.99-2.21,2.21-2.21h4.42c1.22,0,2.21.99,2.21,2.21s-.99,2.21-2.21,2.21h-4.42ZM47.82,142.72v-6.64c0-1.22-.99-2.21-2.21-2.21s-2.21.99-2.21,2.21v6.64h-13.27v-7.46c0-11.49,7.83-21.26,19.05-23.75l9.2-2.04c-.78,1.28-1.38,2.64-1.67,4.07-.14.73.08,1.48.6,2l3.3,3.3-1.5,3c-.2.39-.27.83-.22,1.27.93,7.42,2.65,13.74,5.3,19.63h-16.38ZM69.08,142.72c-2.89-5.82-4.71-12.07-5.71-19.52l1.91-3.82c.43-.85.26-1.88-.41-2.55l-3.5-3.5c1.33-3.49,5.51-6.81,8.76-8.9l1.91,38.3h-2.96ZM88.49,142.72h-2.96l1.91-38.3c3.25,2.09,7.43,5.41,8.76,8.9l-3.5,3.5c-.67.67-.84,1.7-.41,2.55l1.91,3.82c-1,7.45-2.82,13.7-5.71,19.52ZM127.45,142.72h-13.27v-6.64c0-1.22-.99-2.21-2.21-2.21s-2.21.99-2.21,2.21v6.64h-16.38c2.66-5.89,4.37-12.21,5.3-19.63.06-.43-.02-.87-.22-1.27l-1.5-3,3.3-3.3c.52-.52.75-1.27.6-2-.29-1.43-.89-2.79-1.67-4.07l9.2,2.04c11.22,2.49,19.05,12.26,19.05,23.75v7.46Z"
                                style="fill: #fff; stroke-width: 0px;"></path>
                            <path
                                d="M69.94,60.88v-2.21c0-1.22-.99-2.21-2.21-2.21s-2.21.99-2.21,2.21v2.21c0,1.22.99,2.21,2.21,2.21s2.21-.99,2.21-2.21Z"
                                style="fill: #fff; stroke-width: 0px;"></path>
                            <path
                                d="M89.85,63.09c1.22,0,2.21-.99,2.21-2.21v-2.21c0-1.22-.99-2.21-2.21-2.21s-2.21.99-2.21,2.21v2.21c0,1.22.99,2.21,2.21,2.21Z"
                                style="fill: #fff; stroke-width: 0px;"></path>
                            <path
                                d="M78.79,82.99c2.64,0,5.29-.86,7.86-2.57,1.02-.68,1.29-2.05.61-3.07-.68-1.02-2.05-1.29-3.07-.61-3.69,2.46-7.13,2.46-10.82,0-1.02-.68-2.39-.4-3.07.61s-.4,2.39.61,3.07c2.57,1.71,5.22,2.57,7.86,2.57Z"
                                style="fill: #fff; stroke-width: 0px;"></path>
                        </g>
                        <path
                            d="M139.87,14.92c-10.84,0-19.66,8.82-19.66,19.66s8.82,19.66,19.66,19.66,19.66-8.82,19.66-19.66-8.82-19.66-19.66-19.66ZM139.87,17.87c9.25,0,16.71,7.47,16.71,16.71s-7.47,16.71-16.71,16.71-16.71-7.47-16.71-16.71,7.47-16.71,16.71-16.71ZM139.85,24.73c-.81.01-1.46.68-1.45,1.5v6.88h-6.88c-.81-.01-1.48.64-1.5,1.45-.01.81.64,1.48,1.45,1.5.01,0,.03,0,.04,0h6.88v6.88c-.01.81.64,1.48,1.45,1.5.81.01,1.48-.64,1.5-1.45,0-.01,0-.03,0-.04v-6.88h6.88c.81.01,1.48-.64,1.5-1.45.01-.81-.64-1.48-1.45-1.5-.01,0-.03,0-.04,0h-6.88v-6.88c.01-.81-.64-1.48-1.45-1.5-.01,0-.03,0-.04,0Z"
                            style="fill: #fff; stroke-width: 0px;"></path>
                    </svg>
                    Recruteur
                </button>
            </div>
        </div>

        <!-- Divider -->
        <div class="mb-12 mt-28">
            <h2 class="text-3xl font-title mb-4">SE CONNECTER À MON COMPTE</h2>
            <div class="w-28 border-t-[3px] border-primary mx-auto mb-6"></div>
        </div>

        <!-- Section: Se connecter -->
        <div class="grid grid-cols-1 md:grid-cols-2 md:gap-12">
            <!-- Candidat -->
            <button onclick="window.location.href='{{ route('login') }}'"
                class="bg-gray-700 text-white py-4 px-8 flex items-center justify-center hover:bg-primary text-2xl rounded-3xl mb-5">
                <svg xmlns="http://www.w3.org/2000/svg" id="Layer_1" data-name="Layer 1" class="w-28 mx-5"
                    viewBox="0 0 161.57 161.57">
                    <path
                        d="M127.85,14.53c-3.31,0-6.03,2.72-6.03,6.03v6.58c-.01.91.71,1.66,1.62,1.67.91.01,1.66-.71,1.67-1.62,0-.02,0-.03,0-.05v-6.58c0-1.53,1.21-2.74,2.74-2.74h19.74c1.53,0,2.74,1.21,2.74,2.74v27.42c0,1.53-1.21,2.74-2.74,2.74h-19.74c-1.53,0-2.74-1.21-2.74-2.74v-6.56c.01-.91-.71-1.66-1.62-1.67-.91-.01-1.66.71-1.67,1.62,0,.02,0,.03,0,.05v6.56c0,3.31,2.72,6.03,6.03,6.03h19.74c3.31,0,6.03-2.72,6.03-6.03v-27.42c0-3.31-2.72-6.03-6.03-6.03h-19.74ZM134.41,26.05c-.91,0-1.65.74-1.65,1.65,0,.44.18.87.5,1.18l3.77,3.77-21.25-.02c-.91-.01-1.66.71-1.67,1.62-.01.91.71,1.66,1.62,1.67.02,0,.03,0,.05,0l21.26.02-3.77,3.77c-.66.63-.68,1.67-.05,2.33s1.67.68,2.33.05c.02-.02.03-.03.05-.05l6.58-6.58c.64-.64.64-1.68,0-2.33l-6.58-6.58c-.31-.32-.74-.5-1.18-.5Z"
                        style="fill: #fff; stroke-width: 0px;"></path>
                    <g>
                        <path
                            d="M69.73,56.46c-1.22,0-2.21.99-2.21,2.21v2.21c0,1.22.99,2.21,2.21,2.21s2.21-.99,2.21-2.21v-2.21c0-1.22-.99-2.21-2.21-2.21Z"
                            style="fill: #fff; stroke-width: 0px;"></path>
                        <path
                            d="M91.85,63.09c1.22,0,2.21-.99,2.21-2.21v-2.21c0-1.22-.99-2.21-2.21-2.21s-2.21.99-2.21,2.21v2.21c0,1.22.99,2.21,2.21,2.21Z"
                            style="fill: #fff; stroke-width: 0px;"></path>
                        <path
                            d="M111.7,107l-16.36-3.85-3.5-4.67h0,0l-2.21-2.95v-3.02c10.29-3.66,17.69-13.46,17.69-24.99v-26.54c0-14.63-11.91-26.54-26.54-26.54s-26.54,11.91-26.54,26.54v26.54c0,.76.04,1.5.1,2.24.01.16.04.32.06.47.06.59.13,1.17.23,1.74.03.19.08.38.12.57.1.53.21,1.06.35,1.58.06.24.13.47.2.7.13.45.27.91.42,1.35.1.3.22.59.33.88.14.35.28.7.43,1.05.16.37.33.73.51,1.09.11.23.23.46.35.68.24.45.48.89.74,1.32.05.09.11.17.16.25.63,1.03,1.32,2.02,2.09,2.96.03.04.08.05.11.09,3.02,3.61,6.99,6.41,11.51,8.01v3.02l-2.21,2.95h0,0l-3.5,4.67-16.36,3.85c-13.05,3.07-22.17,14.58-22.17,27.99v9.94c0,1.22.99,2.21,2.21,2.21h101.74c1.22,0,2.21-.99,2.21-2.21v-9.94c0-13.41-9.12-24.92-22.17-27.99ZM80.79,108.57l3.04,3.65-3.04,3.04-3.04-3.04,3.04-3.65ZM71.12,113.27l-1.26-7.58,4.3-5.73,3.82,5.09-6.85,8.22ZM83.61,105.04l3.82-5.09,4.3,5.73-1.26,7.58-6.85-8.22ZM62.46,79.88s-.02-.03-.03-.05c-.36-.53-.69-1.08-1-1.65-.1-.18-.19-.38-.29-.56-.21-.4-.4-.8-.58-1.21-.12-.27-.22-.54-.33-.81-.14-.35-.27-.7-.39-1.06-.1-.31-.2-.61-.29-.93-.1-.35-.19-.7-.27-1.05-.07-.31-.15-.63-.21-.94-.08-.4-.13-.81-.19-1.22-.04-.27-.08-.54-.11-.81-.06-.69-.11-1.38-.11-2.08v-13.27h20.66c2.95,0,5.73-1.15,7.82-3.24l4.04-4.04c.86-.86.86-2.26,0-3.13s-2.26-.86-3.13,0l-4.04,4.04c-1.24,1.23-2.94,1.94-4.69,1.94h-20.66v-8.85c0-12.2,9.92-22.12,22.12-22.12s22.12,9.92,22.12,22.12v8.85h-2.97c-.96,0-1.88-.2-2.75-.6-1.12-.51-2.42-.02-2.93,1.09-.51,1.11-.02,2.42,1.09,2.93,1.45.66,2.99.99,4.58.99h2.97v13.27c0,12.2-9.92,22.12-22.12,22.12-7.62,0-14.35-3.87-18.33-9.75ZM76.36,95.53v-1.87c1.31.22,2.65.36,4.02.38h0c.13,0,.27.02.4.02s.27-.02.4-.02h0c1.37-.03,2.71-.16,4.02-.38v1.87l-1.4,1.87h0l-3.02,4.03-3.02-4.03h0l-1.4-1.87ZM72.23,142.72h-22.41v-6.64c0-1.22-.99-2.21-2.21-2.21s-2.21.99-2.21,2.21v6.64h-13.27v-7.73c0-11.35,7.71-21.08,18.76-23.68l14.84-3.49,1.82,10.94c.14.85.77,1.54,1.6,1.77.19.05.39.08.58.08.65,0,1.27-.28,1.7-.8l3.48-4.17,1.05,1.05-3.72,26.04ZM76.7,142.72l3.19-22.32c.29.13.59.21.9.21s.61-.08.9-.21l3.19,22.32h-8.17ZM129.45,142.72h-13.27v-6.64c0-1.22-.99-2.21-2.21-2.21s-2.21.99-2.21,2.21v6.64h-22.41l-3.72-26.04,1.05-1.05,3.48,4.17c.43.51,1.05.8,1.7.8.19,0,.39-.02.58-.08.83-.23,1.46-.92,1.6-1.77l1.82-10.94,14.84,3.49c11.04,2.6,18.76,12.34,18.76,23.68v7.73Z"
                            style="fill: #fff; stroke-width: 0px;"></path>
                        <path
                            d="M88.65,80.42c1.02-.68,1.29-2.05.61-3.07-.51-.76-1.41-1.11-2.25-.95-.28.05-.56.16-.81.33-3.69,2.46-7.13,2.46-10.82,0-.25-.17-.53-.28-.81-.33-.85-.16-1.75.19-2.25.95-.68,1.02-.4,2.39.61,3.07,2.57,1.71,5.22,2.57,7.86,2.57s5.29-.86,7.86-2.57Z"
                            style="fill: #fff; stroke-width: 0px;"></path>
                    </g>
                </svg>
                Candidat
            </button>

            <!-- Recruteur -->
            <button onclick="window.location.href='{{ route('login') }}'"
                class="bg-gray-700 text-white py-4 px-8 flex items-center justify-center hover:bg-primary text-2xl rounded-3xl mb-5">
                <svg xmlns="http://www.w3.org/2000/svg" id="Layer_1" data-name="Layer 1" class="w-28 mx-5"
                    viewBox="0 0 161.57 161.57">
                    <g>
                        <path
                            d="M107.35,107.19l-5.78-1.28c2.33-9.52,2.3-19.16-.11-28.67,1.19-3.01,1.87-6.28,1.87-9.71h2.21c6.1,0,11.06-4.96,11.06-11.06v-4.42c0-3.6-1.75-6.77-4.42-8.79v-2.27c0-19.51-15.88-35.39-35.39-35.39s-35.39,15.88-35.39,35.39v2.27c-2.67,2.02-4.42,5.19-4.42,8.79v4.42c0,3.6,1.76,6.78,4.43,8.8,0,.02,0,.03,0,.05v13.27c0,8.54,6.95,15.48,15.48,15.48h2.62c.92,2.57,3.35,4.42,6.23,4.42h2.21v.89c-1.49.83-4.01,2.36-6.48,4.43l-15.24,3.38c-13.26,2.95-22.52,14.49-22.52,28.07v9.67c0,1.22.99,2.21,2.21,2.21h101.74c1.22,0,2.21-.99,2.21-2.21v-9.67c0-13.58-9.26-25.12-22.52-28.07ZM114.17,52.04v4.42c0,3.66-2.98,6.64-6.64,6.64h-2.21v-17.69h2.21c3.66,0,6.64,2.98,6.64,6.64ZM47.82,40.98c0-17.07,13.89-30.96,30.96-30.96s30.96,13.89,30.96,30.96v.22c-.71-.15-1.45-.22-2.21-.22h-2.21c0-14.63-11.91-26.54-26.54-26.54s-26.54,11.91-26.54,26.54h-2.21c-.76,0-1.5.08-2.21.22v-.22ZM100.9,40.98v8.76c-10.18-.45-20.15-3.97-28.31-10.08l-3.54-2.65c-.57-.43-1.31-.55-1.99-.34-.68.21-1.21.74-1.44,1.41l-1.63,4.88c-1.13,3.4-3.95,5.84-7.33,6.61v-8.57c0-12.2,9.92-22.12,22.12-22.12s22.12,9.92,22.12,22.12ZM43.4,56.46v-4.42c0-3.66,2.98-6.64,6.64-6.64h2.21v17.69h-2.21c-3.66,0-6.64-2.98-6.64-6.64ZM61.5,89.63h-2.62c-6.1,0-11.06-4.96-11.06-11.06v-11.28c.71.15,1.45.22,2.21.22h2.21c0,6.15,2.16,12.15,6.07,16.89.44.53,1.07.8,1.71.8.5,0,.99-.17,1.41-.51.94-.78,1.07-2.17.3-3.11-3.26-3.95-5.06-8.95-5.06-14.07v-13.45c5.31-.82,9.79-4.52,11.53-9.73l.65-1.96,1.08.81c8.92,6.69,19.84,10.51,30.96,10.97v13.36c0,12.2-9.92,22.12-22.12,22.12-.14,0-.27-.02-.41-.02-.92-2.56-3.35-4.4-6.22-4.4h-4.42c-2.88,0-5.31,1.85-6.23,4.42ZM99.26,104.94l-5.14-1.14c-2.47-2.07-4.99-3.6-6.48-4.43v-6.86c5.06-1.8,9.41-5.08,12.53-9.33,1.17,7.24.87,14.54-.91,21.76ZM74.36,98.07c1.88-.67,3.36-2.15,4.02-4.03.13,0,.27.02.4.02,1.51,0,2.98-.16,4.42-.4l-.66,20.29c-1.35,1.46-2.61,2.23-3.76,2.23h-.02c-1.14,0-2.4-.78-3.74-2.23l-.66-15.87ZM75.32,119.73c1.12.56,2.26.86,3.42.87h.05c1.18,0,2.33-.3,3.47-.87l-1.15,22.99h-4.64l-1.15-22.99ZM67.73,94.06c-1.22,0-2.21-.99-2.21-2.21s.99-2.21,2.21-2.21h4.42c1.22,0,2.21.99,2.21,2.21s-.99,2.21-2.21,2.21h-4.42ZM47.82,142.72v-6.64c0-1.22-.99-2.21-2.21-2.21s-2.21.99-2.21,2.21v6.64h-13.27v-7.46c0-11.49,7.83-21.26,19.05-23.75l9.2-2.04c-.78,1.28-1.38,2.64-1.67,4.07-.14.73.08,1.48.6,2l3.3,3.3-1.5,3c-.2.39-.27.83-.22,1.27.93,7.42,2.65,13.74,5.3,19.63h-16.38ZM69.08,142.72c-2.89-5.82-4.71-12.07-5.71-19.52l1.91-3.82c.43-.85.26-1.88-.41-2.55l-3.5-3.5c1.33-3.49,5.51-6.81,8.76-8.9l1.91,38.3h-2.96ZM88.49,142.72h-2.96l1.91-38.3c3.25,2.09,7.43,5.41,8.76,8.9l-3.5,3.5c-.67.67-.84,1.7-.41,2.55l1.91,3.82c-1,7.45-2.82,13.7-5.71,19.52ZM127.45,142.72h-13.27v-6.64c0-1.22-.99-2.21-2.21-2.21s-2.21.99-2.21,2.21v6.64h-16.38c2.66-5.89,4.37-12.21,5.3-19.63.06-.43-.02-.87-.22-1.27l-1.5-3,3.3-3.3c.52-.52.75-1.27.6-2-.29-1.43-.89-2.79-1.67-4.07l9.2,2.04c11.22,2.49,19.05,12.26,19.05,23.75v7.46Z"
                            style="fill: #fff; stroke-width: 0px;"></path>
                        <path
                            d="M67.94,60.88v-2.21c0-1.22-.99-2.21-2.21-2.21s-2.21.99-2.21,2.21v2.21c0,1.22.99,2.21,2.21,2.21s2.21-.99,2.21-2.21Z"
                            style="fill: #fff; stroke-width: 0px;"></path>
                        <path
                            d="M89.85,63.09c1.22,0,2.21-.99,2.21-2.21v-2.21c0-1.22-.99-2.21-2.21-2.21s-2.21.99-2.21,2.21v2.21c0,1.22.99,2.21,2.21,2.21Z"
                            style="fill: #fff; stroke-width: 0px;"></path>
                        <path
                            d="M78.79,82.99c2.64,0,5.29-.86,7.86-2.57,1.02-.68,1.29-2.05.61-3.07-.68-1.02-2.05-1.29-3.07-.61-3.69,2.46-7.13,2.46-10.82,0-1.02-.68-2.39-.4-3.07.61s-.4,2.39.61,3.07c2.57,1.71,5.22,2.57,7.86,2.57Z"
                            style="fill: #fff; stroke-width: 0px;"></path>
                    </g>
                    <path
                        d="M139.87,14.92c-10.84,0-19.66,8.82-19.66,19.66s8.82,19.66,19.66,19.66,19.66-8.82,19.66-19.66-8.82-19.66-19.66-19.66ZM139.87,17.87c9.25,0,16.71,7.47,16.71,16.71s-7.47,16.71-16.71,16.71-16.71-7.47-16.71-16.71,7.47-16.71,16.71-16.71ZM139.85,24.73c-.81.01-1.46.68-1.45,1.5v6.88h-6.88c-.81-.01-1.48.64-1.5,1.45-.01.81.64,1.48,1.45,1.5.01,0,.03,0,.04,0h6.88v6.88c-.01.81.64,1.48,1.45,1.5.81.01,1.48-.64,1.5-1.45,0-.01,0-.03,0-.04v-6.88h6.88c.81.01,1.48-.64,1.5-1.45.01-.81-.64-1.48-1.45-1.5-.01,0-.03,0-.04,0h-6.88v-6.88c.01-.81-.64-1.48-1.45-1.5-.01,0-.03,0-.04,0Z"
                        style="fill: #fff; stroke-width: 0px;"></path>
                </svg>
                Recruteur
            </button>
        </div>
    </section>
    @php
        $cartBG = asset('images/carte-BG.jpg');
    @endphp
    <section class="py-20 bg-gray-100 text-center"
        style="background-image: url('{{ $cartBG }}'); background-size: cover;">
        <h2 class="text-2xl md:text-3xl mb-2">LES CHIFFRES PARLENT D'EUX MÊMES</h2>
        <p class="text-gray-500 mb-10">La Suisse affiche le taux de population active le plus élevé d'Europe, soit
            environ 67,1% (sources: OFS)</p>
        <div
            class="grid grid-cols-1 md:grid-cols-4 gap-0 py-6 px-14 md:px-0 w-4/5 mx-auto bg-white shadow-xl shadow-gray-100 rounded-3xl">
            <!-- Population active -->
            <div class="bg-white p-6">
                <p class="text-5xl font-title font-medium text-gray-700">67%</p>
                <p class="text-gray-500 mt-2">Population active</p>
            </div>
            <!-- Retraités actifs -->
            <div class="bg-white p-6 md:border-l md:border-gray-300">
                <p class="text-5xl font-title font-medium text-gray-700">32%</p>
                <p class="text-gray-500 mt-2">Retraités actifs</p>
            </div>
            <!-- Étudiants actifs -->
            <div class="bg-white p-6 md:border-l md:border-gray-300">
                <p class="text-5xl font-title font-medium text-gray-700">70%</p>
                <p class="text-gray-500 mt-2">Étudiants actifs</p>
            </div>
            <!-- Migrants actifs -->
            <div class="bg-white p-6 md:border-l md:border-gray-300">
                <p class="text-5xl font-title font-medium text-gray-700">41%</p>
                <p class="text-gray-500 mt-2">Migrants actifs</p>
            </div>
        </div>
    </section>
    {{-- <section class="py-20 text-center">
        <div class="mb-12 mt-28">
            <h2 class="text-3xl font-title mb-4 uppercase">Partenaires</h2>
            <div class="w-28 border-t-[3px] border-primary mx-auto mb-6"></div>
        </div>
        <div class="md:flex justify-center items-center gap-6">
            <div class="col-1">
                <img src="{{ asset('images/EVAM-logo-new-1920x1080-1-e1664979973156-300x169-1.png') }}"
                    alt="Partenaire 1" class="mx-auto">
            </div>
        </div>
    </section> --}}
    <section class="py-10 text-center">
        <h2 class="text-3xl mb-10 font-normal font-title">IDENTIFIEZ EN QUELQUES CLICS LA PERSONNE QUI CORRESPOND À VOS
            BESOINS
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 md:w-11/12 mx-auto">
            @foreach ($packages as $package)
                <div
                    class="border relative bg-gray-50 overflow-hidden
                           {{ $package->is_popular ? 'border-2 border-primary' : '' }}
                           {{-- On ne centre que si on a un seul package au total --}}
                           {{ $packages->count() === 1 ? 'md:col-start-2' : '' }}">
                    <!-- En-tête avec badge "Populaire" -->
                    <h3 class="text-2xl font-title font-semibold bg-primary text-white py-5 text-center">
                        {{ $package->name }}
                    </h3>

                    @if ($package->is_popular)
                        <div
                            class="absolute top-6 -right-16 bg-white text-primary text-xs py-1 transform rotate-45 w-52 shadow-lg uppercase font-bold text-center">
                            Populaire
                        </div>
                    @endif

                    <!-- Prix -->
                    <div class="relative w-44 mx-auto font-bold font-title my-10">
                        <p class="text-gray-800 text-md absolute top-0 left-1">CHF</p>
                        <p class="text-6xl font-semibold text-gray-800">{{ $package->price }}</p>
                        <p class="text-gray-500 font-normal text-sm">hors taxes</p>
                    </div>

                    <!-- Liste des fonctionnalités -->
                    <ul class="text-left mt-6 mb-14 mx-9">
                        @foreach ($package->features as $feature)
                            <li class="flex text-sm text-gray-400 font-light items-center mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 border font-bold border-primary rounded-full text-primary mr-2"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3"
                                        d="M5 13l4 4L19 7" />
                                </svg>
                                {!! $feature !!}
                            </li>
                            <hr class="my-4">
                        @endforeach
                    </ul>
                </div>
            @endforeach
        </div>


    </section>
</x-guest-layout>