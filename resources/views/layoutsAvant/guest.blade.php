<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>
        {{ $title ?? config('app.name') }}
    </title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">


    {{-- <link rel="stylesheet"
        href="https://cdn.jsdelivr.net/gh/habibmhamadi/multi-select-tag@3.1.0/dist/css/multi-select-tag.css">
    <script src="https://cdn.jsdelivr.net/gh/habibmhamadi/multi-select-tag@3.1.0/dist/js/multi-select-tag.js"></script> --}}

    {{-- <link rel="stylesheet"
            href="https://cdn.jsdelivr.net/gh/habibmhamadi/multi-select-tag@4.0.1/dist/css/multi-select-tag.min.css">
        …
        <script src="https://cdn.jsdelivr.net/gh/habibmhamadi/multi-select-tag@4.0.1/dist/js/multi-select-tag.min.js"></script> --}}
    <!-- CSS -->
    <link rel="stylesheet" href="{{ asset('assets/css/multi-select-tag.min.css') }}">
    <!-- JS -->
    <script src="{{ asset('assets/js/multi-select-tag.min.js') }}"></script>



    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css"
        integrity="sha512-Kc323vGBEqzTmouAECnVceyQqyqdsSiqLQISBL29aUW4U/M7pSPA/gEUZQqv1cwx4OnYxTxve5UMg5GT6L4JJg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @vite(['resources/js/profileCropper.js'])
    @vite(['resources/js/uploadFile.js'])
</head>
@php
    $bg = asset('images/Cyclone-Logo-Seul-Grey.svg');
@endphp

<body class="font-sans text-gray-900 antialiased with-cyclone-bg" style="background-image: url('{{ $bg }}');">

    <x-nav-bar />
    <main class="w-full bg-transparent min-h-[85vh]">
        {{ $slot }}
    </main>
    <footer class="bg-gray-100 py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-14 items-center">
                <div class="col-span-1 text-center md:text-left">
                    <div class=" w-1/2 md:w-96 mx-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                            version="1.1" id="Layer_1" x="0px" class="w-full" y="0px" viewBox="0 0 283.5 83.4"
                            style="enable-background:new 0 0 283.5 83.4;" xml:space="preserve">
                            <style type="text/css">
                                .st0 {
                                    fill: #0BBBEF;
                                }

                                .st1 {
                                    fill: url(#SVGID_1_);
                                }

                                .st2 {
                                    fill: url(#SVGID_00000042701928136227046160000012637274117045435799_);
                                }

                                .st3 {
                                    fill: url(#SVGID_00000080180224983396334050000001115954929454430374_);
                                }

                                .st4 {
                                    fill: url(#SVGID_00000123421001145540387950000004818007067942726073_);
                                }

                                .st5 {
                                    fill: url(#SVGID_00000156560368273605828160000007985826842588754841_);
                                }

                                .st6 {
                                    fill: url(#SVGID_00000067222395579841345620000017174175569374129827_);
                                }

                                .st7 {
                                    fill: #6F6F6E;
                                }
                            </style>
                            <g>
                                <g>
                                    <path class="st0"
                                        d="M81.4,61.7c-4.6,0-8.3-1.4-11-4.3c-2.7-2.8-4.1-6.4-4.1-10.8c0-5.1,1.7-9.3,5.1-12.7c3.4-3.4,8-5.1,13.7-5.1    c3.4,0,6.4,0.7,9,2.2l-0.9,4.2c-2.2-1.7-5.1-2.5-8.6-2.5c-4.1,0-7.4,1.3-10.1,3.9c-2.7,2.6-4,5.8-4,9.5c0,3.5,1,6.3,3,8.4    c2,2.1,4.8,3.2,8.6,3.2c2.4,0,4.6-0.4,6.6-1.1l-0.9,4.2C85.8,61.4,83.6,61.7,81.4,61.7z">
                                    </path>
                                    <path class="st0"
                                        d="M104.2,61.2l2.9-13.4l-8.5-18.4h4.4l6.6,14.4l12.7-14.4h5.1l-16.3,18.4l-2.8,13.4H104.2z">
                                    </path>
                                    <path class="st0"
                                        d="M142.8,61.7c-4.6,0-8.3-1.4-11-4.3c-2.7-2.8-4.1-6.4-4.1-10.8c0-5.1,1.7-9.3,5.1-12.7s8-5.1,13.7-5.1    c3.4,0,6.4,0.7,9,2.2l-0.9,4.2c-2.2-1.7-5.1-2.5-8.6-2.5c-4.1,0-7.4,1.3-10.1,3.9c-2.7,2.6-4,5.8-4,9.5c0,3.5,1,6.3,3,8.4    c2,2.1,4.8,3.2,8.6,3.2c2.4,0,4.6-0.4,6.6-1.1l-0.9,4.2C147.2,61.4,145.1,61.7,142.8,61.7z">
                                    </path>
                                    <path class="st0" d="M157.3,61.2l6.8-31.8h4.2l-6,27.8h15.4l-0.8,3.9H157.3z">
                                    </path>
                                    <path class="st0"
                                        d="M197.2,61.7c-4.4,0-8-1.4-10.7-4.2s-4.1-6.3-4.1-10.5c0-5.1,1.7-9.4,5-12.9c3.3-3.5,7.7-5.2,13.2-5.2    c4.4,0,8,1.4,10.7,4.2c2.7,2.8,4.1,6.3,4.1,10.5c0,5.1-1.7,9.4-5,12.9C207.1,60,202.7,61.7,197.2,61.7z M197.7,57.8    c3.8,0,7-1.3,9.6-4c2.6-2.7,3.9-5.9,3.9-9.8c0-3.3-1-6-2.9-8.1c-1.9-2.1-4.7-3.1-8.2-3.1c-3.8,0-7,1.3-9.6,4    c-2.6,2.7-3.9,5.9-3.9,9.8c0,3.3,1,6,2.9,8.1C191.5,56.7,194.2,57.8,197.7,57.8z">
                                    </path>
                                    <path class="st0"
                                        d="M247.3,61.7l-17.7-23.5l-4.8,23h-4.2l6.8-32.3l17.7,23.5l4.8-23h4.2L247.3,61.7z">
                                    </path>
                                    <path class="st0"
                                        d="M258.2,61.2l6.8-31.8h18.5l-0.8,3.9h-14.4l-2.1,9.6h10.2l-0.9,3.9h-10.2l-2.2,10.3h14.4l-0.9,3.9H258.2z">
                                    </path>
                                </g>
                                <g>

                                    <linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="-13.0892"
                                        y1="20.0961" x2="-9.4759" y2="20.0961"
                                        gradientTransform="matrix(0.9748 -0.2233 0.2233 0.9748 38.3838 38.5738)">
                                        <stop offset="0" style="stop-color:#0BBBEF"></stop>
                                        <stop offset="0.2429" style="stop-color:#0DB9EC"></stop>
                                        <stop offset="0.409" style="stop-color:#14B4E3"></stop>
                                        <stop offset="0.5522" style="stop-color:#20ABD4"></stop>
                                        <stop offset="0.6824" style="stop-color:#319EBE"></stop>
                                        <stop offset="0.8037" style="stop-color:#478EA2"></stop>
                                        <stop offset="0.9171" style="stop-color:#617A80"></stop>
                                        <stop offset="0.9687" style="stop-color:#6F6F6E"></stop>
                                    </linearGradient>
                                    <path class="st1"
                                        d="M30.1,60.5c-0.2,0.6,0.5,1.2,1.1,1.2c1.3,0.1,1.9-0.6,2.3-2C32.3,60.2,30.5,59.2,30.1,60.5z">
                                    </path>

                                    <linearGradient id="SVGID_00000095332372358063933540000017773228023094028961_"
                                        gradientUnits="userSpaceOnUse" x1="31.065" y1="48.1677" x2="41.7093"
                                        y2="48.1677">
                                        <stop offset="0" style="stop-color:#0BBBEF"></stop>
                                        <stop offset="0.1554" style="stop-color:#0FB8EA"></stop>
                                        <stop offset="0.3378" style="stop-color:#1AB0DB"></stop>
                                        <stop offset="0.534" style="stop-color:#2CA2C4"></stop>
                                        <stop offset="0.7398" style="stop-color:#468EA3"></stop>
                                        <stop offset="0.9511" style="stop-color:#677579"></stop>
                                        <stop offset="1" style="stop-color:#6F6F6E"></stop>
                                    </linearGradient>
                                    <path style="fill:url(#SVGID_00000095332372358063933540000017773228023094028961_);"
                                        d="M31.9,47.6c-0.8-0.1-0.9,0.9-0.7,1.5    c0.5,1.4,2.7,1.5,4.1,1.4c3.3-0.4,5-1.6,6.7-4.6C39.1,47.2,35.5,47.9,31.9,47.6z">
                                    </path>

                                    <linearGradient id="SVGID_00000088854449412612577530000010964576427260058812_"
                                        gradientUnits="userSpaceOnUse" x1="31.425" y1="54.8961" x2="38.5594"
                                        y2="54.8961">
                                        <stop offset="0" style="stop-color:#0BBBEF"></stop>
                                        <stop offset="0.1321" style="stop-color:#10B7E8"></stop>
                                        <stop offset="0.3256" style="stop-color:#20ABD4"></stop>
                                        <stop offset="0.5568" style="stop-color:#3998B4"></stop>
                                        <stop offset="0.8153" style="stop-color:#5B7E87"></stop>
                                        <stop offset="0.9461" style="stop-color:#6F6F6E"></stop>
                                    </linearGradient>
                                    <path style="fill:url(#SVGID_00000088854449412612577530000010964576427260058812_);"
                                        d="M32,54.1c-0.6-0.1-0.6,0.9-0.5,1.3    c0.3,0.9,1.4,1.3,2.3,1.2c2.2-0.1,3.6-1.5,4.8-3.4C36.7,54,34.3,54.7,32,54.1z">
                                    </path>

                                    <linearGradient id="SVGID_00000125590882521963093470000010800274390048605592_"
                                        gradientUnits="userSpaceOnUse" x1="13.7714" y1="26.5931" x2="62.1516"
                                        y2="26.5931">
                                        <stop offset="0" style="stop-color:#0BBBEF"></stop>
                                        <stop offset="0.2168" style="stop-color:#0DB9EC"></stop>
                                        <stop offset="0.3763" style="stop-color:#14B4E2"></stop>
                                        <stop offset="0.5174" style="stop-color:#21AAD2"></stop>
                                        <stop offset="0.6479" style="stop-color:#329DBC"></stop>
                                        <stop offset="0.7711" style="stop-color:#498C9F"></stop>
                                        <stop offset="0.8873" style="stop-color:#65777B"></stop>
                                        <stop offset="0.9257" style="stop-color:#6F6F6E"></stop>
                                    </linearGradient>
                                    <path style="fill:url(#SVGID_00000125590882521963093470000010800274390048605592_);"
                                        d="M54.5,23.4c-5.9,3.6-12.2,5.9-18.9,7.4    c-4.9,1.1-9.9,1.9-15,1.1c-2-0.3-3.7-1.2-4.4-3.4c-0.1-0.4-0.1-1-0.7-1.1c-0.7-0.1-0.9,0.6-1.1,1.1c-1.4,2.7-0.1,5.8,2.9,7.1    c1.2,0.5,3,0.9,4.6,1c20.8,0.8,33.2-9.5,36.8-13.2c2.6-2.6,3.6-4.9,3.5-6.8C61.1,18.3,61.3,18.9,54.5,23.4z">
                                    </path>

                                    <linearGradient id="SVGID_00000041275063781771452170000017889268862109847213_"
                                        gradientUnits="userSpaceOnUse" x1="3.8831" y1="12.8741" x2="63.5405"
                                        y2="12.2543">
                                        <stop offset="0" style="stop-color:#0BBBEF"></stop>
                                        <stop offset="0.2289" style="stop-color:#0DB9EC"></stop>
                                        <stop offset="0.3907" style="stop-color:#14B4E3"></stop>
                                        <stop offset="0.5319" style="stop-color:#20ABD3"></stop>
                                        <stop offset="0.6612" style="stop-color:#319EBD"></stop>
                                        <stop offset="0.7825" style="stop-color:#488DA0"></stop>
                                        <stop offset="0.8962" style="stop-color:#63787E"></stop>
                                        <stop offset="0.9415" style="stop-color:#6F6F6E"></stop>
                                    </linearGradient>
                                    <path style="fill:url(#SVGID_00000041275063781771452170000017889268862109847213_);"
                                        d="M64.5,3.7c-0.1,1.2-1,2.4-1.9,3.2    c-3,2.7-7,4.3-10.8,5.8c-10.2,4-21.4,6.3-32.3,6c-3.1-0.1-7.7-0.6-8.2-2.3c-0.5-1.7,1.9-3.8,6-5.9c5.6-2.8,15.9-5.9,15.9-5.9    C48,0.4,55,0.6,55,0.6c-0.7,0-7.6-1.8-22.9,1C26.5,2.7,20.9,4.1,15.4,6c-4.3,1.5-8.7,3-12.4,6.1c-3.3,2.6-4.4,7.5-0.6,10.4    c8.5,5.5,25.2,1.4,34.4-1.1c7.7-2.3,15.1-4.4,22.5-9.5C66.7,6.2,64.5,3.7,64.5,3.7z">
                                    </path>

                                    <linearGradient id="SVGID_00000104694595350649448010000010012229401183617416_"
                                        gradientUnits="userSpaceOnUse" x1="27.3626" y1="37.7877" x2="55.2321"
                                        y2="37.7877">
                                        <stop offset="0" style="stop-color:#0BBBEF"></stop>
                                        <stop offset="0.1629" style="stop-color:#0EB9EB"></stop>
                                        <stop offset="0.318" style="stop-color:#17B2DF"></stop>
                                        <stop offset="0.4699" style="stop-color:#26A7CC"></stop>
                                        <stop offset="0.6198" style="stop-color:#3B97B1"></stop>
                                        <stop offset="0.767" style="stop-color:#55828F"></stop>
                                        <stop offset="0.8833" style="stop-color:#6F6F6E"></stop>
                                    </linearGradient>
                                    <path style="fill:url(#SVGID_00000104694595350649448010000010012229401183617416_);"
                                        d="M50.4,34.4c-3.2,2.2-5.8,3.4-9.9,4.8    c-4,1.2-6.9,1.5-9.1,1.5c-1.7,0-3.4-1-3.6,0.2c-0.3,2,2.2,2.8,3.4,3C33.1,44,35,44,38.5,43c0.9-0.3,1.9-0.6,2.8-1    c3-1.2,5.5-2.7,5.5-2.7c6.9-4.3,6.6-7.7,6.6-7.7C52.6,32.8,51.5,33.6,50.4,34.4z">
                                    </path>
                                </g>
                                <g>
                                    <path class="st7"
                                        d="M160.5,72.3c0.7,0,1.3,0.1,1.8,0.2c0.5,0.2,0.9,0.4,1.3,0.7c0.3,0.3,0.6,0.7,0.8,1.1c0.2,0.4,0.3,0.9,0.3,1.4    c0,0.5-0.1,1-0.3,1.4c-0.2,0.4-0.4,0.8-0.8,1.1s-0.8,0.5-1.3,0.7c-0.5,0.2-1.1,0.3-1.8,0.3h-1.8v4.1h-1.5v-11H160.5z M160.5,78    c0.4,0,0.8-0.1,1.1-0.2c0.3-0.1,0.6-0.3,0.8-0.5c0.2-0.2,0.4-0.4,0.5-0.7c0.1-0.3,0.2-0.6,0.2-0.9c0-0.7-0.2-1.2-0.6-1.6    c-0.4-0.4-1.1-0.6-2-0.6h-1.8V78H160.5z">
                                    </path>
                                    <path class="st7" d="M176.5,82v1.3h-6.2v-11h1.5V82H176.5z"></path>
                                    <path class="st7"
                                        d="M191,83.3h-1.2c-0.1,0-0.2,0-0.3-0.1c-0.1-0.1-0.1-0.2-0.2-0.3l-1-2.7h-4.9l-1,2.7c0,0.1-0.1,0.2-0.2,0.2    c-0.1,0.1-0.2,0.1-0.3,0.1h-1.2l4.4-11h1.5L191,83.3z M187.9,79.2l-1.7-4.5c-0.1-0.1-0.1-0.3-0.2-0.5c-0.1-0.2-0.1-0.4-0.2-0.6    c-0.1,0.4-0.2,0.8-0.3,1l-1.7,4.5H187.9z">
                                    </path>
                                    <path class="st7"
                                        d="M203.7,81c0.1,0,0.2,0,0.2,0.1l0.6,0.6c-0.5,0.5-1,0.9-1.6,1.2c-0.6,0.3-1.4,0.4-2.3,0.4    c-0.8,0-1.5-0.1-2.1-0.4s-1.2-0.7-1.6-1.2c-0.5-0.5-0.8-1.1-1.1-1.8c-0.3-0.7-0.4-1.4-0.4-2.3c0-0.8,0.1-1.6,0.4-2.3    s0.6-1.3,1.1-1.8c0.5-0.5,1-0.9,1.7-1.2c0.7-0.3,1.4-0.4,2.2-0.4c0.8,0,1.5,0.1,2.1,0.4c0.6,0.3,1.1,0.6,1.6,1l-0.5,0.7    c0,0.1-0.1,0.1-0.1,0.1c0,0-0.1,0.1-0.2,0.1c-0.1,0-0.2-0.1-0.3-0.2c-0.1-0.1-0.3-0.2-0.5-0.3c-0.2-0.1-0.5-0.2-0.8-0.3    s-0.7-0.2-1.2-0.2c-0.6,0-1.1,0.1-1.6,0.3c-0.5,0.2-0.9,0.5-1.2,0.9c-0.3,0.4-0.6,0.8-0.8,1.4c-0.2,0.5-0.3,1.1-0.3,1.8    c0,0.7,0.1,1.3,0.3,1.8c0.2,0.5,0.5,1,0.8,1.4c0.3,0.4,0.7,0.7,1.2,0.8c0.5,0.2,1,0.3,1.5,0.3c0.3,0,0.6,0,0.9-0.1    c0.3,0,0.5-0.1,0.7-0.2c0.2-0.1,0.4-0.2,0.6-0.3c0.2-0.1,0.4-0.3,0.6-0.4C203.5,81,203.6,81,203.7,81z">
                                    </path>
                                    <path class="st7"
                                        d="M216.8,82.1l0,1.2H210v-11h6.8v1.2h-5.3v3.7h4.3v1.2h-4.3v3.7H216.8z"></path>
                                    <path class="st7"
                                        d="M234.2,72.3v11h-1.3v-8.1c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.4l-3.8,6.9c-0.1,0.2-0.3,0.3-0.5,0.3h-0.2    c-0.2,0-0.4-0.1-0.5-0.3l-3.9-6.9c0,0.3,0,0.5,0,0.8v8.1h-1.3v-11h1.1c0.1,0,0.2,0,0.3,0c0.1,0,0.1,0.1,0.2,0.2l3.8,6.8    c0.1,0.1,0.1,0.3,0.2,0.4c0.1,0.1,0.1,0.3,0.2,0.4c0.1-0.1,0.1-0.3,0.2-0.4c0.1-0.1,0.1-0.3,0.2-0.4l3.7-6.8    c0.1-0.1,0.1-0.2,0.2-0.2c0.1,0,0.2,0,0.3,0H234.2z">
                                    </path>
                                    <path class="st7"
                                        d="M247.6,82.1l0,1.2h-6.8v-11h6.8v1.2h-5.3v3.7h4.3v1.2h-4.3v3.7H247.6z"></path>
                                    <path class="st7"
                                        d="M262.4,72.3v11h-0.7c-0.1,0-0.2,0-0.3-0.1c-0.1,0-0.2-0.1-0.2-0.2l-6.4-8.3c0,0.1,0,0.3,0,0.4    c0,0.1,0,0.2,0,0.3v7.8h-1.3v-11h0.8c0.1,0,0.1,0,0.2,0c0,0,0.1,0,0.1,0c0,0,0.1,0,0.1,0.1c0,0,0.1,0.1,0.1,0.1l6.4,8.3    c0-0.1,0-0.3,0-0.4c0-0.1,0-0.2,0-0.4v-7.8H262.4z">
                                    </path>
                                    <path class="st7" d="M276.5,73.5h-3.6v9.7h-1.5v-9.7h-3.6v-1.2h8.6V73.5z"></path>
                                </g>
                            </g>
                        </svg>
                    </div>
                </div>
                <div class="col-span-1">
                    <div class="md:flex justify-center text-center md:justify-end items-center">
                        <div>Suivez-nous:</div>
                        <div class="flex md:space-between justify-center mt-5 md:mt-0 items-center ml-4 gap-4">
                            <a href="https://www.facebook.com/cycloneplacement"
                                class="text-white bg-gray-700 p-2 rounded-full hover:text-primary w-10 h-10 flex justify-center items-center">
                                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                                    <g id="SVGRepo_iconCarrier">
                                        <path
                                            d="M20 12.05C19.9813 10.5255 19.5273 9.03809 18.6915 7.76295C17.8557 6.48781 16.673 5.47804 15.2826 4.85257C13.8921 4.2271 12.3519 4.01198 10.8433 4.23253C9.33473 4.45309 7.92057 5.10013 6.7674 6.09748C5.61422 7.09482 4.77005 8.40092 4.3343 9.86195C3.89856 11.323 3.88938 12.8781 4.30786 14.3442C4.72634 15.8103 5.55504 17.1262 6.69637 18.1371C7.83769 19.148 9.24412 19.8117 10.75 20.05V14.38H8.75001V12.05H10.75V10.28C10.7037 9.86846 10.7483 9.45175 10.8807 9.05931C11.0131 8.66687 11.23 8.30827 11.5161 8.00882C11.8022 7.70936 12.1505 7.47635 12.5365 7.32624C12.9225 7.17612 13.3368 7.11255 13.75 7.14003C14.3498 7.14824 14.9482 7.20173 15.54 7.30003V9.30003H14.54C14.3676 9.27828 14.1924 9.29556 14.0276 9.35059C13.8627 9.40562 13.7123 9.49699 13.5875 9.61795C13.4627 9.73891 13.3667 9.88637 13.3066 10.0494C13.2464 10.2125 13.2237 10.387 13.24 10.56V12.07H15.46L15.1 14.4H13.25V20C15.1399 19.7011 16.8601 18.7347 18.0985 17.2761C19.3369 15.8175 20.0115 13.9634 20 12.05Z"
                                            fill="currentColor"></path>
                                    </g>
                                </svg>
                            </a>
                            <a href="https://www.linkedin.com/company/cyclone-placement/"
                                class="text-white bg-gray-700 p-2 rounded-full hover:text-primary w-10 h-10 flex justify-center items-center">
                                <svg fill="currentColor" class="w-4" version="1.1" id="Layer_1"
                                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                    viewBox="-*********** 512" xml:space="preserve">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                                    <g id="SVGRepo_iconCarrier">
                                        <path
                                            d="M329,145h-432c-22.1,0-40,17.9-40,40v432c0,22.1,17.9,40,40,40h432c22.1,0,40-17.9,40-40V185C369,162.9,351.1,145,329,145z M41.4,508.1H-8.5V348.4h49.9V508.1z M15.1,328.4h-0.4c-18.1,0-29.8-12.2-29.8-27.7c0-15.8,12.1-27.7,30.5-27.7 c18.4,0,29.7,11.9,30.1,27.7C45.6,316.1,33.9,328.4,15.1,328.4z M241,508.1h-56.6v-82.6c0-21.6-8.8-36.4-28.3-36.4 c-14.9,0-23.2,10-27,19.6c-1.4,3.4-1.2,8.2-1.2,13.1v86.3H71.8c0,0,0.7-146.4,0-159.7h56.1v25.1c3.3-11,21.2-26.6,49.8-26.6 c35.5,0,63.3,23,63.3,72.4V508.1z">
                                        </path>
                                    </g>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-12 font-normal">
                <!-- Left Section: Company Description -->
                <div class="text-left">
                    <p class="text-gray-600 mb-4">
                        Grâce à une interface conviviale et à un vaste réseau de recruteurs, Cyclone Placement se
                        donne
                        pour mission de vous aider à trouver la place qui vous convient, quels que soient votre
                        domaine
                        d’activité, vos qualifications et vos aspirations professionnelles.
                    </p>
                </div>

                <!-- Middle Section: Links -->
                <div>
                    <h4 class="font-bold text-gray-700 mb-4">CYCLONE PLACEMENT</h4>
                    <ul class="text-gray-600 space-y-2 font-light">
                        <li><a href="{{ route('candidate.index') }}"
                                class=" font-thin py-2 {{ Route::is('candidates') ? 'text-primary underline' : '' }}">Candidats</a>
                        </li>
                        <li><a href="{{ route('recruiters') }}"
                                class=" font-thin py-2 {{ Route::is('recruiters') ? 'text-primary underline' : '' }}">Recruteurs</a>
                        </li>
                        <li><a href="{{ url('/abonnements') }}"
                                class=" font-thin py-2 {{ Route::is('features') ? 'text-primary underline' : '' }}">Abonnements</a>
                        </li>
                        <li><a href="{{ url('/general-conditions') }}"
                                class=" font-thin py-2 {{ Route::is('generalConditions') ? 'text-primary underline' : '' }}">Conditions
                                d'utilisation</a></li>
                        <li><a href="#" class="hover:text-primary">Articles</a></li>
                    </ul>
                </div>

                <!-- Right Section: Assurances Sociales -->
                <div>
                    <h4 class="font-bold text-gray-700 mb-4">ASSURANCES SOCIALES</h4>
                    <ul class="text-gray-600 space-y-2">
                        <li><a href="https://www.ahv-iv.ch/fr/Assurances-sociales/Assurance-vieillesse-et-survivants-AVS"
                                class="text-gray-600 hover:text-primary">AVS (Assurance-Vieillesse et Survivants)</a>
                        </li>
                        <li><a href="https://www.ahv-iv.ch/fr/M%C3%A9mentos-Formulaires/Formulaires/Prestations-de-lAI"
                                class="text-gray-600 hover:text-primary">AI (Assurance-Invalidité)</a></li>
                        <li><a href="https://www.ahv-iv.ch/fr/Assurances-sociales/Prestations-compl%C3%A9mentaires-PC"
                                class="text-gray-600 hover:text-primary">PC (Prestations Complémentaires)</a></li>
                        <li><a href="https://www.bsv.admin.ch/bsv/fr/home/<USER>/bv.html"
                                class="text-gray-600 hover:text-primary">LPP (Prévoyance Professionnelle)</a></li>
                        <li><a href="https://www.bsv.admin.ch/bsv/fr/home/<USER>/bv.html"
                                class="text-gray-600 hover:text-primary">LAA (Loi sur l’Assurance-Accidents
                                obligatoire)</a></li>
                        <li><a href="https://www.bag.admin.ch/bag/fr/home/<USER>/gesetzgebung/gesetzgebung-versicherungen/gesetzgebung-krankenversicherung/kvg.html"
                                class="text-gray-600 hover:text-primary">LAMal (Loi sur l’Assurance Maladie)</a></li>
                        <li><a href="https://www.vtg.admin.ch/fr/mon-service-militaire/militaires/sold-eo.html"
                                class="text-gray-600 hover:text-primary">APG (Allocations pour perte de gain)</a></li>
                        <li><a href="https://www.eak.admin.ch/eak/fr/home/<USER>/kinder/familienzulagen.html"
                                class="text-gray-600 hover:text-primary">Allocations familiales</a></li>
                        <li><a href="https://www.ahv-iv.ch/fr/Assurances-sociales/Autres-types-dassurance-sociale/Assurance-ch%C3%B4mage-AC"
                                class="text-gray-600 hover:text-primary">AC (Assurance Chômage)</a></li>
                    </ul>

                </div>
            </div>

            <!-- Bottom Section: Socials and Legal -->
            <div class="flex flex-col md:flex-row justify-between items-center mt-10 border-t pt-4 text-gray-600">
                <div class="text-center md:text-left mb-4 md:mb-0">
                    ©2024 Cyclone Placement. Tous droits réservé.
                </div>

                <div class="text-center md:text-right">
                    <a href="{{ url('/general-conditions') }}"
                        class=" hover:text-green-500 hover:underline font-thin py-2 {{ Route::is('generalConditions') ? 'text-green-500 underline' : '' }}">Conditions
                        Générales</a>
                    <a href="{{ route('faq') }}" class="hover:text-primary mx-2">FAQs</a>
                    <a href="#" class="hover:text-primary mx-2">Politique de confidentialité</a>
                </div>
            </div>
        </div>
    </footer>
    <!-- Conteneur des boutons -->
    <div class="fixed bottom-4 right-4 flex gap-2 items-center">
        <!-- Bouton "Donner un avis" -->

        <x-feedback-button-guest active="true" />

        {{-- @dd(auth()->user()); --}}

        @isset($priorities, $severities)
            <x-feedback-modal :priorities="$priorities" :severities="$severities" :user="auth()->user()" />
        @endisset




        <!-- Bouton "Go Top" -->
        <button id="goTop"
            class="bg-primary text-white w-12 h-12 rounded-full flex justify-center items-center shadow-md hover:text-primary transition">
            <svg fill="currentColor" height="200px" width="200px" class="w-6 h-6"
                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512.01 512.01">
                <g>
                    <path d="M505.755,358.256L271.088,123.589c-8.341-8.341-21.824-8.341-30.165,0L6.256,358.256c-8.341,8.341-8.341,21.824,0,30.165
            s21.824,8.341,30.165,0l219.584-219.584l219.584,219.584c4.16,4.16,9.621,6.251,15.083,6.251c5.462,0,10.923-2.091,15.083-6.251
            C514.096,380.08,514.096,366.597,505.755,358.256z">
                    </path>
                </g>
            </svg>
        </button>
    </div>






    <script>
        document.getElementById('goTop').addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>

</body>

</html>
