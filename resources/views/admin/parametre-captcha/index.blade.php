@php
    use App\Models\ConfigGlobalApp; // Importer le modèle ConfigGlobalApp
@endphp
<x-admin-layout>
    <x-slot name="title">
        Paramètres Captcha
    </x-slot>

    <!-- Titre de la page -->
    <div class="w-full flex justify-between items-center my-4">
        <div class="w-full flex items-center space-x-2">
            <svg fill="currentColor"
                class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <rect x="4" y="4" width="16" height="16" />
            </svg>

            <h1 class="text-3xl font-semibold text-gray-900 dark:text-white mb-0">
                Paramètres Captcha
            </h1>
        </div>
    </div>

    <!-- Modal de modification -->
    <div id="edit-modal" tabindex="-1" aria-hidden="true"
        class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow-lg dark:bg-gray-800">
                <div class="flex items-center justify-between p-4 border-b rounded-t dark:border-gray-600">
                    <!-- Titre dynamique -->
                    <h3 id="modal-title" class="text-xl font-semibold text-gray-900 dark:text-white">
                        Modifier le paramètre
                    </h3>
                    <button type="button" data-modal-hide="edit-modal"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg w-8 h-8">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M1 1l6 6m0 0l6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                    </button>
                </div>
                <div class="p-4">
                    <!-- Formulaire dans le modal -->
                    <form class="space-y-4" id="edit-form" method="POST"
                        action="{{ route('parametre-captcha.update') }}">
                        @csrf
                        @method('POST')

                        <!-- Champ caché pour la clé -->
                        <input type="hidden" id="edit-key" name="key" />

                        <!-- Champ pour la valeur -->
                        <div>
                            <label for="edit-tax-value" class="block text-sm font-medium text-gray-900 dark:text-white">
                                Nouvelle valeur
                            </label>
                            <input type="text" id="edit-tax-value" name="value" required
                                class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
                        </div>

                        <!-- Bouton de soumission -->
                        <button type="submit"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5">
                            Mettre à jour
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des paramètres Captcha -->
    <div class="w-full overflow-x-auto shadow-lg rounded-lg bg-white">
        <table class="min-w-full table-auto text-sm text-gray-500">
            <thead>
                <tr class="bg-gray-100 text-gray-700">
                    <th class="px-6 py-3 text-left">Clé</th>
                    <th class="px-6 py-3 text-left">Valeur</th>
                    <th class="px-6 py-3 text-left">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white">
                @if ($captchaConfig)
                    @foreach ($captchaConfig->value as $key => $value)
                        <tr class="border-b">
                            <!-- Clé -->
                            <td class="px-6 py-4">{{ $key }}</td>

                            <!-- Valeur -->
                            <td class="px-6 py-4">{{ $value }}</td>

                            <!-- Actions -->
                            <td class="px-6 py-4">
                                <!-- Bouton Modifier -->
                                <button data-modal-target="edit-modal" data-modal-toggle="edit-modal"
                                    data-key="{{ $key }}" 
                                    data-tax-value="{{ $value }}"
                                    data-title="Modifier {{ ucfirst(str_replace('_', ' ', strtolower($key))) }}" 
                                    class="text-blue-500 hover:text-blue-700 ml-2">
                                    Modifier
                                </button>
                            </td>
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <td colspan="3" class="px-6 py-4 text-center">Aucune configuration Captcha trouvée.</td>
                    </tr>
                @endif
            </tbody>
        </table>
    </div>

    <!-- Scripts pour SweetAlert -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Gestion du modal d'édition
            const editButtons = document.querySelectorAll('[data-modal-target="edit-modal"]');
            editButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Récupérer les données du bouton
                    const key = button.getAttribute('data-key');
                    const value = button.getAttribute('data-tax-value');
                    const title = button.getAttribute('data-title'); // Récupérer le titre dynamique

                    // Mettre à jour l'action du formulaire
                    const editForm = document.getElementById('edit-form');
                    editForm.action = "{{ route('parametre-captcha.update') }}";

                    // Pré-remplir les champs du modal
                    document.getElementById('edit-key').value = key;
                    document.getElementById('edit-tax-value').value = value;

                    // Mettre à jour le titre du modal
                    const modalTitle = document.querySelector('#edit-modal h3');
                    modalTitle.textContent = title; // Mettre à jour le titre

                    // Ouvrir le modal
                    const editModal = document.getElementById('edit-modal');
                    editModal.classList.remove('hidden');
                    editModal.setAttribute('aria-hidden', 'false');
                });
            });

            // Afficher une notification SweetAlert en fonction des messages de session
            const successMessage = "{{ session('success') }}";
            const errorMessage = "{{ session('error') }}";

            if (successMessage) {
                Swal.fire({
                    icon: 'success',
                    title: 'Succès',
                    text: successMessage,
                    showConfirmButton: false,
                    timer: 3000 // Fermer automatiquement après 3 secondes
                });
            }

            if (errorMessage) {
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur',
                    text: errorMessage,
                    showConfirmButton: false,
                    timer: 3000 // Fermer automatiquement après 3 secondes
                });
            }
        });
    </script>
</x-admin-layout>