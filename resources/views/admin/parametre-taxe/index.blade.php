@php
    use App\Models\ConfigGlobalApp; // Importer le modèle ConfigGlobalApp
@endphp
<x-admin-layout>
    <x-slot name="title">
        Paramètre taxe
    </x-slot>

    <!-- Titre de la page -->
    <div class="w-full flex justify-between items-center my-4">
        <div class="w-full flex items-center space-x-2">
            <svg fill="currentColor"
                class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <!-- Nouveau code SVG pour une icône de rouage -->
                <path
                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2V7zm0 8h2v2h-2v-2z" />
            </svg>
            <h1 class="text-3xl font-semibold text-gray-900 dark:text-white mb-0">
                Paramètre taxe
            </h1>
        </div>
    </div>

    <!-- Modal de modification -->
    <div id="edit-modal" tabindex="-1" aria-hidden="true"
        class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow-lg dark:bg-gray-800">
                <div class="flex items-center justify-between p-4 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Modifier l'identifiant taux de taxe</h3>
                    <button type="button" data-modal-hide="edit-modal"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg w-8 h-8">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M1 1l6 6m0 0l6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                    </button>
                </div>
                <div class="p-4">
                    <form class="space-y-4" id="edit-form" method="POST"
                        action="{{ route('stripe-webhook.parametre-tax.update') }}">
                        @csrf
                        @method('POST')

                        <!-- Champ pour la valeur de la taxe -->
                        <div>
                            <label for="edit-tax-value" class="block text-sm font-medium text-gray-900 dark:text-white">
                                Identifiant taux de taxe
                            </label>
                            <input type="text" id="edit-tax-value" name="value" required
                                class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
                        </div>

                        <!-- Bouton de soumission -->
                        <button type="submit"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5">
                            Mettre à jour
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des paramètres de taxe -->
    <div class="w-full overflow-x-auto shadow-lg rounded-lg bg-white">
        <table class="min-w-full table-auto text-sm text-gray-500">
            <thead>
                <tr class="bg-gray-100 text-gray-700">
                    <th class="px-6 py-3 text-left">Identifiant taux de taxe</th>
                    <th class="px-6 py-3 text-left">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white">
                @if ($taxConfig)
                    <tr class="border-b">
                        <!-- Valeur de la taxe -->
                        <td class="px-6 py-4">{{ $taxConfig->value }}</td>

                        <!-- Actions -->
                        <td class="px-6 py-4">
                            <!-- Bouton Modifier -->
                            <button data-modal-target="edit-modal" data-modal-toggle="edit-modal"
                                data-id="{{ $taxConfig->id }}" data-tax-value="{{ $taxConfig->value }}"
                                class="text-blue-500 hover:text-blue-700 ml-2">
                                Modifier
                            </button>
                        </td>
                    </tr>
                @else
                    <tr>
                        <td colspan="2" class="px-6 py-4 text-center">Aucun paramètre de taxe enregistré.</td>
                    </tr>
                @endif
            </tbody>
        </table>
    </div>

    <!-- Scripts pour SweetAlert -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Gestion du modal d'édition
            const editButtons = document.querySelectorAll('[data-modal-target="edit-modal"]');
            editButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Récupérer les données du bouton
                    const taxValue = button.getAttribute('data-tax-value');

                    // Mettre à jour l'action du formulaire
                    const editForm = document.getElementById('edit-form');
                    editForm.action = "{{ route('stripe-webhook.parametre-tax.update') }}";

                    // Pré-remplir les champs du modal
                    document.getElementById('edit-tax-value').value = taxValue;

                    // Ouvrir le modal
                    const editModal = document.getElementById('edit-modal');
                    editModal.classList.remove('hidden');
                    editModal.setAttribute('aria-hidden', 'false');
                });
            });

            // Afficher une notification SweetAlert en fonction des messages de session
            const successMessage = "{{ session('success') }}";
            const errorMessage = "{{ session('error') }}";

            if (successMessage) {
                Swal.fire({
                    icon: 'success',
                    title: 'Succès',
                    text: successMessage,
                    showConfirmButton: false,
                    timer: 3000 // Fermer automatiquement après 3 secondes
                });
            }

            if (errorMessage) {
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur',
                    text: errorMessage,
                    showConfirmButton: false,
                    timer: 3000 // Fermer automatiquement après 3 secondes
                });
            }
        });
    </script>
</x-admin-layout>
