<x-admin-layout>
    <x-slot name="title">Re<PERSON>ruteurs</x-slot>
    <div class="w-full flex items-center space-x-2">
        <svg fill="currentColor"
            class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
            viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
            <g id="SVGRepo_iconCarrier">
                <path
                    d="M856 752L645 540q-4-4-10-2.5t-8 7.5l-94 263-71 25q-6 2-7 8t3 11l87 87q4 4 10.5 2.5t8.5-6.5l25-71 263-94q6-2 7.5-8t-3.5-10zM418 894q-13-12-18.5-28.5T397 831t15.5-32.5T443 778l31-11q13-5 17-18l45-124q4-12-3-23l-34-51q-6-8-14-15-2-2-1.5-4.5t2.5-3.5q60-29 98-83 39-58 39-124 0-63-31-116t-84-84-115.5-31T277 121t-84 84-31 116q0 66 39 124 37 54 98 83 2 1 2 3.5t-2 4.5q-7 7-13 15-88 131-132 205-18 32-18 52 0 27 34.5 50.5T264 896t128 14h19q6 0 8.5-6t-1.5-10zm434-393l-45 60q-3 3-2.5 7t3.5 7q8 7 16 15 2 3 6 3.5t7-1.5l61-44q3-3 4-7t-2-8q-15-18-34-34-3-3-7.5-2.5T852 501zm7 160q2 10 2 20 1 4 3.5 6.5t6.5 2.5h75q5 0 8-3t2-7q-1-23-6-45-1-4-4.5-6t-7.5-1l-73 22q-3 1-5 4t-1 7zM749 532l22-72q2-4 0-7.5t-6-4.5q-24-6-48-8-4 0-7 3t-3 7v76q0 3 2.5 6t6.5 3q11 1 22 4 3 1 6.5-1t4.5-6z">
                </path>
            </g>
        </svg>
        <h1 class="text-3xl font-semibold text-gray-900 dark:text-white mb-0">
            Liste des recruteurs
        </h1>
    </div>

    <div class="w-full flex justify-between items-center my-4">
        <form id="search-form" class="relative w-96" action="{{ route('admin.recruiters.search') }}" method="GET">
            <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                    <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                    </svg>
                </div>
                <input type="search" id="default-search" name="query"
                    class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    placeholder="Rechercher un recruteur..." required />
                <button type="submit"
                    class="text-white absolute end-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                    Rechercher
                </button>
            </div>
        </form>
        <div class="flex items-center">
            <!-- Bouton affiché uniquement si la route correspond -->
            @if (request()->routeIs('admin.recruiters.search') || request()->routeIs('admin.recruiters.search'))
                <a href="{{ route('admin.recruiters') }}"
                    class="block text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800 mr-2"
                    type="button">
                    Listes des recruteurs
                </a>
            @endif



            <button id="dropdownDefaultButton" data-dropdown-toggle="dropdown"
                class="text-white md:mx-3 bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                type="button">
                Filtres
                <svg class="w-2.5 h-2.5 ms-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 10 6">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m1 1 4 4 4-4" />
                </svg>
            </button>

            <!-- Dropdown menu -->
            <div id="dropdown"
                class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                    <li>
                        <a href="{{ route('admin.recruiters') }}"
                            class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                            Tout</a>
                    </li>
                    <li>
                        <a href="{{ route('admin.recruiters', ['f' => 'active']) }}"
                            class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                            Activés
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('admin.recruiters', ['f' => 'inactive']) }}"
                            class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                            Désactivés
                        </a>
                    </li>

                </ul>
            </div>
        </div>
    </div>



    <div class="relative overflow-x-auto shadow-md sm:rounded-lg" id="recruiter-list">
        @include('admin.recruters.partials.recruiter_list', ['recruiters' => $recruiters])
    </div>

    {{-- <script>
        document.getElementById('search-form').addEventListener('submit', function(e) {
            e.preventDefault();
            let form = this;
            let formData = new FormData(form);

            fetch('{{ route('admin.recruiters.search') }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('recruiter-list').innerHTML = data.recruiters;
                })
                .catch(error => console.error('Error:', error));
        });
    </script> --}}
</x-admin-layout>
