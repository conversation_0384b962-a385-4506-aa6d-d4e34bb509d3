<x-admin-layout>

    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Re<PERSON>ruter Details') }}
        </h2>
    </x-slot>
    <div class="h-full p-8">
        <div class="bg-white rounded-lg shadow-xl pb-8">
            <div class="w-full h-[250px]">
                <img src="https://vojislavd.com/ta-template-demo/assets/img/profile-background.jpg"
                    class="w-full h-full rounded-tl-lg rounded-tr-lg">
            </div>
            <div class="flex flex-col items-center -mt-20">
                @if ($recruiter->photo_file_id)
                    <img src="{{ $recruiter->photo }}" class="w-40 border-4 border-white rounded-full" alt="user photo">
                @else
                    <img src="https://avatar.iran.liara.run/public" class="w-40 h-40 rounded-full" alt="user photo">
                @endif


                <div class="flex items-center space-x-2 mt-2">
                    <p class="text-2xl">{{ $fullName ?? 'Nom complet non disponible' }}</p>
                    <span class="bg-blue-500 rounded-full p-1" title="Verified">
                        <svg xmlns="http://www.w3.org/2000/svg" class="text-gray-100 h-2.5 w-2.5" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M5 13l4 4L19 7">
                            </path>
                        </svg>
                    </span>
                </div>
                {{-- <p class="text-gray-700">Senior Software Engineer at Tailwind CSS</p> --}}
                {{-- @dd($country) --}}
                <p class="text-sm text-gray-500">
                    {{ $country ? $country->name . ' ' . $country->code : 'Pays non disponible' }},
                    {{ $recruiter->commune ?? 'Commune non disponible' }}</p>
            </div>
            <div class="flex-1 flex flex-col items-center lg:items-end justify-end px-8 mt-2">
                <div class="flex items-center space-x-4 mt-2">
                    @if ($user->is_suspend == true)
                        <a href="{{ route('admin.recruiters.unsuspend', $user->id) }}"
                            class="flex items-center bg-green-600 hover:bg-green-700 text-gray-100 px-4 py-2 rounded text-sm space-x-2 transition duration-100">
                            <svg fill="currentColor" viewBox="0 0 32 32" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" class="h-4 w-4">
                                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                                <g id="SVGRepo_iconCarrier">
                                    <title>
                                        Réactiver le compte
                                    </title>
                                    <path
                                        d="M16 2c7.732 0 14 6.268 14 14s-6.268 14-14 14-14-6.268-14-14 6.268-14 14-14zM16 4c-6.617 0-12 5.383-12 12s5.383 12 12 12 12-5.383 12-12-5.383-12-12-12zM12 16l8 4v-8l-8 4z">
                                    </path>
                                </g>
                            </svg>
                            <span>Réactiver le compte</span>
                        </a>
                    @else
                        <a href="{{ route('admin.recruiters.suspend', $user->id) }}"
                            class="flex items-center bg-red-600 hover:bg-red-700 text-gray-100 px-4 py-2 rounded text-sm space-x-2 transition duration-100">
                            <svg fill="currentColor" viewBox="0 0 32 32" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" class="h-4 w-4">
                                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                                <g id="SVGRepo_iconCarrier">
                                    <title>
                                        Suspendre le compte
                                    </title>
                                    <path
                                        d="M16 29c-7.179 0-13-5.82-13-13s5.821-13 13-13c7.18 0 13 5.82 13 13s-5.82 13-13 13zM16 26c2.211 0 4.249-0.727 5.905-1.941l-13.963-13.962c-1.216 1.655-1.942 3.692-1.942 5.903 0 5.522 4.477 10 10 10zM16 6c-2.228 0-4.279 0.737-5.941 1.97l13.971 13.972c1.232-1.663 1.97-3.713 1.97-5.942 0-5.523-4.477-10-10-10z">
                                    </path>
                                </g>
                            </svg>
                            <span>Suspendre le compte</span>
                        </a>
                    @endif

                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-xl p-8 mt-5">
            <h4 class="text-xl text-gray-900 font-bold mb-4">Info Personnel</h4>
            <div class="grid grid-cols-2 gap-4">
                <div class="font-bold">Nom Complet:</div>
                <div class="text-gray-700">{{ $fullName ?? 'Nom complet non disponible' }}</div>

                <div class="font-bold">Date de Naissance:</div>
                <div class="text-gray-700">
                    {{ $recruiter->date_of_birth ?? 'Date de naissance non disponible' }}
                </div>

                <div class="font-bold">Date d'inscription:</div>
                <div class="text-gray-700">
                    {{ $recruiter->created_at ? $recruiter->created_at->format('d/m/Y') : 'Date non disponible' }}
                </div>

                <div class="font-bold">Téléphone:</div>
                <div class="text-gray-700">{{ $phoneNumber ? $phoneNumber->number : 'Numéro non disponible' }}</div>

                <div class="font-bold">Email:</div>
                <div class="text-gray-700">{{ $user->email ?? 'Email non disponible' }}</div>

                <div class="font-bold">Adresse:</div>
                <div class="text-gray-700">
                    {{ $country ? $country->name . ' ' . $country->code : 'Pays non disponible' }},
                    {{ $recruiter->commune ?? 'Commune non disponible' }}
                </div>
            </div>
        </div>

    </div>

    <script>
        const DATA_SET_VERTICAL_BAR_CHART_1 = [68.106, 26.762, 94.255, 72.021, 74.082, 64.923, 85.565, 32.432, 54.664,
            87.654, 43.013, 91.443
        ];

        const labels_vertical_bar_chart = ['January', 'February', 'Mart', 'April', 'May', 'Jun', 'July', 'August',
            'September', 'October', 'November', 'December'
        ];

        const dataVerticalBarChart = {
            labels: labels_vertical_bar_chart,
            datasets: [{
                label: 'Revenue',
                data: DATA_SET_VERTICAL_BAR_CHART_1,
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
            }]
        };
        const configVerticalBarChart = {
            type: 'bar',
            data: dataVerticalBarChart,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'Last 12 Months'
                    }
                }
            },
        };

        var verticalBarChart = new Chart(
            document.getElementById('verticalBarChart'),
            configVerticalBarChart
        );
    </script>
</x-admin-layout>
