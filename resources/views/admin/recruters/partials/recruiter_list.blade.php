<table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
            <th scope="col" class="px-4 py-2">Nom et Prénom</th>
            <th scope="col" class="px-4 py-2">Email</th>
            <th scope="col" class="px-4 py-2">Nombre d'offres</th>
            <th scope="col" class="px-4 py-2">Nombre d'offres conclues</th>
            <th scope="col" class="px-4 py-2">Date de création</th>
            <th scope="col" class="px-4 py-2">Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($recruiters as $recruiter)
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                    {{ $recruiter->fullname() }}
                </td>
                <td class="px-4 py-2 text-gray-500 dark:text-gray-400">
                    {{ $recruiter->email }}
                </td>
                <td class="px-4 py-2 text-gray-500 dark:text-gray-400">
                    {{ $recruiter->offers_count ?? 'N/A' }}
                </td>
                <td class="px-4 py-2 text-gray-500 dark:text-gray-400">
                    {{ $recruiter->concluded_offers_count ?? 'N/A' }}
                </td>
                <td class="px-4 py-2 text-gray-500 dark:text-gray-400">
                    {{ $recruiter->created_at ? $recruiter->created_at->format('d/m/Y') : 'N/A' }}
                </td>
                <td class="px-4 py-2 text-right text-sm font-medium">
                    <a href="{{ route('admin.recruiters.show', $recruiter->id) }}"
                        class="text-primary border border-primary p-1 dark:text-primary-light focus:outline-none mx-1 text-xs">
                        Voir les détails
                    </a>
                    @if ($recruiter->is_suspend == true)
                        <a href="{{ route('admin.recruiters.unsuspend', $recruiter->id) }}"
                            class="text-green-500 p-1 border border-green-500 focus:outline-none mx-1 text-xs">
                            Réactiver
                        </a>
                    @else
                        <a href="{{ route('admin.recruiters.suspend', $recruiter->id) }}"
                            class="text-red-500 p-1 border border-red-500 focus:outline-none mx-1 text-xs">
                            Suspendre
                        </a>
                    @endif
                </td>
            </tr>
        @endforeach
    </tbody>
</table>

<div class="w-full flex justify-center items-center my-4">
    {{ $recruiters->links() }}
</div>
