<x-admin-layout>
    <x-slot name="title">
        Dashboard
    </x-slot>
    <div class="flex px-4 py-8 md:p-10 items-center h-[70vh]">

        <div class="w-full bg-white rounded-lg dark:bg-gray-800 dark:border-gray-700">
            <div class="p-4 bg-white rounded-lg md:p-8 dark:bg-gray-800" id="stats" role="tabpanel"
                aria-labelledby="stats-tab">
                <dl
                    class="grid max-w-screen-xl grid-cols-2 gap-8 p-4 mx-auto text-gray-900 sm:grid-cols-3 xl:grid-cols-4 dark:text-white sm:p-8">
                    <div class="flex flex-col items-center justify-center">
                        <dt class="mb-2 text-3xl font-extrabold">
                            @php
                                $role_id = \App\Models\Role::where('slug', 'candidate')->first()->id;
                                $recruiterCount = \App\Models\User::where('role_id', $role_id)->count();
                            @endphp
                            {{ $recruiterCount }}
                        </dt>
                        <dd class="text-gray-500 dark:text-gray-400">
                            Candidats
                        </dd>
                        <a href="{{ route('admin.candidates') }}"
                            class="mt-4 text-sm font-semibold text-primary dark:text-primary-light hover:underline focus:outline-none">
                            Voir les détails
                        </a>
                    </div>
                    <div class="flex flex-col items-center justify-center">
                        <dt class="mb-2 text-3xl font-extrabold">
                            @php
                                $role_id = \App\Models\Role::where('slug', 'recruter')->first()->id;
                                $recruiterCount = \App\Models\User::where('role_id', $role_id)->count();
                            @endphp
                            {{ $recruiterCount }}
                        </dt>
                        <dd class="text-gray-500 dark:text-gray-400">Recruteurs</dd>
                        <a href="{{ route('admin.recruiters') }}"
                            class="mt-4 text-sm font-semibold text-primary dark:text-primary-light hover:underline focus:outline-none">
                            Voir les détails
                        </a>
                    </div>
                    <div class="flex flex-col items-center justify-center">
                        <dt class="mb-2 text-3xl font-extrabold">
                            @php
                                $activeProfessionCount = \App\Models\Profession::where('is_active', true)->count();
                            @endphp
                            {{ $activeProfessionCount }}
                        </dt>
                        <dd class="text-gray-500 dark:text-gray-400">Profession</dd>
                        <a href="{{ route('admin.profession') }}"
                            class="mt-4 text-sm font-semibold text-primary dark:text-primary-light hover:underline focus:outline-none">
                            Voir les détails
                        </a>
                    </div>

                    <div class="flex flex-col items-center justify-center">
                        <dt class="mb-2 text-3xl font-extrabold">
                            @php
                                $activeFieldActivityCount = \App\Models\FieldActivity::where(
                                    'is_active',
                                    true,
                                )->count();
                            @endphp
                            {{ $activeFieldActivityCount }}
                        </dt>
                        <dd class="text-gray-500 dark:text-gray-400">Domaines d'activité</dd>
                        <a href="{{ route('admin.activities') }}"
                            class="mt-4 text-sm font-semibold text-primary dark:text-primary-light hover:underline focus:outline-none">
                            Voir les détails
                            </button>
                    </div>

                </dl>
            </div>
        </div>
    </div>
</x-admin-layout>
