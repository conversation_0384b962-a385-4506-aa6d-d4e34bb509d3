<x-admin-layout>
    <x-slot name="title">Packages</x-slot>

    <div class="w-full flex items-center">
        <!-- Icône SVG -->
        <svg class="w-5 h-5 text-gray-500 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd"
                d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z"
                clip-rule="evenodd"></path>
        </svg>
        <!-- Titre -->
        <h1 class="text-3xl font-semibold text-gray-900 dark:text-white">Liste des packages</h1>
    </div>

    <div class="w-full flex justify-between items-center my-4">


        <div class="flex items-center">
            @if (request()->routeIs('admin.packages.search'))
                <a href="{{ route('admin.packages.index') }}"
                    class="block text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800 mr-2"
                    type="button">
                    Liste complète
                </a>
            @endif

            <!-- Ajouter package Modal -->
            <div id="authentication-modal-add" tabindex="-1" aria-hidden="true"
                class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-md max-h-full">
                    <!-- Modal content -->
                    <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                        <!-- Modal header -->
                        <div
                            class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                Ajouter un package
                            </h3>
                            <button type="button"
                                class="end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                                data-modal-hide="authentication-modal-add">
                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                </svg>
                                <span class="sr-only">Fermer</span>
                            </button>
                        </div>
                        <!-- Modal body -->
                        <div class="p-4 md:p-5">
                            <form class="space-y-4" method="POST" action="{{ route('admin.packages.store') }}">
                                @csrf
                                <div>
                                    <label for="name"
                                        class="block text-sm font-medium text-gray-700 dark:text-gray-400 text-left">
                                        Nom
                                    </label>
                                    <input type="text" id="name" name="name"
                                        class="block w-full p-3 mt-1 text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Ex: 1 SEMAINE" required />
                                </div>

                                <div>
                                    <label for="duration"
                                        class="block text-sm font-medium text-gray-700 dark:text-gray-400 text-left">
                                        Durée
                                    </label>
                                    <input type="text" id="duration" name="duration"
                                        class="block w-full p-3 mt-1 text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Ex: 1 semaine" required />
                                </div>

                                <div>
                                    <label for="price"
                                        class="block text-sm font-medium text-gray-700 dark:text-gray-400 text-left">
                                        Prix
                                    </label>
                                    <input type="number" step="0.01" id="price" name="price"
                                        class="block w-full p-3 mt-1 text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Ex: 99.00" required />
                                </div>


                                <div id="features">
                                    <div class="description-group flex items-center mb-2">
                                        <div class="flex-grow">
                                            <label for="features"
                                                class="block text-sm font-medium text-gray-900 dark:text-white">Fonctionnalités
                                                (une par ligne)</label>
                                            <textarea id="features" name="features[]"
                                                class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" id="add-features" class="text-sm text-blue-600 hover:underline">
                                    Ajouter
                                </button>

                                <div>
                                    <button type="submit"
                                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                        Ajouter
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bouton déclencheur du modal -->
            <button data-modal-target="authentication-modal-add" data-modal-toggle="authentication-modal-add"
                class="block text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                type="button">
                Ajouter un package
            </button>
        </div>
    </div>

    <div class="relative overflow-x-auto shadow-md sm:rounded-lg" id="packages-list">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">Nom</th>
                    <th scope="col" class="px-6 py-3">Prix</th>
                    <th scope="col" class="px-6 py-3">Description</th>
                    <th scope="col" class="px-6 py-3">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($packages as $package)
                    <tr
                        class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                        <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                            {{ $package->name }}
                        </td>

                        <td class="px-6 py-4">
                            {{ number_format($package->price, 2) }} CHF
                        </td>
                        <td class="px-6 py-4">
                            @if (!empty($package->features))
                                <ul class="list-disc list-inside">
                                    @foreach ($package->features as $feature)
                                        <li>{{ $feature }}</li>
                                    @endforeach
                                </ul>
                            @else
                                Aucune description
                            @endif
                        </td>
                        <td class="px-6 py-4 text-right text-sm font-medium">
                            <div class="flex justify-end items-center space-x-2">
                                <!-- Modal toggle -->
                                <button data-modal-target="authentication-modal-{{ $package->id }}"
                                    data-modal-toggle="authentication-modal-{{ $package->id }}"
                                    class="block text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                    type="button">
                                    Modifier
                                </button>

                                <!-- Main modal -->
                                <!-- Main modal -->
                                <div id="authentication-modal-{{ $package->id }}" tabindex="-1" aria-hidden="true"
                                    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative p-4 w-full max-w-md max-h-full">
                                        <!-- Modal content -->
                                        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                                            <!-- Modal header -->
                                            <div
                                                class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                                    Modifier le package
                                                </h3>
                                                <button type="button"
                                                    class="end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                                                    data-modal-hide="authentication-modal-{{ $package->id }}">
                                                    <svg class="w-3 h-3" aria-hidden="true"
                                                        xmlns="http://www.w3.org/2000/svg" fill="none"
                                                        viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round"
                                                            stroke-linejoin="round" stroke-width="2"
                                                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Fermer</span>
                                                </button>
                                            </div>
                                            <!-- Modal body -->
                                            <div class="p-4 md:p-5">
                                                <form class="space-y-4" method="POST"
                                                    action="{{ route('admin.packages.update', $package->id) }}">
                                                    @csrf
                                                    @method('PUT')
                                                    <div>
                                                        <label for="name"
                                                            class="block text-sm font-medium text-gray-700 dark:text-gray-400 text-left">
                                                            Nom
                                                        </label>
                                                        <input type="text" id="name" name="name"
                                                            value="{{ $package->name }}"
                                                            class="block w-full p-3 mt-1 text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                                            required />
                                                    </div>

                                                    <div>
                                                        <label for="price"
                                                            class="block text-sm font-medium text-gray-700 dark:text-gray-400 text-left">
                                                            Prix
                                                        </label>
                                                        <input type="number" step="0.01" id="price"
                                                            name="price" value="{{ $package->price }}"
                                                            class="block w-full p-3 mt-1 text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                                            required />
                                                    </div>


                                                    <div id="features-container-{{ $package->id }}">
                                                        <label
                                                            class="block text-sm font-medium text-gray-900 dark:text-white">
                                                            Fonctionnalités
                                                        </label>
                                                        @if (!empty($package->features))
                                                            @foreach ($package->features as $index => $feature)
                                                                <div class="feature-group mt-2 flex items-center">
                                                                    <input type="text" name="features[]"
                                                                        value="{{ $feature }}"
                                                                        class="block w-full p-2 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                                                    <button type="button"
                                                                        class="ml-2 text-red-500 hover:text-red-700 focus:outline-none"
                                                                        onclick="this.closest('.feature-group').remove()">
                                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                                        </svg>
                                                                    </button>
                                                                </div>
                                                            @endforeach
                                                        @else
                                                            <div class="feature-group mt-2 flex items-center">
                                                                <input type="text" name="features[]"
                                                                    class="block w-full p-2 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                                                <button type="button"
                                                                    class="ml-2 text-red-500 hover:text-red-700 focus:outline-none"
                                                                    onclick="this.closest('.feature-group').remove()">
                                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                                    </svg>
                                                                </button>
                                                            </div>
                                                        @endif
                                                    </div>

                                                    <button type="button"
                                                        onclick="addFeatureField('{{ $package->id }}')"
                                                        class="text-sm text-blue-600 hover:underline">
                                                        Ajouter une fonctionnalité
                                                    </button>

                                                    <div>
                                                        <button type="submit"
                                                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                                            Enregistrer
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <script>
                                    function addFeatureField(packageId) {
                                        const container = document.getElementById(`features-container-${packageId}`);
                                        const newFeatureField = document.createElement('div');
                                        newFeatureField.className = 'feature-group mt-2 flex items-center';
                                        newFeatureField.innerHTML = `
            <input type="text" name="features[]"
                class="block w-full p-2 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
            <button type="button"
                class="ml-2 text-red-500 hover:text-red-700 focus:outline-none"
                onclick="this.closest('.feature-group').remove()">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        `;
                                        container.appendChild(newFeatureField);
                                    }
                                </script>

                                <form action="{{ route('admin.packages.destroy', $package->id) }}" method="POST">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit"
                                        class="text-red-500 p-2 border border-red-500 focus:outline-none w-36">
                                        Supprimer
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    @if (session('success'))
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script>
            Swal.fire({
                title: 'Succès',
                text: "{{ session('success') }}",
                icon: 'success',
                confirmButtonText: 'OK'
            });
        </script>
    @endif

    @if (session('error'))
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script>
            Swal.fire({
                title: 'Erreur',
                text: "{{ session('error') }}",
                icon: 'error',
                confirmButtonText: 'OK'
            });
        </script>
    @endif

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const addFeaturesButton = document.getElementById("add-features");
            const featuresContainer = document.getElementById("features");

            addFeaturesButton.addEventListener("click", function(e) {
                e.preventDefault();

                // Créer un nouveau groupe de description
                const newFeaturesGroup = document.createElement("div");
                newFeaturesGroup.classList.add("feature-group", "flex", "items-center", "mt-2");

                // Créer le label et la zone de texte pour la nouvelle description
                const input = document.createElement("input");
                input.setAttribute("type", "text");
                input.setAttribute("name", "features[]");
                input.setAttribute("placeholder", "Nouvelle fonctionnalité");
                input.classList.add("block", "w-full", "p-2", "text-sm", "border", "rounded-lg",
                    "focus:ring-blue-500", "dark:bg-gray-700", "dark:border-gray-600", "dark:text-white"
                );

                const removeButton = document.createElement("button");
                removeButton.setAttribute("type", "button");
                removeButton.classList.add("ml-2", "text-red-500", "hover:text-red-700", "focus:outline-none");
                removeButton.innerHTML = `
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                `;
                removeButton.addEventListener("click", function() {
                    newFeaturesGroup.remove();
                });

                newFeaturesGroup.appendChild(input);
                newFeaturesGroup.appendChild(removeButton);

                // Ajouter le groupe de description dans le container
                featuresContainer.appendChild(newFeaturesGroup);
            });
        });
    </script>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Gestion de l'ajout de fonctionnalités
            const addFeaturesButton = document.getElementById("add-features");
            if (addFeaturesButton) {
                addFeaturesButton.addEventListener("click", function(e) {
                    e.preventDefault();
                    // ... (votre code existant pour l'ajout de fonctionnalités)
                });
            }

            // Confirmation de suppression
            document.querySelectorAll('form[action*="destroy"]').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const packageName = this.closest('tr').querySelector('td:first-child')
                        .textContent.trim();

                    Swal.fire({
                        title: 'Confirmer la suppression',
                        html: `Voulez-vous vraiment supprimer le package <strong>${packageName}</strong> ?`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Oui, supprimer',
                        cancelButtonText: 'Annuler',
                        reverseButtons: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Soumission du formulaire après confirmation
                            this.submit();
                        }
                    });
                });
            });
        });
    </script>


</x-admin-layout>
