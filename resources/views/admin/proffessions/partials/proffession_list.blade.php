<table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
            <th scope="col" class="px-6 py-3">Nom et Prénom</th>
            <th scope="col" class="px-6 py-3">Email</th>
            <th scope="col" class="px-6 py-3">Nombre d'offres</th>
            <th scope="col" class="px-6 py-3">Nombre d'offres conclues</th>
            <th scope="col" class="px-6 py-3">Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($proffessions as $proffession)
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                    {{ $proffession->name }}
                </td>
                <td class="px-6 py-4 text-gray-500 dark:text-gray-400">
                    {{ $proffession->email }}
                </td>
                <td class="px-6 py-4 text-gray-500 dark:text-gray-400">
                    {{ $proffession->offers_count ?? 'N/A' }}
                </td>
                <td class="px-6 py-4 text-gray-500 dark:text-gray-400">
                    {{ $proffession->concluded_offers_count ?? 'N/A' }}
                </td>
                <td class="px-6 py-4 text-right text-sm font-medium">
                    <a href="{{ route('admin.profession.show', $proffession->id) }}"
                        class="text-primary border border-primary p-2 dark:text-primary-light focus:outline-none mx-2">
                        Voir les détails
                    </a>
                    <a href="#" class="text-red-500 p-2 border border-red-500 focus:outline-none">
                        Suspendre
                    </a>
                </td>
            </tr>
        @endforeach
    </tbody>
</table>

<div class="w-full flex justify-end items-center my-4">
    {{ $proffessions->links() }}
</div>
