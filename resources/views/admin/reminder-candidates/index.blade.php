<x-admin-layout>
    <x-slot name="title">Gestion des Rappels Candidats</x-slot>

    <div class="container mx-auto px-4 py-8">
        <!-- En-tête avec statistiques -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Gestion des Rappels Candidats</h1>
            
            <!-- Statistiques -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total candidats</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $stats['total'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Réponses positives</p>
                            <p class="text-2xl font-bold text-green-600">{{ $stats['yes_count'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-red-100 rounded-lg">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Réponses négatives</p>
                            <p class="text-2xl font-bold text-red-600">{{ $stats['no_count'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-gray-100 rounded-lg">
                            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Sans réponse</p>
                            <p class="text-2xl font-bold text-gray-600">{{ $stats['no_response_count'] }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtres et recherche -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6">
                <form method="GET" action="{{ route('admin.reminder-candidates.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Recherche -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Rechercher</label>
                        <input type="text" 
                               id="search" 
                               name="search" 
                               value="{{ request('search') }}"
                               placeholder="Nom, prénom ou email..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- Filtre par statut -->
                    <div>
                        <label for="response_status" class="block text-sm font-medium text-gray-700 mb-2">Statut de réponse</label>
                        <select id="response_status" 
                                name="response_status" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Tous les statuts</option>
                            <option value="yes" {{ request('response_status') == 'yes' ? 'selected' : '' }}>Oui</option>
                            <option value="no" {{ request('response_status') == 'no' ? 'selected' : '' }}>Non</option>
                            <option value="no_response" {{ request('response_status') == 'no_response' ? 'selected' : '' }}>Pas de réponse</option>
                        </select>
                    </div>

                    <!-- Filtre par date -->
                    <div>
                        <label for="last_reminder_date" class="block text-sm font-medium text-gray-700 mb-2">Date du dernier rappel</label>
                        <input type="date" 
                               id="last_reminder_date" 
                               name="last_reminder_date" 
                               value="{{ request('last_reminder_date') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- Boutons d'action -->
                    <div class="flex items-end space-x-2">
                        <button type="submit" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Filtrer
                        </button>
                        <a href="{{ route('admin.reminder-candidates.index') }}" 
                           class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            Réinitialiser
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Actions groupées -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6">
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-4">
                        <button id="select-all" 
                                class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            Tout sélectionner
                        </button>
                        <button id="bulk-email-btn" 
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed" 
                                disabled>
                            Envoyer email groupé
                        </button>
                        <span id="selected-count" class="text-sm text-gray-600">0 candidat(s) sélectionné(s)</span>
                    </div>
                    
                    <div class="flex space-x-2">
                        <a href="{{ route('admin.reminder-candidates.export', request()->query()) }}" 
                           class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Exporter CSV
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tableau des rappels -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="select-all-checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Candidat
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Email
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Statut
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Dernier rappel
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Nb rappels
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Prochain rappel
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($reminders as $reminder)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" 
                                           class="reminder-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" 
                                           value="{{ $reminder->id }}">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ $reminder->user->civility->first_name ?? '' }} {{ $reminder->user->civility->last_name ?? '' }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $reminder->user->email }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $reminder->status_badge_color }}">
                                        {{ $reminder->formatted_response_status }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $reminder->last_reminder_sent_at ? $reminder->last_reminder_sent_at->format('d/m/Y H:i') : '-' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $reminder->reminder_count ?? 0 }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $reminder->next_reminder_date ? $reminder->next_reminder_date->format('d/m/Y') : '-' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button class="send-individual-email text-blue-600 hover:text-blue-900" 
                                                data-reminder-id="{{ $reminder->id }}"
                                                data-candidate-name="{{ $reminder->user->firstname() }}"
                                                data-candidate-email="{{ $reminder->user->email }}">
                                            Envoyer email
                                        </button>
                                        <select class="update-status text-xs border-gray-300 rounded" 
                                                data-reminder-id="{{ $reminder->id }}">
                                            <option value="no_response" {{ $reminder->response_status == 'no_response' ? 'selected' : '' }}>Pas de réponse</option>
                                            <option value="yes" {{ $reminder->response_status == 'yes' ? 'selected' : '' }}>Oui</option>
                                            <option value="no" {{ $reminder->response_status == 'no' ? 'selected' : '' }}>Non</option>
                                        </select>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                    Aucun rappel candidat trouvé.
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <div class="mt-6">
            {{ $reminders->appends(request()->query())->links() }}
        </div>
    </div>

    <!-- Modal d'envoi d'email individuel -->
    <div id="individual-email-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Envoyer un email</h3>
                <form id="individual-email-form">
                    <input type="hidden" id="individual-reminder-id">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Destinataire</label>
                        <p id="individual-recipient" class="text-sm text-gray-600"></p>
                    </div>

                    <div class="mb-4">
                        <label for="individual-subject" class="block text-sm font-medium text-gray-700 mb-2">Sujet</label>
                        <input type="text" 
                               id="individual-subject" 
                               name="subject" 
                               required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div class="mb-4">
                        <label for="individual-message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                        <textarea id="individual-message" 
                                  name="message" 
                                  rows="6" 
                                  required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" 
                                id="cancel-individual-email" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Annuler
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Envoyer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal d'envoi d'email groupé -->
    <div id="bulk-email-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Envoyer un email groupé</h3>
                <form id="bulk-email-form">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Destinataires</label>
                        <p id="bulk-recipients" class="text-sm text-gray-600"></p>
                    </div>

                    <div class="mb-4">
                        <label for="bulk-subject" class="block text-sm font-medium text-gray-700 mb-2">Sujet</label>
                        <input type="text" 
                               id="bulk-subject" 
                               name="subject" 
                               required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div class="mb-4">
                        <label for="bulk-message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                        <textarea id="bulk-message" 
                                  name="message" 
                                  rows="6" 
                                  required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" 
                                id="cancel-bulk-email" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Annuler
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                            Envoyer à tous
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Gestion des rappels candidats - Interface Admin
        document.addEventListener('DOMContentLoaded', function() {
            const selectedReminders = new Set();

            // Gestion des checkboxes
            const selectAllCheckbox = document.getElementById('select-all-checkbox');
            const reminderCheckboxes = document.querySelectorAll('.reminder-checkbox');
            const selectedCountSpan = document.getElementById('selected-count');
            const bulkEmailBtn = document.getElementById('bulk-email-btn');

            function updateSelectedCount() {
                const checkedBoxes = document.querySelectorAll('.reminder-checkbox:checked');
                const count = checkedBoxes.length;
                selectedCountSpan.textContent = `${count} candidat(s) sélectionné(s)`;
                bulkEmailBtn.disabled = count === 0;

                // Mettre à jour le Set
                selectedReminders.clear();
                checkedBoxes.forEach(cb => selectedReminders.add(cb.value));
            }

            function updateSelectAllCheckbox() {
                if (selectAllCheckbox && reminderCheckboxes.length > 0) {
                    const checkedCount = document.querySelectorAll('.reminder-checkbox:checked').length;
                    selectAllCheckbox.checked = checkedCount === reminderCheckboxes.length;
                    selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < reminderCheckboxes.length;
                }
            }

            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    reminderCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateSelectedCount();
                });
            }

            reminderCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateSelectedCount();
                    updateSelectAllCheckbox();
                });
            });

            // Modal d'email individuel
            const individualEmailModal = document.getElementById('individual-email-modal');
            const individualEmailForm = document.getElementById('individual-email-form');

            document.querySelectorAll('.send-individual-email').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const reminderId = this.dataset.reminderId;
                    const candidateName = this.dataset.candidateName;
                    const candidateEmail = this.dataset.candidateEmail;

                    document.getElementById('individual-reminder-id').value = reminderId;
                    document.getElementById('individual-recipient').textContent = `${candidateName} (${candidateEmail})`;
                    document.getElementById('individual-subject').value = 'Rappel - Recherche d\'emploi';
                    document.getElementById('individual-message').value = `Bonjour ${candidateName},\n\nNous espérons que vous allez bien. Nous souhaitions prendre de vos nouvelles concernant votre recherche d'emploi.\n\nN'hésitez pas à nous contacter si vous avez des questions.\n\nCordialement,\nL'équipe Cyclone Placement`;

                    individualEmailModal.classList.remove('hidden');
                    document.body.style.overflow = 'hidden';
                });
            });

            document.getElementById('cancel-individual-email')?.addEventListener('click', function() {
                individualEmailModal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            });

            if (individualEmailForm) {
                individualEmailForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const reminderId = document.getElementById('individual-reminder-id').value;
                    const submitBtn = this.querySelector('button[type="submit"]');

                    try {
                        submitBtn.disabled = true;
                        submitBtn.textContent = 'Envoi en cours...';

                        const response = await fetch(`/admin/reminder-candidates/${reminderId}/send-email`, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        });

                        const data = await response.json();

                        if (data.success) {
                            showNotification(data.message, 'success');
                            individualEmailModal.classList.add('hidden');
                            document.body.style.overflow = 'auto';
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showNotification('Erreur: ' + data.message, 'error');
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        showNotification('Une erreur est survenue', 'error');
                    } finally {
                        submitBtn.disabled = false;
                        submitBtn.textContent = 'Envoyer';
                    }
                });
            }

            // Modal d'email groupé
            const bulkEmailModal = document.getElementById('bulk-email-modal');
            const bulkEmailForm = document.getElementById('bulk-email-form');

            if (bulkEmailBtn) {
                bulkEmailBtn.addEventListener('click', function() {
                    if (selectedReminders.size === 0) return;

                    document.getElementById('bulk-recipients').textContent = `${selectedReminders.size} candidat(s) sélectionné(s)`;
                    document.getElementById('bulk-subject').value = 'Rappel - Recherche d\'emploi';
                    document.getElementById('bulk-message').value = 'Bonjour,\n\nNous espérons que vous allez bien. Nous souhaitions prendre de vos nouvelles concernant votre recherche d\'emploi.\n\nN\'hésitez pas à nous contacter si vous avez des questions.\n\nCordialement,\nL\'équipe Cyclone Placement';

                    bulkEmailModal.classList.remove('hidden');
                    document.body.style.overflow = 'hidden';
                });
            }

            document.getElementById('cancel-bulk-email')?.addEventListener('click', function() {
                bulkEmailModal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            });

            if (bulkEmailForm) {
                bulkEmailForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    formData.append('reminder_ids', JSON.stringify(Array.from(selectedReminders)));
                    const submitBtn = this.querySelector('button[type="submit"]');

                    try {
                        submitBtn.disabled = true;
                        submitBtn.textContent = 'Envoi en cours...';

                        const response = await fetch('/admin/reminder-candidates/send-bulk-emails', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        });

                        const data = await response.json();

                        if (data.success) {
                            showNotification(data.message, 'success');
                            if (data.errors && data.errors.length > 0) {
                                showNotification('Erreurs: ' + data.errors.join('\n'), 'warning');
                            }
                            bulkEmailModal.classList.add('hidden');
                            document.body.style.overflow = 'auto';
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showNotification('Erreur: ' + data.message, 'error');
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        showNotification('Une erreur est survenue', 'error');
                    } finally {
                        submitBtn.disabled = false;
                        submitBtn.textContent = 'Envoyer à tous';
                    }
                });
            }

            // Mise à jour du statut
            document.querySelectorAll('.update-status').forEach(select => {
                select.addEventListener('change', async function() {
                    const reminderId = this.dataset.reminderId;
                    const status = this.value;

                    try {
                        const response = await fetch(`/admin/reminder-candidates/${reminderId}/update-status`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({ response_status: status })
                        });

                        const data = await response.json();

                        if (data.success) {
                            showNotification('Statut mis à jour avec succès', 'success');
                            setTimeout(() => location.reload(), 500);
                        } else {
                            showNotification('Erreur: ' + data.message, 'error');
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        showNotification('Une erreur est survenue', 'error');
                    }
                });
            });

            // Fonction pour afficher les notifications
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${getNotificationClasses(type)}`;
                notification.textContent = message;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 5000);
            }

            function getNotificationClasses(type) {
                switch (type) {
                    case 'success':
                        return 'bg-green-500 text-white';
                    case 'error':
                        return 'bg-red-500 text-white';
                    case 'warning':
                        return 'bg-yellow-500 text-white';
                    default:
                        return 'bg-blue-500 text-white';
                }
            }

            // Initialisation
            updateSelectedCount();
        });
    </script>
    @endpush
</x-admin-layout>
