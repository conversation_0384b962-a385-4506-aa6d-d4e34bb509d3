<x-admin-layout>
    <x-slot name="title">
        Ajouter Webhook sur Stripe
    </x-slot>

    <!-- Bouton d'ajout de produit -->
    <div class="w-full flex justify-between items-center my-4">
        <div class="w-full flex items-center space-x-2">
            <svg fill="currentColor"
                class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path
                    d="M12 2C9.243 2 7 4.243 7 7v7H4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-5a2 2 0 0 0-2-2h-3V7c0-2.757-2.243-5-5-5zM5 14h14v4H5v-4z" />
            </svg>
            <h1 class="text-3xl font-semibold text-gray-900 dark:text-white mb-0">
                Liste des Webhooks
            </h1>
        </div>

        <!-- Bouton d'ajout de webhook (masqué si un webhook existe déjà) -->
        @if ($webhooks->isEmpty())
            <button data-modal-target="product-modal" data-modal-toggle="product-modal"
                class="block text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5 transition duration-200 ease-in-out transform hover:scale-105 whitespace-nowrap">
                Ajouter Webhook sur Stripe
            </button>
        @else
            <p class="text-sm text-gray-500">Un seul webhook peut être ajouté.</p>
        @endif
    </div>

    <!-- Modal de création de webhook -->
    <div id="product-modal" tabindex="-1" aria-hidden="true"
        class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow-lg dark:bg-gray-800">
                <div class="flex items-center justify-between p-4 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Ajouter Webhook sur Stripe</h3>
                    <button type="button" data-modal-hide="product-modal"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg w-8 h-8">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M1 1l6 6m0 0l6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                    </button>
                </div>
                <div class="p-4">
                    <form class="space-y-4" id="add-product-form" method="POST"
                        action="{{ route('stripe-webhook.create') }}">
                        @csrf

                        <div>
                            <label for="url" class="block text-sm font-medium text-gray-900 dark:text-white">Votre
                                Domaine</label>
                            <input type="text" id="url" name="url" required
                                class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                placeholder="ex: monsite.fr" />
                            <!-- Texte d'aide -->
                            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                                Entrez votre domaine sans "www" et sans "https://". Exemple : monsite.fr
                            </p>
                        </div>

                        <button type="submit"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5">
                            Ajouter
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des webhooks Stripe -->
    <div class="w-full overflow-x-auto shadow-lg rounded-lg bg-white">
        <table class="min-w-full table-auto text-sm text-gray-500">
            <thead>
                <tr class="bg-gray-100 text-gray-700">
                    <th class="px-6 py-3 text-left">Status</th>
                    <th class="px-6 py-3 text-left">Webhook ID</th>
                    <th class="px-6 py-3 text-left">Secret</th>
                    <th class="px-6 py-3 text-left">URL</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white">
                @forelse ($webhooks as $webhook)
                    <tr>
                        <td class="px-6 py-3">{{ $webhook->status }}</td>
                        <td class="px-6 py-3">{{ $webhook->webhook_id }}</td>
                        <td class="px-6 py-3">{{ $webhook->secret }}</td>
                        <td class="px-6 py-3">{{ $webhook->url }}</td>
                        <td class="px-6 py-3">
                            <form action="{{ route('stripe-webhook.delete') }}" method="POST" id="deleteWebhookForm">
                                @csrf
                                <input type="hidden" name="webhook_id" value="{{ $webhook->webhook_id }}">
                                <button type="button" id="deleteButton"
                                    class="text-red-500 p-2 border border-red-500 focus:outline-none">
                                    Supprimer
                                </button>
                            </form>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5" class="px-6 py-3 text-center text-gray-500">
                            Aucun webhook n'a été créé.
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Intégration de SweetAlert -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        @if (session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Webhook créé avec succès',
                text: '{{ session('success') }}'
            });
        @elseif (session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Erreur',
                text: '{{ session('error') }}'
            });
        @endif
    </script>

    <!-- Script pour la suppression de webhook -->
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const deleteButton = document.getElementById("deleteButton");
            const deleteForm = document.getElementById("deleteWebhookForm");

            deleteButton.addEventListener("click", function(e) {
                e.preventDefault(); // Empêche la soumission immédiate du formulaire

                // Afficher le popup de confirmation
                Swal.fire({
                    title: 'Êtes-vous sûr ?',
                    text: "Cette action est irréversible et supprimera définitivement le webhook.",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Oui, supprimer !',
                    cancelButtonText: 'Annuler'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Si l'utilisateur confirme, soumettre le formulaire
                        deleteForm.submit();
                    }
                });
            });
        });
    </script>
</x-admin-layout>
