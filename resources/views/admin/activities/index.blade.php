<x-admin-layout>
    <x-slot name="title">
        Activités
    </x-slot>

    <div class="w-full flex items-center">
        <!-- Icône SVG -->
        <svg fill="currentColor"
            class="w-6 h-6 text-gray-500 dark:text-gray-400"
            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <path
                d="M10 0C4.477 0 0 4.477 0 10s4.477 10 10 10 10-4.477 10-10S15.523 0 10 0Zm0 18.75c-4.688 0-8.75-3.063-8.75-8.75S5.312 1.25 10 1.25 18.75 4.312 18.75 10 14.688 18.75 10 18.75ZM10 3.75c-3.75 0-6.25 2.5-6.25 6.25 0 3.75 2.5 6.25 6.25 6.25 3.75 0 6.25-2.5 6.25-6.25 0-3.75-2.5-6.25-6.25-6.25Z">
            </path>
        </svg>
        <!-- Titre -->
        <h1 class="text-3xl font-semibold text-gray-900 dark:text-white ml-2">Domaines d'activités</h1>
    </div>


    <div class="w-full flex justify-between items-center my-4">
        <form class="relative w-96" action="{{ route('admin.search-activities') }}" method="GET">
            <label for="default-search"
                class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Rechercher</label>
            <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                    <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                    </svg>
                </div>
                <input type="search" id="default-search" name="query"
                    class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    placeholder="Rechercher des activités..." required />
                <button type="submit"
                    class="text-white absolute end-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Rechercher</button>
            </div>
        </form>
        <div class="flex items-center">
            <!-- Bouton affiché uniquement si la route correspond -->
            @if (request()->routeIs('admin.search-activities') || request()->routeIs('admin.search-activities'))
                <a href="{{ route('admin.activities') }}"
                    class="block text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800 mr-2"
                    type="button">
                    Listes des Activités
                </a>
            @endif

            <button id="dropdownDefaultButton" data-dropdown-toggle="dropdown"
                class="text-white md:mx-3 bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                type="button">
                Filtres
                <svg class="w-2.5 h-2.5 ms-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 10 6">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m1 1 4 4 4-4" />
                </svg>
            </button>

            <!-- Dropdown menu -->
            <div id="dropdown"
                class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                    <li>
                        <a href="{{ route('admin.activities') }}"
                            class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                            Tout</a>
                    </li>
                    <li>
                        <a href="{{ route('admin.activities', ['f' => 'active']) }}"
                            class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                            Activés
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('admin.activities', ['f' => 'inactive']) }}"
                            class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                            Désactivés
                        </a>
                    </li>

                </ul>
            </div>

            <button data-modal-target="authentication-modal" data-modal-toggle="authentication-modal"
                class="block text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                type="button">
                Ajouter un domaine d'activité
            </button>


            <!-- Main modal -->
            <div id="authentication-modal" tabindex="-1" aria-hidden="true"
                class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-md max-h-full">
                    <!-- Modal content -->
                    <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                        <!-- Modal header -->
                        <div
                            class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                Ajouter un domaine d'activité
                            </h3>
                            <button type="button"
                                class="end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                                data-modal-hide="authentication-modal">
                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                </svg>
                                <span class="sr-only">Close modal</span>
                            </button>
                        </div>
                        <!-- Modal body -->
                        <div class="p-4 md:p-5">
                            <form class="space-y-4" id="add-activity-form">
                                @csrf
                                <div>
                                    <label for="name"
                                        class="block text-sm font-medium text-gray-900 dark:text-white">
                                        Nom de l'activité
                                    </label>
                                    <input type="text" id="name" name="name" required
                                        class="block w-full p-3 mt-1 text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" />
                                </div>

                                <div class="flex items-center justify-between">
                                    <button type="submit"
                                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                        Ajouter
                                    </button>
                                </div>
                            </form>
                        </div>

                    </div>
                </div>
            </div>

            <!-- Overlay (fond semi-transparent) -->
            <div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 hidden z-40"></div>

            <div id="edit-activity-modal" tabindex="-1" aria-hidden="true"
                class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 flex justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-md max-h-full">
                    <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                        <!-- Votre contenu du modal -->
                        <div class="flex items-center justify-between p-4 border-b rounded-t dark:border-gray-600">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                Modifier une activité
                            </h3>
                            <button type="button"
                                class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto dark:hover:bg-gray-600 dark:hover:text-white"
                                onclick="closeEditModall()">
                                <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="M1 1l6 6m0 0l6 6M7 7l6-6M7 7L1 13" />
                                </svg>
                                <span class="sr-only">Fermer</span>
                            </button>
                        </div>
                        <div class="p-4">
                            <form id="edit-activity-form">
                                @csrf
                                @method('PUT')
                                <input type="hidden" id="edit-activity-id" name="id">
                                <div>
                                    <label for="edit-name"
                                        class="block text-sm font-medium text-gray-900 dark:text-white">
                                        Nom de l'activité
                                    </label>
                                    <input type="text" id="edit-name" name="name" required
                                        class="block w-full p-3 mt-1 text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" />
                                </div>
                                <button type="submit"
                                    class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2 mt-4 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                    Enregistrer
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>




        </div>
    </div>

    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        Nom de l'Activité
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Slug
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Statut
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($activities as $activity)
                    <tr
                        class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                        <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                            {{ $activity->name }}
                        </td>
                        <td class="px-6 py-4 text-gray-500 dark:text-gray-400">
                            {{ $activity->slug }}
                        </td>
                        <td class="px-6 py-4">
                            <button onclick="toggleActivity('{{ $activity->id }}')"
                                class="bg-{{ $activity->is_active ? 'green' : 'gray' }}-500 text-white font-bold py-2 px-4 rounded"
                                id="activity-button-{{ $activity->id }}">
                                {{ $activity->is_active ? 'Actif' : 'Inactif' }}
                            </button>
                        </td>



                        <td class="px-6 py-4 text-right text-sm font-medium">
                            <button
                                class="text-primary border border-primary p-2 dark:text-primary-light focus:outline-none mx-2 edit-button"
                                data-activity-id="{{ $activity->id }}" data-activity-name="{{ $activity->name }}">
                                Modifier
                            </button>

                            <form action="{{ route('admin.delete-activities', $activity->id) }}" method="POST"
                                class="inline delete-form" data-activity-name="{{ $activity->name }}">
                                @csrf
                                @method('DELETE')
                                <button type="button"
                                    class="text-red-500 p-2 border border-red-500 focus:outline-none delete-button">
                                    Supprimer
                                </button>
                            </form>
                        </td>


                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <!-- Centrer les liens de pagination -->
    <div class="mt-4 flex justify-center">
        {{ $activities->links() }}
    </div>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.querySelectorAll('.delete-button').forEach(button => {
            button.addEventListener('click', function() {
                const form = this.closest('.delete-form');
                const activityName = form.getAttribute('data-activity-name');

                Swal.fire({
                    title: 'Êtes-vous sûr ?',
                    text: `Vous êtes sur le point de supprimer l'activité "${activityName}". Cette action est irréversible.`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Oui, supprimer !',
                    cancelButtonText: 'Annuler'
                }).then((result) => {
                    if (result.isConfirmed) {
                        form.submit(); // Soumet le formulaire si l'utilisateur confirme
                    }
                });
            });
        });
    </script>

    <script>
        document.getElementById('add-activity-form').addEventListener('submit', function(event) {
            event.preventDefault(); // Empêche le rechargement de la page

            // Récupération des données du formulaire
            let formData = new FormData(this);

            // Envoi AJAX
            fetch("{{ route('admin.add-activities') }}", {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Succès',
                            text: data.message,
                        }).then(() => {
                            location
                                .reload(); // Recharge la page pour voir la nouvelle activité dans la liste
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Erreur',
                            text: data.message ||
                                'Une erreur est survenue lors de l\'ajout de l\'activité.',
                        });
                    }
                })
                .catch(error => {
                    Swal.fire({
                        icon: 'error',
                        title: 'Erreur',
                        text: 'Une erreur est survenue. Veuillez réessayer.',
                    });
                    console.error('Erreur:', error);
                });
        });
    </script>

    <script>
        // Fonction pour afficher le modal et l'overlay
        function openEditModal() {
            document.getElementById('overlay').classList.remove('hidden');
            document.getElementById('edit-activity-modal').classList.remove('hidden');
        }

        // Fonction pour masquer le modal et l'overlay
        function closeEditModall() {
            document.getElementById('overlay').classList.add('hidden');
            document.getElementById('edit-activity-modal').classList.add('hidden');
            console.log('Modal et overlay fermés'); // Vérifiez si cette ligne s'exécute
        }

        // Associez le bouton de modification pour ouvrir le modal
        document.querySelectorAll('.edit-button').forEach(button => {
            button.addEventListener('click', () => {
                // Mettez à jour les champs du modal ici, si nécessaire
                const activityId = button.getAttribute('data-activity-id');
                const activityName = button.getAttribute('data-activity-name');
                document.getElementById('edit-activity-id').value = activityId;
                document.getElementById('edit-name').value = activityName;

                openEditModal();
            });
        });
    </script>


    <script>
        // Ouvrir le modal de modification
        document.querySelectorAll('.edit-button').forEach(button => {
            button.addEventListener('click', function() {
                const activityId = this.getAttribute('data-activity-id');
                const activityName = this.getAttribute('data-activity-name');

                document.getElementById('edit-activity-id').value = activityId;
                document.getElementById('edit-name').value = activityName;

                document.getElementById('edit-activity-modal').classList.remove('hidden');
            });
        });

        // Fermer le modal de modification
        function closeEditModal() {
            document.getElementById('edit-activity-modal').classList.add('hidden');
        }
    </script>

    <script>
        document.getElementById('edit-activity-form').addEventListener('submit', function(event) {
            event.preventDefault();

            const activityId = document.getElementById('edit-activity-id').value;
            const name = document.getElementById('edit-name').value;

            fetch(`/admin/update-activite/${activityId}`, {
                    method: 'PUT',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: name
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Activité modifiée',
                            text: data.message,
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Erreur',
                            text: 'Une erreur est survenue lors de la mise à jour.',
                        });
                    }
                })
                .catch(error => {
                    Swal.fire({
                        icon: 'error',
                        title: 'Erreur',
                        text: 'Une erreur est survenue. Veuillez réessayer.',
                    });
                    console.error('Erreur:', error);
                });
        });
    </script>

    <script>
        function toggleActivity(activityId) {
            fetch(`/admin/activite/${activityId}/toggle`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}' // Ajoutez le token CSRF pour Laravel
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    const button = document.getElementById(`activity-button-${activityId}`);
                    if (data.is_active) {
                        button.classList.remove('bg-gray-500');
                        button.classList.add('bg-green-500');
                        button.textContent = 'Actif';
                    } else {
                        button.classList.remove('bg-green-500');
                        button.classList.add('bg-gray-500');
                        button.textContent = 'Inactif';
                    }
                })
                .catch(error => console.error('Error:', error));
        }
    </script>



</x-admin-layout>
