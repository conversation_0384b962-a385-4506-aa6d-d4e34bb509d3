<x-admin-layout>
    <x-slot name="title">Proffession</x-slot>


    <div class="w-full flex items-center">
        <!-- <PERSON><PERSON><PERSON> SVG -->
        <svg fill="currentColor"
            class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white mr-2"
            version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
            viewBox="0 0 512.002 512.002" xml:space="preserve">
            <g>
                <g>
                    <path
                        d="M317.143,444.752c-0.775-3.101-3.562-5.284-6.759-5.284H201.616c-3.197,0-5.985,2.182-6.759,5.283 c-7.876,31.546-12.131,48.723-14.469,58.702c-1.024,4.372,2.294,8.547,6.784,8.547h137.654c4.489,0,7.807-4.174,6.784-8.544 C329.291,493.545,325.046,476.404,317.143,444.752z">
                    </path>
                    <path
                        d="M189.856,352.876c-17.271-6.304-33.919-15.116-49.648-26.949c-2.971-2.236-7.162-1.776-9.547,1.077L37.381,438.586 c-24.366,29.146-3.791,73.415,35.301,73.415h78.805c3.29,0,6.124-2.306,6.812-5.524c3.059-14.314,13.917-57.282,35.927-145.371 C195.09,357.65,193.203,354.097,189.856,352.876z">
                    </path>
                    <path
                        d="M297.659,366.797c-0.887-3.55-4.37-5.81-7.973-5.168c-21.566,3.844-44.191,4.132-67.372,0 c-3.603-0.642-7.085,1.619-7.973,5.168c-3.908,15.641-7.404,29.622-10.523,42.097l-0.256,1.023 c-1.099,4.397,2.226,8.655,6.756,8.655h91.36c4.529,0,7.858-4.247,6.761-8.641c-0.116-0.467-0.234-0.933-0.351-1.404 C304.995,396.148,301.529,382.288,297.659,366.797z">
                    </path>
                    <path
                        d="M474.622,438.586l-93.281-111.583c-2.385-2.852-6.575-3.311-9.547-1.077c-15.087,11.349-31.622,20.37-49.649,26.949 c-3.348,1.222-5.234,4.774-4.37,8.232c21.979,87.96,32.931,131.32,35.928,145.396c0.684,3.21,3.527,5.497,6.808,5.497h78.809 C478.459,512,498.966,467.703,474.622,438.586z">
                    </path>
                    <path
                        d="M101.086,110.267c2.691-0.501-5.292-0.35,115.908-0.35c11.54,0,20.896,9.356,20.896,20.896v20.626 c0,1.994,1.616,3.608,3.608,3.608h29.004c1.992,0,3.608-1.616,3.608-3.608v-20.626c0-11.54,9.356-20.896,20.896-20.896 c120.981,0,113.202-0.156,115.91,0.35c2.797,0.521,5.019-2.296,3.948-4.933C357.801-35.119,154.222-35.103,97.14,105.332 C96.067,107.969,98.289,110.786,101.086,110.267z">
                    </path>
                    <path
                        d="M410.567,247.156c1.191-2.432-0.66-5.184-3.368-5.174c-0.025,0-0.05,0-0.077,0H295.007 c-11.54,0-20.896-9.356-20.896-20.896v-20.637c0-1.993-1.616-3.608-3.608-3.608h-29.004c-1.992,0-3.608,1.615-3.608,3.608v20.637 c0,11.54-9.356,20.896-20.896,20.896H104.879c-0.025,0-0.05,0-0.077,0c-2.708-0.01-4.56,2.742-3.368,5.174 C164.328,375.641,347.639,375.717,410.567,247.156z">
                    </path>
                    <path
                        d="M400.157,221.085c3.846,0,6.965-3.119,6.965-6.965v-76.341c0-3.846-3.119-6.965-6.965-6.965h-98.184 c-3.846,0-6.965,3.119-6.965,6.965v76.341c0,3.846,3.119,6.965,6.965,6.965H400.157z">
                    </path>
                    <path
                        d="M210.029,221.085c3.846,0,6.965-3.119,6.965-6.965v-76.341c0-3.846-3.119-6.965-6.965-6.965h-98.184 c-3.846,0-6.965,3.119-6.965,6.965v76.341c0,3.846,3.119,6.965,6.965,6.965H210.029z">
                    </path>
                </g>
            </g>
        </svg>
        <!-- Titre -->
        <h1 class="text-3xl font-semibold text-gray-900 dark:text-white">Liste des professions</h1>
    </div>

    <div class="w-full flex justify-between items-center my-4">
        <form id="search-form" class="relative w-96" action="{{ route('admin.proffession.search') }}" method="GET">
            <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                    <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                    </svg>
                </div>
                <input type="search" id="default-search" name="name"
                    class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    placeholder="Rechercher une profession..." required />
                <button type="submit"
                    class="text-white absolute end-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                    Rechercher
                </button>
            </div>
        </form>

        <div class="flex items-center">

            @if (request()->routeIs('admin.proffession.search') || request()->routeIs('admin.search-activities'))
                <a href="{{ route('admin.profession') }}"
                    class="block text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800 mr-2"
                    type="button">
                    Listes des Proffessions
                </a>
            @endif

            <button id="dropdownDefaultButton" data-dropdown-toggle="dropdown"
                class="text-white md:mx-3 bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                type="button">
                Filtres
                <svg class="w-2.5 h-2.5 ms-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 10 6">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m1 1 4 4 4-4" />
                </svg>
            </button>

            <!-- Dropdown menu -->
            <div id="dropdown"
                class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                    <li>
                        <a href="{{ route('admin.profession') }}"
                            class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                            Tout</a>
                    </li>
                    <li>
                        <a href="{{ route('admin.profession', ['f' => 'active']) }}"
                            class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                            Activés
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('admin.profession', ['f' => 'inactive']) }}"
                            class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                            Désactivés
                        </a>
                    </li>

                </ul>
            </div>

            <!-- Ajouter profession Modal -->
            <div id="authentication-modal-add" tabindex="-1" aria-hidden="true"
                class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-md max-h-full">
                    <!-- Modal content -->
                    <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                        <!-- Modal header -->
                        <div
                            class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                Ajouter une profession
                            </h3>
                            <button type="button"
                                class="end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                                data-modal-hide="authentication-modal-add">
                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                </svg>
                                <span class="sr-only">Fermer</span>
                            </button>
                        </div>
                        <!-- Modal body -->
                        <div class="p-4 md:p-5">
                            <form class="space-y-4" method="POST" action="{{ route('admin.proffession.store') }}">
                                @csrf
                                <div>
                                    <label for="name"
                                        class="block text-sm font-medium text-gray-700 dark:text-gray-400 text-left">
                                        Nom
                                    </label>
                                    <input type="text" id="name" name="name"
                                        class="block w-full p-3 mt-1 text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Entrez le nom de la profession" required />
                                </div>

                                <div>
                                    <label for="activity_domain"
                                        class="block text-sm font-medium text-gray-700 dark:text-gray-400 text-left">
                                        Domaine d'activité
                                    </label>
                                    <select id="activity_domain" name="field_activity_id"
                                        class="block w-full p-3 mt-1 text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                        required>
                                        <option value="" disabled selected>Sélectionnez un domaine</option>
                                        @foreach ($fieldActivities as $field)
                                            <option value="{{ $field->_id }}">{{ $field->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div>
                                    <button type="submit"
                                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                        Ajouter
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bouton déclencheur du modal -->
            <button data-modal-target="authentication-modal-add" data-modal-toggle="authentication-modal-add"
                class="block text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                type="button">
                Ajouter La profession
            </button>


        </div>

    </div>



    <div class="relative overflow-x-auto shadow-md sm:rounded-lg" id="proffession-list">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">Profession</th>
                    <th scope="col" class="px-6 py-3 w-28">Status</th>
                    <th scope="col" class="px-6 py-3"></th>
                </tr>
            </thead>
            <tbody>
                @foreach ($professions as $proffession)
                    <tr
                        class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                        <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                            {{ $proffession->name }}
                        </td>
                        <td class="px-6 py-4 text-gray-500 dark:text-gray-400">
                            @if ($proffession->is_active == true)
                                <span class="text-green dark:text-green-400">Activé</span>
                            @else
                                <span class="text-red-500 dark:text-red-400">Désactivé</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 text-right text-sm font-medium">
                            <div class="flex justify-end items-center space-x-2">
                                <!-- Modal toggle -->
                                <button data-modal-target="authentication-modal{{ $proffession->id }}"
                                    data-modal-toggle="authentication-modal{{ $proffession->id }}"
                                    class="block text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                    type="button">
                                    Modifier
                                </button>

                                <!-- Main modal -->
                                <div id="authentication-modal{{ $proffession->id }}" tabindex="-1"
                                    aria-hidden="true"
                                    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative p-4 w-full max-w-md max-h-full">
                                        <!-- Modal content -->
                                        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                                            <!-- Modal header -->
                                            <div
                                                class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                                    Modifier la profession
                                                </h3>
                                                <button type="button"
                                                    class="end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                                                    data-modal-hide="authentication-modal{{ $proffession->id }}">
                                                    <svg class="w-3 h-3" aria-hidden="true"
                                                        xmlns="http://www.w3.org/2000/svg" fill="none"
                                                        viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round"
                                                            stroke-linejoin="round" stroke-width="2"
                                                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">
                                                        Fermer
                                                    </span>
                                                </button>
                                            </div>
                                            <!-- Modal body -->
                                            <div class="p-4 md:p-5">
                                                <form class="space-y-4" method="POST"
                                                    action="{{ route('admin.proffession.update', $proffession->id) }}">
                                                    @csrf
                                                    <div>
                                                        <label for="name"
                                                            class="block text-sm font-medium text-gray-700 dark:text-gray-400 text-left">
                                                            Nom
                                                        </label>
                                                        <input type="text" id="name" name="name"
                                                            value="{{ $proffession->name }}"
                                                            class="block w-full p-3 mt-1 text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" />
                                                    </div>
                                                    <div>
                                                        <label for="field_activity_id" class="block text-sm font-medium text-gray-700 dark:text-gray-400 text-left">
                                                            Domaine d'activité
                                                        </label>
                                                        <select id="field_activity_id" name="field_activity_id"
                                                            class="block w-full p-3 mt-1 text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                                            required>
                                                            <option value="" disabled>Sélectionnez un domaine</option>
                                                            @foreach ($fieldActivities as $field)
                                                                <option value="{{ $field->_id }}"
                                                                    {{ $proffession->field_activity_id == $field->_id ? 'selected' : '' }}>
                                                                    {{ $field->name }}
                                                                </option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <div>
                                                        <button type="submit"
                                                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                                            Enregistrer
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @if ($proffession->is_active)
                                    <form action="{{ route('admin.proffession.toggle-is-active', $proffession->id) }}"
                                        method="POST">
                                        @csrf
                                        <input type="hidden" name="is_active" value="0">
                                        <button type="submit"
                                            class="text-red-500 p-2 border border-red-500 focus:outline-none w-36">
                                            Désactiver
                                        </button>
                                    </form>
                                @else
                                    <form action="{{ route('admin.proffession.toggle-is-active', $proffession->id) }}"
                                        method="POST">
                                        @csrf
                                        <input type="hidden" name="is_active" value="1">
                                        <button type="submit"
                                            class="text-green-500 p-2 border border-green-500 focus:outline-none w-36">
                                            Activer
                                        </button>
                                    </form>
                                @endif
                                <form action="{{ route('admin.proffession.delete', $proffession->id) }}"
                                    method="POST">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit"
                                        class="text-red-500 p-2 border border-red-500 focus:outline-none w-36">
                                        Supprimer
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="w-full flex justify-center items-center my-4">
        {{ $professions->links() }}
    </div>
    {{-- <script>
        const searchInput = document.getElementById('default-search');
        searchInput.addEventListener('input', function(e) {
            // filter the list
            let searchValue = e.target.value;
            let rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                let name = row.querySelector('td:nth-child(1)').textContent;
                if (name.toLowerCase().includes(searchValue.toLowerCase())) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    </script> --}}

    @if (session('success'))
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script>
            Swal.fire({
                title: 'Succès',
                text: "{{ session('success') }}",
                icon: 'success',
                confirmButtonText: 'OK'
            });
        </script>
    @endif

    @if (session('error'))
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script>
            Swal.fire({
                title: 'Erreur',
                text: "{{ session('error') }}",
                icon: 'error',
                confirmButtonText: 'OK'
            });
        </script>
    @endif
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Ecouteur d'événement pour tous les formulaires de suppression
        document.querySelectorAll('form[action*="delete"]').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault(); // Empêche l'envoi du formulaire

                // SweetAlert2 pour confirmer la suppression
                Swal.fire({
                    title: 'Êtes-vous sûr ?',
                    text: "Cette action est irréversible.",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Oui, supprimer !',
                    cancelButtonText: 'Annuler',
                    reverseButtons: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Si l'utilisateur confirme, soumettre le formulaire
                        form.submit();
                    }
                });
            });
        });
    </script>

</x-admin-layout>
