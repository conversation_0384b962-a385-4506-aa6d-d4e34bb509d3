<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config('app.name') }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .email-container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .content {
            margin-bottom: 30px;
        }
        .message {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            margin: 20px 0;
            white-space: pre-line;
        }
        .footer {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
        }
        .contact-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .contact-info h4 {
            margin-top: 0;
            color: #1976d2;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">{{ config('app.name', 'Cyclone Placement') }}</div>
            <p>Votre partenaire pour l'emploi</p>
        </div>

        <div class="content">
            <h2>Bonjour {{ $candidateName }},</h2>
            
            <div class="message">
                {{ $emailMessage }}
            </div>

            <div class="contact-info">
                <h4>Besoin d'aide ?</h4>
                <p>Notre équipe est là pour vous accompagner dans votre recherche d'emploi.</p>
                <p>
                    <strong>Email :</strong> <EMAIL><br>
                    <strong>Téléphone :</strong> +33 1 23 45 67 89
                </p>
                
                <a href="{{ config('app.url') }}" class="btn">Accéder à votre espace candidat</a>
            </div>
        </div>

        <div class="footer">
            <p>
                Cet email a été envoyé par {{ config('app.name') }}<br>
                Si vous ne souhaitez plus recevoir ces emails, 
                <a href="#" style="color: #007bff;">cliquez ici pour vous désabonner</a>
            </p>
            <p>
                © {{ date('Y') }} {{ config('app.name') }}. Tous droits réservés.
            </p>
        </div>
    </div>
</body>
</html>
