@php
    use App\Models\ConfigGlobalApp; // Importer le modèle ConfigGlobalApp
@endphp
<x-admin-layout>
    <x-slot name="title">
        Ajouter Date de publication
    </x-slot>

    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <!-- Bouton d'ajout de produit -->
    <div class="w-full flex justify-between items-center my-4">
        <div class="w-full flex items-center space-x-2">
            <svg fill="currentColor"
                class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path
                    d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z" />
                <path d="M13 7h-2v6h6v-2h-4z" />
            </svg>
            <h1 class="text-3xl font-semibold text-gray-900 dark:text-white mb-0">
                Période d’essai gratuite
            </h1>
        </div>

        <!-- Bouton d'ajout de produit -->
        {{-- <button data-modal-target="product-modal" data-modal-toggle="product-modal"
            class="block text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5 transition duration-200 ease-in-out transform hover:scale-105 whitespace-nowrap">
            Ajouter la date de publication
        </button> --}}
    </div>

    <!-- Modal de création de produit -->
    <div id="product-modal" tabindex="-1" aria-hidden="true"
        class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow-lg dark:bg-gray-800">
                <div class="flex items-center justify-between p-4 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Ajouter la date de publication</h3>
                    <button type="button" data-modal-hide="product-modal"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg w-8 h-8">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M1 1l6 6m0 0l6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                    </button>
                </div>
                <div class="p-4">
                    <form class="space-y-4" id="add-product-form" method="POST"
                        action="{{ route('stripe-webhook.add-date-publication') }}">
                        @csrf

                        <!-- Champ pour sélectionner la date et l'heure -->
                        <div>
                            <label for="datetime-picker"
                                class="block text-sm font-medium text-gray-900 dark:text-white">
                                Date et heure de publication
                            </label>
                            <input type="text" id="datetime-picker" placeholder="Date et heure de publication"
                                name="date" required
                                class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
                        </div>

                        <!-- Nouveau champ pour sélectionner le nombre de mois -->
                        <div>
                            <label for="months" class="block text-sm font-medium text-gray-900 dark:text-white">
                                Nombre de mois
                            </label>
                            <select id="months" name="months" required
                                class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <option value="" selected disabled>Sélectionner le mois</option>
                                <option value="30">1 mois</option>
                                <option value="60">2 mois</option>
                                <option value="90">3 mois</option>
                                <option value="120">4 mois</option>
                                <option value="150">5 mois</option>
                                <option value="180">6 mois</option>
                            </select>
                        </div>

                        <!-- Bouton de soumission -->
                        <button type="submit"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5">
                            Ajouter
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de modification -->
    <div id="edit-modal" tabindex="-1" aria-hidden="true"
        class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow-lg dark:bg-gray-800">
                <div class="flex items-center justify-between p-4 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Modifier la configuration</h3>
                    <button type="button" data-modal-hide="edit-modal"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg w-8 h-8">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M1 1l6 6m0 0l6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                    </button>
                </div>
                <div class="p-4">
                    <form class="space-y-4" id="edit-form" method="POST"
                        action="{{ route('stripe-webhook.update-date-publication') }}">
                        @csrf

                        <!-- Champ pour sélectionner la date et l'heure -->
                        <div>
                            <label for="edit-datetime-picker"
                                class="block text-sm font-medium text-gray-900 dark:text-white">
                                Début période d'essai gratuite
                            </label>
                            <input type="text" id="edit-datetime-picker" name="date" required
                                class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
                        </div>

                        <!-- Champ pour sélectionner la date de fin -->
                        <div>
                            <label for="edit-end-datetime-picker"
                                class="block text-sm font-medium text-gray-900 dark:text-white">
                                Fin période d'essai gratuite
                            </label>
                            <input type="text" id="edit-end-datetime-picker" name="enddate"
                                placeholder="Fin période d'essai gratuite" required
                                class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
                        </div>

                        <!-- Toggle switch pour display_bandeau -->
                        <div class="flex items-center">
                            <label for="display-bandeau" class="inline-flex relative items-center cursor-pointer">
                                <input type="hidden" name="display_bandeau" value="0"> <!-- Champ caché avec valeur par défaut -->
                                <input type="checkbox" id="display-bandeau" name="display_bandeau" value="1"
                                    class="sr-only peer"
                                    {{ isset($displayBandeau) && $displayBandeau->value ? 'checked' : '' }}>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                                <span class="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                                    Activer l'essai gratuit et Afficher le bandeau
                                </span>
                            </label>
                        </div>

                        <!-- Bouton de soumission -->
                        <button type="submit"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5">
                            Mettre à jour
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>


    <!-- Tableau des produits Stripe -->
    <div class="w-full overflow-x-auto shadow-lg rounded-lg bg-white">
        <table class="min-w-full table-auto text-sm text-gray-500">
            <thead>
                <tr class="bg-gray-100 text-gray-700">
                    <th class="px-6 py-3 text-left">Date de publication</th>
                    <th class="px-6 py-3 text-left">Date d'expiration</th>
                    <th class="px-6 py-3 text-left">Activer l'essai gratuit</th>
                    <th class="px-6 py-3 text-left">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white">
                @php
                    // Récupérer les configurations
                    $datePublish = ConfigGlobalApp::where('name', 'date_publish')->first();
                    $dayFreeAfterPublish = ConfigGlobalApp::where('name', 'day_free_after_publish')->first();
                    $displayBandeau = ConfigGlobalApp::where('name', 'display_bandeau')->first();
                @endphp

                @if ($datePublish && $dayFreeAfterPublish && $displayBandeau)
                    @php
                        // Convertir les dates en objets Carbon
                        $publicationDate = \Carbon\Carbon::parse($datePublish->value->toDateTime());
                        $expirationDate = \Carbon\Carbon::parse($dayFreeAfterPublish->value->toDateTime());

                        // Calculer la durée en mois entre les deux dates
                        $months = $publicationDate->diffInMonths($expirationDate);

                        // Statut du bandeau
                        $bandeauStatus = $displayBandeau->value ? 'Activé' : 'Désactivé';
                        $bandeauColor = $displayBandeau->value ? 'text-green-500' : 'text-red-500';
                    @endphp

                    <tr class="border-b">
                        <!-- Date de publication -->
                        <td class="px-6 py-4">{{ $publicationDate->format('d/m/Y H:i') }}</td>

                        <!-- Date d'expiration -->
                        <td class="px-6 py-4">{{ $expirationDate->format('d/m/Y H:i') }}</td>

                        <!-- Statut du bandeau -->
                        <td class="px-6 py-4 {{ $bandeauColor }}">
                            {{ $bandeauStatus }}
                        </td>

                        <!-- Actions -->
                        <td class="px-6 py-4">
                            <!-- Bouton Modifier -->
                            <button data-modal-target="edit-modal" data-modal-toggle="edit-modal"
                                data-id="{{ $datePublish->id }}"
                                data-date="{{ $publicationDate->format('Y-m-d H:i') }}"
                                data-enddate="{{ $expirationDate->format('Y-m-d H:i') }}"
                                data-display-bandeau="{{ $displayBandeau->value }}"
                                class="text-blue-500 hover:text-blue-700 ml-2">
                                Modifier
                            </button>
                        </td>
                    </tr>
                @else
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center">Aucune configuration trouvée.</td>
                    </tr>
                @endif
            </tbody>
        </table>
    </div>

    <!-- Scripts pour SweetAlert et autres fonctionnalités -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/fr.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Configuration de Flatpickr pour l'ajout
            flatpickr("#datetime-picker", {
                enableTime: true,
                dateFormat: "Y-m-d H:i",
                time_24hr: true,
                locale: "fr",
                minDate: "today",
            });

            // Configuration de Flatpickr pour l'édition
            const editDatetimePicker = flatpickr("#edit-datetime-picker", {
                enableTime: true,
                dateFormat: "Y-m-d H:i",
                time_24hr: true,
                locale: "fr",
                minDate: "today",
            });

            const editEndDatetimePicker = flatpickr("#edit-end-datetime-picker", {
                enableTime: true,
                dateFormat: "Y-m-d H:i",
                time_24hr: true,
                locale: "fr",
                minDate: "today",
            });

            // Gestion du modal d'édition
            const editButtons = document.querySelectorAll('[data-modal-target="edit-modal"]');
            editButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Récupérer les données du bouton
                    const date = button.getAttribute('data-date');
                    const enddate = button.getAttribute('data-enddate');
                    const displayBandeau = button.getAttribute('data-display-bandeau') === '1';

                    // Pré-remplir les champs du modal
                    editDatetimePicker.setDate(date);
                    editEndDatetimePicker.setDate(enddate);

                    // Mettre à jour le toggle switch correctement
                    const bandeauCheckbox = document.getElementById('display-bandeau');
                    bandeauCheckbox.checked = displayBandeau;

                    // Ouvrir le modal
                    const editModal = document.getElementById('edit-modal');
                    editModal.classList.remove('hidden');
                    editModal.setAttribute('aria-hidden', 'false');
                });
            });


            // Afficher une notification SweetAlert en fonction des messages de session
            const successMessage = "{{ session('success') }}";
            const errorMessage = "{{ session('error') }}";

            if (successMessage) {
                Swal.fire({
                    icon: 'success',
                    title: 'Succès',
                    text: successMessage,
                    showConfirmButton: false,
                    timer: 3000 // Fermer automatiquement après 3 secondes
                });
            }

            if (errorMessage) {
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur',
                    text: errorMessage,
                    showConfirmButton: false,
                    timer: 3000 // Fermer automatiquement après 3 secondes
                });
            }
        });

        // Fonction pour confirmer la suppression
        function confirmDelete() {
            Swal.fire({
                title: 'Êtes-vous sûr ?',
                text: "Vous ne pourrez pas revenir en arrière !",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Oui, supprimer !'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById('delete-form').submit();
                }
            });
        }
    </script>
</x-admin-layout>
