<x-admin-layout>
    <x-slot name="title">Candidats</x-slot>
    <div class="w-full flex items-center space-x-2">
        <svg fill="currentColor"
            class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
            version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
            viewBox="0 0 125.023 125.023" xml:space="preserve">
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
            <g id="SVGRepo_iconCarrier">
                <g>
                    <g>
                        <path
                            d="M65.176,57.92c16,0,28.952-12.972,28.952-28.962C94.128,12.963,81.176,0,65.176,0C49.183,0,36.218,12.964,36.218,28.958 C36.218,44.949,49.183,57.92,65.176,57.92z">
                        </path>
                        <path
                            d="M72.632,59.087l-7.211,7.264l-6.993-7.34c-0.024,0.006-0.05,0.006-0.066,0.012c-1.167,0.28-6.12,1.856-12.546,6.465 c-0.057,0.04-1.362,0.945-1.973,1.328c-1.213,0.766-3.024,1.875-5.215,2.922c-2.178,1.064-4.758,2.027-7.106,2.531 c-1.159,0.23-2.206,0.293-3.047,0.266c-0.869-0.016-1.369-0.204-1.899-0.436c-0.285-0.066-0.496-0.334-0.808-0.482 c-0.244-0.324-0.597-0.479-0.862-0.939c-0.142-0.203-0.305-0.373-0.457-0.593l-0.411-0.761c-0.318-0.452-0.519-1.126-0.776-1.706 c-0.281-0.558-0.426-1.292-0.635-1.935c-0.218-0.637-0.364-1.336-0.491-2.037c-0.322-1.348-0.473-2.755-0.63-4.047 c-0.193-1.274-0.181-2.553-0.276-3.632c-0.003-0.031-0.001-0.058-0.003-0.089c0.613-0.878,1.446-1.67,2.459-2.405 c1.012-0.727,1.808-1.937,2.336-3.094c2.054-4.563,2.947-7.176,4.421-11.962c0.622-2.016-3.096-4.247-5.522,1.459 c-1.026,2.067-0.578,2.279-1.621,4.338l-0.373,0.701c0,0-0.215-1.988-0.243-2.589c-0.323-6.89-0.618-10.586-0.949-17.476 c-0.09-1.911-0.886-2.762-2.361-2.66c-1.404,0.101-2.021,0.966-1.946,2.823c0.151,3.761,0.331,4.323,0.483,8.081 c0.071,1.417-0.851,1.148-0.845-0.006c-0.244-5.126-0.477-6.258-0.683-11.385c-0.058-1.392-0.637-2.305-2.064-2.458 c-1.379-0.146-2.321,0.999-2.251,2.742c0.205,4.955,0.45,5.915,0.654,10.871c0.072,1.466-0.83,1.235-0.833,0.133 c-0.183-3.928-0.299-4.667-0.583-8.588c-0.055-0.79-0.535-1.828-1.156-2.242c-1.515-1.009-3.171,0.277-3.101,2.369 c0.146,4.387,0.383,5.577,0.564,9.96c0.109,1.125-0.772,1.427-0.82,0.117c-0.136-2.791-0.241-2.389-0.394-5.177 c-0.07-1.271-0.794-1.997-2.072-2.046c-1.291-0.047-2.002,0.704-2.212,1.918c-0.09,0.497-0.042,1.022-0.019,1.531 c0.294,6.608,0.471,10.029,0.959,16.622c0.174,2.309,0.451,3.921,0.829,5.785c0.378,1.864,1.418,2.743,1.667,3.666 c-0.058,1.068-0.128,2.19-0.086,3.477c0.023,1.71,0.033,3.558,0.27,5.615c0.082,1.012,0.19,2.062,0.416,3.182 c0.215,1.114,0.345,2.219,0.72,3.428c0.348,1.197,0.616,2.388,1.18,3.666c0.259,0.63,0.52,1.264,0.783,1.9 c0.312,0.643,0.69,1.293,1.051,1.939c0.659,1.296,1.715,2.576,2.692,3.828c1.162,1.193,2.332,2.404,3.784,3.361 c2.788,1.992,6.115,3.328,9.163,3.834c3.063,0.549,5.932,0.553,8.498,0.308c0.689-0.077,1.532-0.168,2.192-0.269l0.019,33.848 h59.882v-12.961c1.321,3.738,2.566,8.053,3.745,12.961h23.102C116.131,93.336,98.253,67.534,72.632,59.087z M65.487,123.662 h-0.128l-6.987-9.557l6.987-46.678h0.128l6.992,46.678L65.487,123.662z">
                        </path>
                    </g>
                </g>
            </g>
        </svg>
        <h1 class="text-3xl font-semibold text-gray-900 dark:text-white mb-0">Liste des Candidats</h1>
    </div>
    <div class="w-full flex justify-between items-center my-4">
        <form id="search-form" class="relative w-96" action="{{ route('admin.candidat.search') }}" method="GET">
            <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                    <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                    </svg>
                </div>
                <input type="search" id="default-search" name="query"
                    class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    placeholder="Rechercher un candidat..." required />
                <button type="submit"
                    class="text-white absolute end-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                    Rechercher
                </button>
            </div>
        </form>
        <div class="flex items-center">
            <!-- Bouton affiché uniquement si la route correspond -->
            @if (request()->routeIs('admin.candidat.search') || request()->routeIs('admin.candidat.search'))
                <a href="{{ route('admin.candidates') }}"
                    class="block text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800 mr-2"
                    type="button">
                    Listes des candidates
                </a>
            @endif


            <form action="{{ route('admin.candidates') }}" method="GET" id="filter-form">
                <button id="dropdownDefaultButton" data-dropdown-toggle="dropdown"
                    class="text-white md:mx-3 bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                    type="button">
                    Filtres
                    <svg class="w-2.5 h-2.5 ms-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 10 6">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 4 4 4-4" />
                    </svg>
                </button>

                <!-- Dropdown menu -->
                <div id="dropdown"
                    class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                    <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                        <li>
                            <a href="{{ route('admin.candidates') }}"
                                class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                Tout
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('admin.candidates', ['f' => 'active']) }}"
                                class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                Activés
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('admin.candidates', ['f' => 'inactive']) }}"
                                class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                Désactivés
                            </a>
                        </li>
                        <!-- Ajout des options de filtrage par type de contrat -->
                        <li class="px-4 py-2">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="contract_type[]" value="call" class="form-checkbox"
                                    @if (in_array('call', $selectedContractTypes)) checked @endif>
                                <span>Travail sur appel</span>
                            </label>
                        </li>
                        <li class="px-4 py-2">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="contract_type[]" value="cdi" class="form-checkbox"
                                    @if (in_array('cdi', $selectedContractTypes)) checked @endif>
                                <span>CDI</span>
                            </label>
                        </li>
                        <li class="px-4 py-2">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="contract_type[]" value="cdd" class="form-checkbox"
                                    @if (in_array('cdd', $selectedContractTypes)) checked @endif>
                                <span>CDD</span>
                            </label>
                        </li>
                    </ul>
                </div>
            </form>

            <!-- Dropdown menu -->
            <div id="dropdown"
                class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                    <li>
                        <a href="{{ route('admin.candidates') }}"
                            class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                            Tout</a>
                    </li>
                    <li>
                        <a href="{{ route('admin.candidates', ['f' => 'active']) }}"
                            class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                            Activés
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('admin.candidates', ['f' => 'inactive']) }}"
                            class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                            Désactivés
                        </a>
                    </li>

                </ul>
            </div>
        </div>

    </div>
    <div class="relative overflow-x-auto shadow-md sm:rounded-lg" id="recruiter-list">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-4 py-2">Nom et Prénom</th>
                    <th scope="col" class="px-4 py-2">Email</th>
                    <th scope="col" class="px-4 py-2">Nombre d'offres</th>
                    <th scope="col" class="px-4 py-2">Nombre d'offres conclues</th>
                    <th scope="col" class="px-4 py-2">Date de création</th>
                    <th scope="col" class="px-4 py-2">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($candidates as $candidate)
                    @if (!Str::contains($candidate->email, '_deleted_'))
                        <tr
                            class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                            <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                {{ $candidate->fullname() }}
                            </td>
                            <td class="px-4 py-2 text-gray-500 dark:text-gray-400">
                                {{ preg_replace('/_deleted_\d+/', '', $candidate->email) }}
                            </td>
                            <td class="px-4 py-2 text-gray-500 dark:text-gray-400">
                                {{ $candidate->offers_count ?? 'N/A' }}
                            </td>
                            <td class="px-4 py-2 text-gray-500 dark:text-gray-400">
                                {{ $candidate->concluded_offers_count ?? 'N/A' }}
                            </td>
                            <td class="px-4 py-2 text-gray-500 dark:text-gray-400">
                                {{ $candidate->created_at ? $candidate->created_at->format('d/m/Y') : 'N/A' }}
                            </td>
                            <td class="px-4 py-2 text-right text-sm font-medium">
                                <a href="{{ route('admin.candidate.show', $candidate->_id) }}"
                                    class="text-primary border border-primary p-1 dark:text-primary-light focus:outline-none mx-1 text-xs">
                                    Voir les détails
                                </a>
                                @if ($candidate->is_suspend == true)
                                    <a href="{{ route('admin.recruiters.unsuspend', $candidate->id) }}"
                                        class="text-green-500 p-1 border border-green-500 focus:outline-none mx-1 text-xs">
                                        Réactiver
                                    </a>
                                @else
                                    <a href="{{ route('admin.recruiters.suspend', $candidate->id) }}"
                                        class="text-red-500 p-1 border border-red-500 focus:outline-none mx-1 text-xs">
                                        Suspendre
                                    </a>
                                @endif
                            </td>
                        </tr>
                    @endif
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="w-full flex justify-center items-center my-4">
        {{ $candidates->links() }}
    </div>
    </div>
</x-admin-layout>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const checkboxes = document.querySelectorAll('input[name="contract_type[]"]');
        const form = document.getElementById('filter-form');

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                form.submit();
            });
        });
    });
</script>
