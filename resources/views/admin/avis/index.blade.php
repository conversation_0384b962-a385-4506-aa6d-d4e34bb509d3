<x-admin-layout>
    <x-slot name="title">Proffession</x-slot>


    <div class="w-full flex items-center">
        <!-- Icône SVG -->
        <svg class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white mr-2"
            fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z">
            </path>
        </svg>
        <!-- Titre -->
        <h1 class="text-3xl font-semibold text-gray-900 dark:text-white">Liste des Avis</h1>
    </div>

    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">Email</th>
                    <th scope="col" class="px-6 py-3">Résumé</th>
                    {{-- <th scope="col" class="px-6 py-3">Catégorie</th>
                    <th scope="col" class="px-6 py-3">Priorité</th>
                    <th scope="col" class="px-6 py-3">Sévérité</th> --}}
                    <th scope="col" class="px-6 py-3">Date</th>
                    <th scope="col" class="px-6 py-3 text-right">Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse($avis as $item)
                    @php
                        // $priorityName = App\Models\Priority::where('value', $item['priority_id'])->first();
                        // $severityName = App\Models\Severity::where('value', $item['severity_id'])->first();
                    @endphp
                    <tr
                        class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                        <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                            {{ $item->email }}
                        </td>
                        <td class="px-6 py-4">
                            {{ Str::limit($item->summary, 30) }}
                        </td>
                        {{-- <td class="px-6 py-4">
                            {{ $item->category_name }}
                        </td> --}}
                        {{-- <td class="px-6 py-4">
                            <span
                                class="{{ $item->priority_id == 1 ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800' }} text-xs font-medium px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-200">
                                {{ $priorityName->name }}
                            </span>
                        </td> --}}
                        {{-- <td class="px-6 py-4">
                            {{ $severityName->name }}
                        </td> --}}
                        <td class="px-6 py-4">
                            {{ $item->created_at->format('d/m/Y') }}
                        </td>
                        <td class="px-6 py-4 text-right">
                            <div class="flex justify-end items-center space-x-2">
                                <!-- View button -->
                                <button data-modal-target="view-modal-{{ $item->id }}"
                                    data-modal-toggle="view-modal-{{ $item->id }}"
                                    class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-3 py-2 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                    Voir
                                </button>
                            </div>
                            <!-- View Modal -->
                            <div id="view-modal-{{ $item->id }}" tabindex="-1" aria-hidden="true"
                                class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                <div class="relative p-4 w-full max-w-2xl max-h-full">
                                    <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                                        <div
                                            class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                                Détails de l'avis
                                            </h3>
                                            <button type="button"
                                                class="end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                                                data-modal-hide="view-modal-{{ $item->id }}">
                                                <svg class="w-3 h-3" aria-hidden="true"
                                                    xmlns="http://www.w3.org/2000/svg" fill="none"
                                                    viewBox="0 0 14 14">
                                                    <path stroke="currentColor" stroke-linecap="round"
                                                        stroke-linejoin="round" stroke-width="2"
                                                        d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                </svg>
                                                <span class="sr-only">Fermer</span>
                                            </button>
                                        </div>
                                        <div class="p-4 md:p-5 space-y-4 text-left"> <!-- Ajout de text-right ici -->
                                            <div>
                                                <h4 class="text-lg font-medium text-gray-900 dark:text-white">Email</h4>
                                                <p class="mt-1 text-gray-500 dark:text-gray-400">{{ $item->email }}
                                                </p>
                                            </div>
                                            <div>
                                                <h4 class="text-lg font-medium text-gray-900 dark:text-white">Résumé
                                                </h4>
                                                <p class="mt-1 text-gray-500 dark:text-gray-400">{{ $item->summary }}
                                                </p>
                                            </div>
                                            <div>
                                                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                                                    Description</h4>
                                                <p class="mt-1 text-gray-500 dark:text-gray-400">
                                                    {{ $item->description }}</p>
                                            </div>
                                            {{-- <div>
                                                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                                                    Catégorie</h4>
                                                <p class="mt-1 text-gray-500 dark:text-gray-400">
                                                    {{ $item->category_name }}</p>
                                            </div> --}}
                                            {{-- <div>
                                                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                                                    Priorité</h4>
                                                <p class="mt-1 text-gray-500 dark:text-gray-400">
                                                    {{ $priorityName->name }}</p>
                                            </div> --}}
                                            {{-- <div>
                                                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                                                    Severité</h4>
                                                <p class="mt-1 text-gray-500 dark:text-gray-400">
                                                    {{ $severityName->name }}</p>
                                            </div> --}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                        <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                            Aucun avis trouvé
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>


    <div class="w-full flex justify-center items-center my-4">
        {{ $avis->links() }}
    </div>




</x-admin-layout>
