<x-admin-layout>
    <x-slot name="title">
        Création de produit sur Stripe
    </x-slot>

    <div class="w-full flex justify-between items-center my-4">
        <form class="relative w-96" action="{{ route('admin.search-activities') }}" method="GET">
            <!-- Champ de recherche -->
            <label for="default-search" class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">
                Rechercher
            </label>
            <div class="relative">
                <input type="search" id="default-search" name="query"
                    class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                    placeholder="Rechercher des activités..." required />
                <button type="submit"
                    class="text-white absolute end-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm px-4 py-2">
                    Rechercher
                </button>
            </div>
        </form>
        <div class="flex items-center">
            <button data-modal-target="product-modal" data-modal-toggle="product-modal"
                class="block text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5 text-center">
                Ajouter produit sur Stripe
            </button>

            <!-- Modal de création de produit -->
            <div id="product-modal" tabindex="-1" aria-hidden="true"
                class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-md max-h-full">
                    <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                        <!-- Header du modal -->
                        <div class="flex items-center justify-between p-4 border-b rounded-t dark:border-gray-600">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Ajouter produit sur Stripe</h3>
                            <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg w-8 h-8"
                                data-modal-hide="product-modal">
                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="M1 1l6 6m0 0l6 6M7 7l6-6M7 7l-6 6" />
                                </svg>
                                <span class="sr-only">Fermer</span>
                            </button>
                        </div>
                        <!-- Body du modal -->
                        <div class="p-4">
                            <form class="space-y-4" id="add-product-form" method="POST" action="{{ route('admin.create-product') }}">
                                @csrf
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-900 dark:text-white">Nom</label>
                                    <input type="text" id="name" name="name" required
                                        class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
                                </div>
                                <div>
                                    <label for="description" class="block text-sm font-medium text-gray-900 dark:text-white">Description</label>
                                    <textarea id="description" name="description"
                                        class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"></textarea>
                                </div>
                                <div>
                                    <label for="amount" class="block text-sm font-medium text-gray-900 dark:text-white">Montant</label>
                                    <input type="number" id="amount" name="amount" required min="0"
                                        class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
                                </div>
                                <div>
                                    <label for="currency" class="block text-sm font-medium text-gray-900 dark:text-white">Devise</label>
                                    <input type="text" id="currency" name="currency" required maxlength="3"
                                        class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
                                </div>
                                <div>
                                    <label for="billing_period" class="block text-sm font-medium text-gray-900 dark:text-white">Période de facturation</label>
                                    <select id="billing_period" name="billing_period" required
                                        class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        <option value="day">Jour</option>
                                        <option value="week">Semaine</option>
                                        <option value="month">Mois</option>
                                        <option value="year">Année</option>
                                    </select>
                                </div>
                                <div class="flex items-center justify-between">
                                    <button type="submit"
                                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5">
                                        Ajouter
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-admin-layout>
