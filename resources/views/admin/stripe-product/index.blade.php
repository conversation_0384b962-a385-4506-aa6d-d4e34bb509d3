<x-admin-layout>
    <x-slot name="title">
        Création de produit sur Stripe
    </x-slot>

    <div class="w-full flex justify-between items-center my-4">
        <div class="w-full flex items-center space-x-2">
            <svg fill="currentColor"
                class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path
                    d="M12 5c.552 0 1 .448 1 1v5h5c.552 0 1 .448 1 1s-.448 1-1 1h-5v5c0 .552-.448 1-1 1s-1-.448-1-1v-5H6c-.552 0-1-.448-1-1s.448-1 1-1h5V6c0-.552.448-1 1-1z" />
            </svg>
            <h1 class="text-3xl font-semibold text-gray-900 dark:text-white mb-0">
                Liste des Produits Stripe
            </h1>
        </div>

        <!-- Bouton d'ajout de produit -->
        <button data-modal-target="product-modal" data-modal-toggle="product-modal"
            class="block text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5 transition duration-200 ease-in-out transform hover:scale-105 whitespace-nowrap">
            Ajouter produit sur Stripe
        </button>
    </div>


    <!-- Modal de création de produit -->
    <div id="product-modal" tabindex="-1" aria-hidden="true"
        class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow-lg dark:bg-gray-800">
                <div class="flex items-center justify-between p-4 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Ajouter produit sur Stripe</h3>
                    <button type="button" data-modal-hide="product-modal"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg w-8 h-8">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M1 1l6 6m0 0l6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                    </button>
                </div>
                <div class="p-4">
                    <form class="space-y-4" id="add-product-form" method="POST"
                        action="{{ route('admin.create-product') }}">
                        @csrf
                        <div>
                            <label for="name"
                                class="block text-sm font-medium text-gray-900 dark:text-white">Nom</label>
                            <input type="text" id="name" name="name" required
                                class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
                        </div>
                        <div id="descriptions">
                            <div class="description-group">
                                <label for="description"
                                    class="block text-sm font-medium text-gray-900 dark:text-white">Description</label>
                                <textarea id="description" name="description[]"
                                    class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"></textarea>
                            </div>
                        </div>
                        <button type="button" id="add-description" class="text-sm text-blue-600 hover:underline">
                            Ajouter une autre description
                        </button>

                        <div>
                            <label for="price"
                                class="block text-sm font-medium text-gray-900 dark:text-white">Montant</label>
                            <input type="number" id="price" name="amount" required min="0"
                                class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
                        </div>
                        <div>
                            <label for="currency"
                                class="block text-sm font-medium text-gray-900 dark:text-white">Devise</label>
                            <select id="currency" name="currency" required
                                class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <option value="USD">USD</option>
                                <option value="EUR">EUR</option>
                                <option value="CHF">CHF</option>
                            </select>
                        </div>
                        <div>
                            <label for="periode"
                                class="block text-sm font-medium text-gray-900 dark:text-white">Période de
                                facturation</label>
                            <select id="periode" name="billing_period" required
                                class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <option value="day">Jour</option>
                                <option value="week">Semaine</option>
                                <option value="month">Mois</option>
                            </select>
                        </div>
                        <button type="submit"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5">
                            Ajouter
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des produits Stripe -->
    <div class="w-full overflow-x-auto shadow-lg rounded-lg bg-white">
        <table class="min-w-full table-auto text-sm text-gray-500">
            <thead>
                <tr class="bg-gray-100 text-gray-700">
                    <th class="px-6 py-3 text-left">Nom</th>
                    <th class="px-6 py-3 text-left">Stripe Product ID</th>
                    <th class="px-6 py-3 text-left">Stripe Price ID</th>
                    <th class="px-6 py-3 text-left">Prix</th>
                    <th class="px-6 py-3 text-left">Devise</th> <!-- Nouvelle colonne pour la devise -->
                    <th class="px-6 py-3 text-left">Description</th>
                    <th class="px-6 py-3 text-left">Durée (jours)</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white">
                @foreach ($plans as $plan)
                    <tr class="border-b hover:bg-gray-50">
                        <td class="px-6 py-4">{{ $plan->name }}</td>
                        <td class="px-6 py-4">{{ $plan->stripe_product_id }}</td>
                        <td class="px-6 py-4">{{ $plan->stripe_price_id }}</td>
                        <td class="px-6 py-4">{{ number_format($plan->price, 2) }}</td>
                        <td class="px-6 py-4">{{ $plan->currency }}</td> <!-- Affichage de la devise -->
                        <td class="px-6 py-4">
                            @php
                                $descriptions = json_decode($plan->description_html, true);
                            @endphp
                            @if (is_array($descriptions) && !empty($descriptions))
                                <ul style="list-style-type: disc; padding-left: 20px;">
                                    @foreach ($descriptions as $description)
                                        <li>{{ strip_tags($description) }}</li>
                                    @endforeach
                                </ul>
                            @else
                                <p>Aucune description disponible</p>
                            @endif
                        </td>
                        <td class="px-6 py-4">{{ $plan->duration_in_days }} jours</td>
                        <td>
                            {{-- <button
                                class="text-primary border border-primary p-2 dark:text-primary-light focus:outline-none mx-2 edit-button"
                                data-activity-id="" data-activity-name="">
                                Modifier
                            </button> --}}

                            <!-- Formulaire Supprimer -->
                            <form
                                action="{{ route('stripe-product.delete', ['product_id' => $plan->stripe_product_id]) }}"
                                method="POST" class="inline delete-form" data-activity-name="">
                                @csrf
                                @method('DELETE')
                                <button type="button"
                                    class="text-red-500 p-2 border border-red-500 focus:outline-none delete-button"
                                    onclick="confirmDelete(event)">
                                    Supprimer
                                </button>
                            </form>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <!-- Liens de pagination -->
    <div class="mt-5 flex justify-center">
        {{ $plans->links('pagination::tailwind') }}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    {{-- <script>
        @if (session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Produit créé avec succès',
                text: '{{ session('success') }}'
            });
        @endif
    </script> --}}
    <script>
        @if (session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Produit créé avec succès',
                text: '{{ session('success') }}'
            });
        @elseif (session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Erreur',
                text: '{{ session('error') }}'
            });
        @endif
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const addDescriptionButton = document.getElementById("add-description");
            const descriptionsContainer = document.getElementById("descriptions");

            addDescriptionButton.addEventListener("click", function(e) {
                e.preventDefault();

                // Créer un nouveau groupe de description
                const newDescriptionGroup = document.createElement("div");
                newDescriptionGroup.classList.add("description-group");

                // Créer le label et la zone de texte pour la nouvelle description
                const label = document.createElement("label");
                label.setAttribute("for", "description");
                label.classList.add("block", "text-sm", "font-medium", "text-gray-900", "dark:text-white");
                label.textContent = "Description";

                const textarea = document.createElement("textarea");
                textarea.setAttribute("name", "description[]");
                textarea.classList.add("block", "w-full", "p-3", "mt-1", "text-sm", "border", "rounded-lg",
                    "focus:ring-blue-500", "dark:bg-gray-700", "dark:border-gray-600", "dark:text-white"
                );

                // Ajouter le label et le textarea dans le nouveau groupe
                newDescriptionGroup.appendChild(label);
                newDescriptionGroup.appendChild(textarea);

                // Ajouter le groupe de description dans le container
                descriptionsContainer.appendChild(newDescriptionGroup);
            });
        });
    </script>
    <script>
        function confirmDelete(event) {
            event.preventDefault(); // Empêche l'envoi immédiat du formulaire

            Swal.fire({
                title: 'Êtes-vous sûr ?',
                text: 'Cette action est irréversible!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Oui, supprimer !',
                cancelButtonText: 'Annuler'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Soumettre le formulaire si la suppression est confirmée
                    event.target.closest('form').submit();
                }
            });
        }
    </script>
    <script>
        @if (session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Succès',
                text: '{{ session('success') }}'
            });
        @elseif (session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Erreur',
                text: '{{ session('error') }}'
            });
        @endif
    </script>

</x-admin-layout>
