@php
    use App\Models\ConfigGlobalApp; // Importer le modèle ConfigGlobalApp
@endphp
<x-admin-layout>
    <x-slot name="title">
        Paramètre email admin
    </x-slot>

    <!-- Titre de la page -->
    <div class="w-full flex justify-between items-center my-4">
        <div class="w-full flex items-center space-x-2">
            <svg fill="currentColor"
                class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path
                    d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
            </svg>
            <h1 class="text-3xl font-semibold text-gray-900 dark:text-white mb-0">
                Paramètre email admin
            </h1>
        </div>
    </div>

    <!-- Modal de modification -->
    <div id="edit-modal" tabindex="-1" aria-hidden="true"
        class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow-lg dark:bg-gray-800">
                <div class="flex items-center justify-between p-4 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Modifier l'email admin</h3>
                    <button type="button" data-modal-hide="edit-modal"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg w-8 h-8">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M1 1l6 6m0 0l6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                    </button>
                </div>
                <div class="p-4">
                    <form class="space-y-4" id="edit-form" method="POST" action="{{ route('admin-mail.update') }}">
                        @csrf
                        @method('POST')

                        <!-- Champ pour l'email admin -->
                        <div>
                            <label for="edit-admin-mail"
                                class="block text-sm font-medium text-gray-900 dark:text-white">
                                Email admin
                            </label>
                            <input type="email" id="edit-admin-mail" name="value" required
                                class="block w-full p-3 mt-1 text-sm border rounded-lg focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
                        </div>

                        <!-- Bouton de soumission -->
                        <button type="submit"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5">
                            Mettre à jour
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des paramètres de l'email admin -->
    <div class="w-full overflow-x-auto shadow-lg rounded-lg bg-white">
        <table class="min-w-full table-auto text-sm text-gray-500">
            <thead>
                <tr class="bg-gray-100 text-gray-700">
                    <th class="px-6 py-3 text-left">Email admin</th>
                    <th class="px-6 py-3 text-left">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white">
                @if ($adminMailConfig)
                    <tr class="border-b">
                        <!-- Valeur de l'email admin -->
                        <td class="px-6 py-4">{{ $adminMailConfig->value }}</td>

                        <!-- Actions -->
                        <td class="px-6 py-4">
                            <!-- Bouton Modifier -->
                            <button data-modal-target="edit-modal" data-modal-toggle="edit-modal"
                                data-id="{{ $adminMailConfig->id }}" data-admin-mail="{{ $adminMailConfig->value }}"
                                class="text-blue-500 hover:text-blue-700 ml-2">
                                Modifier
                            </button>
                        </td>
                    </tr>
                @else
                    <tr>
                        <td colspan="2" class="px-6 py-4 text-center">Aucun paramètre d'email admin enregistré.</td>
                    </tr>
                @endif
            </tbody>
        </table>
    </div>

    <!-- Scripts pour SweetAlert -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Gestion du modal d'édition
            const editButtons = document.querySelectorAll('[data-modal-target="edit-modal"]');
            editButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Récupérer les données du bouton
                    const adminMail = button.getAttribute('data-admin-mail');

                    // Mettre à jour l'action du formulaire
                    const editForm = document.getElementById('edit-form');
                    editForm.action = "{{ route('admin-mail.update') }}";

                    // Pré-remplir les champs du modal
                    document.getElementById('edit-admin-mail').value = adminMail;

                    // Ouvrir le modal
                    const editModal = document.getElementById('edit-modal');
                    editModal.classList.remove('hidden');
                    editModal.setAttribute('aria-hidden', 'false');
                });
            });

            // Afficher une notification SweetAlert en fonction des messages de session
            const successMessage = "{{ session('success') }}";
            const errorMessage = "{{ session('error') }}";

            if (successMessage) {
                Swal.fire({
                    icon: 'success',
                    title: 'Succès',
                    text: successMessage,
                    showConfirmButton: false,
                    timer: 3000 // Fermer automatiquement après 3 secondes
                });
            }

            if (errorMessage) {
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur',
                    text: errorMessage,
                    showConfirmButton: false,
                    timer: 3000 // Fermer automatiquement après 3 secondes
                });
            }
        });
    </script>
</x-admin-layout>
