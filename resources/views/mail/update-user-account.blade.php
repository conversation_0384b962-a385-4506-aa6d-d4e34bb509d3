<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mise à jour importante de votre compte - {{ config('app.name') }}</title>
</head>

<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f9f9f9;">
    <div
        style="max-width: 600px; margin: 20px auto; background: #ffffff; border: 1px solid #ddd; border-radius: 10px; overflow: hidden; box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);">

        <!-- En-tête -->
        <div style="text-align: left; padding: 15px;">
            <h2 style="margin: 0; font-size: 20px; color: #333;">
                Mise à jour de votre compte
                <a href="{{ config('app.url') }}" style="color: #007bff; text-decoration: none;">
                    {{ config('app.name') }}
                </a>
            </h2>
        </div>

        <!-- Corps -->
        <div style="padding: 20px;">
            <p style="font-size: 16px; color: #555; line-height: 1.5;">
                Cher(e) @if($user->getRoleSlugAttribute() === 'recruter') Recruteur @else Candidat @endif,
            </p>
            <p style="font-size: 16px; color: #555; line-height: 1.5;">
                <strong>{{ config('app.name') }}</strong> évolue pour améliorer votre expérience. Une restructuration de notre base de données est en cours et requiert une action de votre part.
                .
            </p>

            <h3 style="font-size: 18px; color: #333; margin-top: 20px;">Vos identifiants de connexion :</h3>
            <p style="font-size: 16px; color: #555; line-height: 1.5;">
                <strong>Email :</strong> {{ $user->email ?? 'Non spécifié' }}<br>
                <strong>Accès sécurisé :</strong>
                <a href="{{ route('password.reset', ['token' => $user->reset_token]) }}" style="color: #007bff; text-decoration: none;">
                    Réinitialiser votre mot de passe
                </a>
            </p>

            <h3 style="font-size: 18px; color: #333; margin-top: 20px;">Action requise :</h3>
            <ul style="font-size: 16px; color: #555; line-height: 1.8; margin-top: 10px;">
                <li><strong>Connectez-vous :</strong> Cliquez sur le lien “Réinitialiser votre mot de passe” pour accéder à votre compte Cyclone Placement.</li>
                <li><strong>Mettez à jour vos informations  :</strong> Une fois connecté(e), il vous sera demandé de vérifier et de modifier certaines informations de votre profil. Ces modifications garantiront que votre compte est correctement migré vers la nouvelle version de notre plateforme.
                </li>
                <li><strong>Validez les données :</strong> Certaines données par défaut peuvent être pré-remplies. Veuillez les valider ou les corriger si nécessaire.</li>
            </ul>

            <h3 style="font-size: 18px; color: #333; margin-top: 20px;">Pourquoi cette mise à jour ?</h3>
            <ul style="font-size: 16px; color: #555; line-height: 1.8; margin-top: 10px;">
                <li>Offrir une interface plus intuitive et moderne.</li>
                <li>Optimiser la gestion des candidatures et des recrutements.</li>
                <li>Garantir la sécurité et l'intégrité de vos données.</li>
            </ul>

            <h3 style="font-size: 18px; color: #333; margin-top: 20px;">Assistance et support :</h3>
            <p style="font-size: 16px; color: #555; line-height: 1.5;">
                Si vous avez des questions ou rencontrez des difficultés, n'hésitez pas à contacter notre service client à l'adresse :
                <a href="mailto:{{ config('app.admin_email') }}" style="color: #007bff; text-decoration: none;">
                    {{ config('app.admin_email') }}
                </a>.
            </p>
        </div>

        <!-- Pied de page -->
        {{-- <div
            style="background: #f9f9f9; color: #777; text-align: center; padding: 15px; font-size: 12px; border-top: 1px solid #ddd;">
            <p style="margin: 0;">Merci de votre collaboration et de votre confiance.</p>
            <p style="margin: 0;"><strong>L'équipe {{ config('app.name') }}</strong></p>
        </div> --}}
    </div>
</body>

</html>
