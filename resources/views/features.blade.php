<x-guest-layout>
    <x-slot name="title">Abonnements</x-slot>
    <section class="py-10 text-center px-5 md:px-0">
        <h2 class="text-3xl mb-10 font-normal font-title">
            Des prix attractifs et transparents
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 md:w-11/12 mx-auto">
            @foreach ($packages as $package)
                <div
                    class="border relative bg-gray-50 overflow-hidden
                           {{ $package->is_popular ? 'border-2 border-primary' : '' }}
                           {{-- On ne centre que si on a un seul package au total --}}
                           {{ $packages->count() === 1 ? 'md:col-start-2' : '' }}">
                    <!-- En-tête avec badge "Populaire" -->
                    <h3 class="text-2xl font-title font-semibold bg-primary text-white py-5 text-center">
                        {{ $package->name }}
                    </h3>

                    @if ($package->is_popular)
                        <div
                            class="absolute top-6 -right-16 bg-white text-primary text-xs py-1 transform rotate-45 w-52 shadow-lg uppercase font-bold text-center">
                            Populaire
                        </div>
                    @endif

                    <!-- Prix -->
                    <div class="relative w-44 mx-auto font-bold font-title my-10">
                        <p class="text-gray-800 text-md absolute top-0 left-1">CHF</p>
                        <p class="text-6xl font-semibold text-gray-800">{{ $package->price }}</p>
                        <p class="text-gray-500 font-normal text-sm">hors taxes</p>
                    </div>

                    <!-- Liste des fonctionnalités -->
                    <ul class="text-left mt-6 mb-14 mx-9">
                        @foreach ($package->features as $feature)
                            <li class="flex text-sm text-gray-400 font-light items-center mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 border font-bold border-primary rounded-full text-primary mr-2"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3"
                                        d="M5 13l4 4L19 7" />
                                </svg>
                                {!! $feature !!}
                            </li>
                            <hr class="my-4">
                        @endforeach
                    </ul>
                </div>
            @endforeach
        </div>
    </section>
</x-guest-layout>
