@if (\App\Helpers\UsedUpFunction::isCandidate())
    <x-candidate-dashboard-layout>
        <div class="bg-white w-full min-h-full">
            <div class="max-w-7xl mx-auto p-6">
                <h2 class="text-2xl font-semibold text-primary mb-6">Contacter Cyclone Placement
                </h2>

                <!-- Session Status -->
                <x-auth-session-status class="mb-4" :status="session('status')" />

                <form method="POST" action="{{ route('contact.store') }}" class="space-y-4" enctype="multipart/form-data">
                    @csrf
                    <!-- Nom et Email Row -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="col-span-1">
                            <x-text-input-other type="text" name="name"
                                value="{{ old('name', auth()->user()?->fullname()) }}" placeholder="Votre prénom & nom"
                                class="w-full bg-gray-100 cursor-not-allowed" required readonly onfocus="this.blur()" />
                            <x-input-error :messages="$errors->get('name')" class="mt-2" />
                        </div>
                        <div class="col-span-1">
                            <x-text-input-other type="email" name="email"
                                value="{{ old('email', auth()->user()?->email) }}" placeholder="Votre email"
                                class="w-full bg-gray-100 cursor-not-allowed" required readonly onfocus="this.blur()" />
                            <x-input-error :messages="$errors->get('email')" class="mt-2" />
                        </div>
                    </div>

                    <!-- Domaine(s) d'activité manquant(s) -->
                    <div>
                        <x-text-input type="text" name="field_activities" value="{{ old('field_activities') }}"
                            placeholder="Veuillez citer le(s) domaine(s) d'activité manquant(s)" class="w-full"
                            required />
                        <x-input-error :messages="$errors->get('field_activities')" class="mt-2" />
                    </div>

                    <!-- Profession(s) manquante(s) -->
                    <div>
                        <x-text-input type="text" name="professions" value="{{ old('professions') }}"
                            placeholder="Veuillez citer la/les profession(s) manquante(s)" class="w-full" required />
                        <x-input-error :messages="$errors->get('professions')" class="mt-2" />
                    </div>

                    <!-- Autres changements -->
                    <div>
                        <x-textarea-input name="other" placeholder="Autres changements à proposer..." rows="4"
                            class="w-full">{{ old('other') }}</x-textarea-input>
                        <x-input-error :messages="$errors->get('other')" class="mt-2" />
                    </div>

                    <!-- Champ d'upload de fichier -->
                    <div>
                        <x-file-input name="attachment" class="w-full" />
                        <x-input-error :messages="$errors->get('attachment')" class="mt-2" />
                    </div>

                    <!-- Recaptcha -->
                   <!--  <div class="mb-4">
                        <x-recaptcha />
                        @error('g-recaptcha-response')
                            <p class="text-red-600 text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div> -->

                    <!-- Bouton de soumission -->
                    <x-primary-button type="submit" class="w-full">
                        Envoyer la demande
                    </x-primary-button>
                </form>
                <div class="feedback-button fixed bottom-4 right-4">
                    <x-feedback-button-guest active="true" />
                </div>
                        <x-feedback-modal />
                        @isset($priorities, $severities)
                            <x-feedback-modal :priorities="$priorities" :severities="$severities" :user="auth()->user()" />
                        @endisset
            </div>
        </div>
    </x-candidate-dashboard-layout>
@elseif(\App\Helpers\UsedUpFunction::isRecruter())
    <x-recruter-dashboard-layout>
        <div class="bg-white w-full min-h-full">
            <div class="max-w-7xl mx-auto p-6">
                <h2 class="text-2xl font-semibold text-primary mb-6">Contacter Cyclone Placement
                </h2>

                <!-- Session Status -->
                <x-auth-session-status class="mb-4" :status="session('status')" />

                <form method="POST" action="{{ route('contact.store') }}" class="space-y-4"
                    enctype="multipart/form-data">
                    @csrf
                    <!-- Nom et Email Row -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="col-span-1">
                            <x-text-input-other type="text" name="name"
                                value="{{ old('name', auth()->user()?->fullname()) }}" placeholder="Votre prénom & nom"
                                class="w-full bg-gray-100 text-gray-700 cursor-not-allowed border-gray-300 rounded-md shadow-sm focus:ring-0 focus:border-gray-300"
                                required readonly onfocus="this.blur()" />
                            <x-input-error :messages="$errors->get('name')" class="mt-2" />
                        </div>
                        <div class="col-span-1">
                            <x-text-input-other type="email" name="email"
                                value="{{ old('email', auth()->user()?->email) }}" placeholder="Votre email"
                                class="w-full bg-gray-100 text-gray-700 cursor-not-allowed border-gray-300 rounded-md shadow-sm focus:ring-0 focus:border-gray-300"
                                required readonly onfocus="this.blur()" />
                            <x-input-error :messages="$errors->get('email')" class="mt-2" />
                        </div>
                    </div>

                    <!--SUJET -->
                    <div>
                        <x-text-input type="text" name="subject" value="{{ old('subject') }}"
                            placeholder="Sujet" class="w-full" required />
                        <x-input-error :messages="$errors->get('subject')" class="mt-2" />
                    </div>

                    <!-- Autres changements -->
                    <div class="my-4">
                        <x-textarea-input id="other" name="other" placeholder="Autres changements à proposer..."
                            rows="4" class="w-full" required>{{ old('other') }}</x-textarea-input>
                        <x-input-error :messages="$errors->get('other')" class="mt-2" />
                    </div>


                    <!-- Champ d'upload de fichier -->
                    <div>
                        <x-file-input name="attachment" class="w-full" />
                        <x-input-error :messages="$errors->get('attachment')" class="mt-2" />
                    </div>

                    <!-- Recaptcha -->
                 <!--    <div class="mb-4">
                        <x-recaptcha />
                        @error('g-recaptcha-response')
                            <p class="text-red-600 text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div> -->

                    <!-- Bouton de soumission -->
                    <x-primary-button type="submit" class="w-full">
                        Envoyer la demande
                    </x-primary-button>
                </form>

            </div>
            <div class="feedback-button fixed bottom-4 right-4">
                <x-feedback-button-guest active="true" />
            </div>
            <x-feedback-modal />
                        @isset($priorities, $severities)
                            <x-feedback-modal :priorities="$priorities" :severities="$severities" :user="auth()->user()" />
                        @endisset
        </div>
    </x-recruter-dashboard-layout>
@endif
