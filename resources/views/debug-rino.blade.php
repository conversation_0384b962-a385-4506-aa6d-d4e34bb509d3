<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="mb-48">
            <br><br><br>
            {{-- For test input completion --}}
            <h1>Recherche d'adresse</h1>
            <input type="text" id="addressInput" placeholder="Tapez une adresse..." value="" latitude=""
                longitude="">
            <ul id="suggestions"></ul>
        </div>

        <div id="map" style="height: 500px; width:500px;"></div>

        <script>
            function setPositionWithMarkerOnMap(zoomLevel = 13, map, latitude, longitude, address) {
                // Centrer la carte et ajouter un marqueur de l'addresse et lat, lon marquée
                map.setView([latitude, longitude], zoomLevel);
                const marker = L.marker([latitude, longitude]).addTo(map);
                marker.bindPopup(`<b>${address}</b><br>Latitude: ${latitude}<br>Longitude: ${longitude}`).openPopup();
            }

            function getCoordinateLatLon(address) {
                // address is like Ambanja, Madagascar
                const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}`;
                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        console.log('data', data);
                        if (data && data.length > 0) {
                            const {
                                lat,
                                lon
                            } = data[0];
                            // Get meilleure reponse
                            const latitude = parseFloat(lat);
                            const longitude = parseFloat(lon);

                            // setPositionOnMap(map,latitude,longitude,address);
                            return {
                                lat: latitude,
                                lon: longitude
                            };

                        } else {
                            return null;
                        }
                    })
                    .catch(error => {
                        console.error("Erreur lors de la requête à Nominatim :", error);
                        return null;
                    });
            }
        </script>

        <script>
            let zoomDefault = 2;
            let latDefault = 0;
            let longDefault = 0;
            let maxZoom = 19;
            document.addEventListener('DOMContentLoaded', () => {
                // Initialiser la carte Leaflet
                const map = L.map('map').setView([latDefault, longDefault], zoomDefault); // Coordonnées par défaut

                // Ajouter une couche de tuiles OpenStreetMap
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    maxZoom: maxZoom,
                    attribution: '© Cyclone-placement'
                }).addTo(map);

                // Fonction pour obtenir la position via Nominatim

                setPositionWithMarkerOnMap(13, map, -18.805093417491566, 47.556613531909605, 'Lazaina FJKM');

                // Appeler la fonction avec l'adresse que vous souhaitez afficher
                // getCoordinates("Ambanja");
            });
        </script>

        <script>
            document.addEventListener('DOMContentLoaded', () => {
                const addressInput = document.getElementById('addressInput');
                const suggestions = document.getElementById('suggestions');

                // Fonction pour effectuer la requête à Nominatim
                // async function fetchAddressSuggestions(query) {
                //     const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&addressdetails=1&limit=5`;
                //     const response = await fetch(url);
                //     const data = await response.json();
                //     return data;
                // }

                async function fetchAddressSuggestions(query) {
                    const url = `https://photon.komoot.io/api/?q=${encodeURIComponent(query)}&limit=5&lang=fr`;

                    try {
                        const response = await fetch(url);
                        const data = await response.json();

                        return data.features.map(feature => ({
                            description: feature.properties.name,
                            address: feature.properties.city || feature.properties.street || '',
                            latitude: feature.geometry.coordinates[1],
                            longitude: feature.geometry.coordinates[0]
                        }));
                    } catch (error) {
                        console.error('Request failed:', error);
                        return [];
                    }
                }

                // Fonction pour afficher les suggestions
                function displaySuggestions(addresses) {
                    suggestions.innerHTML = ''; // Vider la liste de suggestions

                    addresses.forEach(address => {
                        console.log('each', address)
                        const li = document.createElement('li');
                        li.textContent = address.description;
                        li.addEventListener('click', () => {
                            console.log('on a clicqué => ', li, address);
                            // console.log('on a clicqué',li,address.description);
                            addressInput.value = address
                            .description; // Remplir le champ avec l'adresse choisie
                            console.log('addressInput', addressInput.value);
                            addressInput.setAttribute('latitude', address.latitude);
                            addressInput.setAttribute('longitude', address.longitude);
                            suggestions.innerHTML = ''; // Vider la liste après sélection
                        });
                        suggestions.appendChild(li);
                    });
                }

                // Écouteur d'événement pour détecter les saisies de l'utilisateur
                addressInput.addEventListener('input', async () => {
                    const query = addressInput.value;
                    console.log('query ', query.length, query);
                    if (query.length < 2) {
                        suggestions.innerHTML = ''; // Masquer les suggestions si la saisie est trop courte
                        return;
                    }

                    // Appel à Nominatim pour obtenir les suggestions
                    const addresses = await fetchAddressSuggestions(query);
                    console.log('addresses', addresses)
                    displaySuggestions(addresses);
                });
            });
        </script>

        <style>
            #addressInput {
                width: 100%;
                padding: 8px;
                font-size: 16px;
            }

            #suggestions {
                list-style: none;
                padding: 0;
                margin-top: 5px;
                border: 1px solid #ddd;
                max-width: 100%;
            }

            #suggestions li {
                padding: 8px;
                cursor: pointer;
            }

            #suggestions li:hover {
                background-color: #f0f0f0;
            }
        </style>
    </div>
</x-app-layout>
