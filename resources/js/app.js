import "./bootstrap";

import Alpine from "alpinejs";
import "flowbite";
window.Alpine = Alpine;

Alpine.start();

document.addEventListener("DOMContentLoaded", function () {
    // Your code here select all mult-select-tag class
    const multiSelects = document.querySelectorAll(".mult-select-tag");
    multiSelects.forEach((multiSelect) => {
        // console.log(
        //     "multiSelect",
        //     multiSelect.querySelector(".btn-container > button")
        // );
        // si on click sur btn-container on active l'event click sur le .button
        multiSelect
            .querySelector(".btn-container")
            .addEventListener("click", function () {
                multiSelect
                    .querySelector(".btn-container")
                    .querySelector("button")
                    .click();
            });
        // si on click sur btn-container on active l'event click sur le .button
        multiSelect
            .querySelector(".btn-container > button")
            .addEventListener("click", function () {
                multiSelect
                    .querySelector(".btn-container")
                    .querySelector("button")
                    .click();
            });
        // si on click sur input-container on active l'event click sur le .button
        multiSelect
            .querySelector(".input-container")
            .addEventListener("click", function () {
                multiSelect
                    .querySelector(".btn-container")
                    .querySelector("button")
                    .click();
            });
    });

    let multiSelectsActivities = document.querySelectorAll(
        ".activity-domaine .mult-select-tag .input-container .item-container"
    );

    const selectedItems = document.querySelectorAll(
        ".activity-domaine .mult-select-tag .drawer ul li[style*='background-color: rgb(231, 249, 254)']"
      );


    // Convertir la NodeList en tableau pour garantir compatibilité
    let multiSelectsArray = Array.from(multiSelectsActivities);
    let selectedItemArray = Array.from(selectedItems);
    if(errorCount>0){
        for(let i=0; i<multiSelectsArray.length; i++){
            console.log("multiSelectsArray", multiSelectsArray[i]);
            multiSelectsArray[i].remove();
        }

        for(let i=0; i<selectedItemArray.length; i++){
            console.log("selectedItemArray", selectedItemArray[i]);
            selectedItemArray[i].remove();
        }
    }



});
