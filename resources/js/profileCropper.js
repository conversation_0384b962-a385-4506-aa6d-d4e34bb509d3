// resources/js/profileCropper.js
import Cropper from 'cropperjs';
import 'cropperjs/dist/cropper.min.css';

document.addEventListener("DOMContentLoaded", function () {
    if (window.profileCropperConfig) {
        window.profileCropperConfig.forEach(id => {
            const profileImageInput = document.getElementById(`${id}-profileImageInput`);
            const imageToCrop = document.getElementById(`${id}-imageToCrop`);
            const cropContainer = document.getElementById(`${id}-cropContainer`);
            const displayContainer = document.getElementById(`${id}-displayContainer`);
            const profileImageViewer = document.getElementById(`${id}-profileImageViewer`);
            const editContainer = document.getElementById(`${id}-editContainer`);
            const croppedImage = document.getElementById(`${id}-croppedImage`);
            const cropButton = document.getElementById(`${id}-cropButton`);
            const modifyButton = document.getElementById(`${id}-modifyButton`);
            const deleteButton = document.getElementById(`${id}-deleteButton`);
            const uploadButton = document.getElementById(`${id}-uploadButton`);
            const modal = document.getElementById(`${id}-modal`);
            const closeModal = document.getElementById(`${id}-closeModal`);
            let cropper;

            // Ouvrir l'input de fichier lors du clic sur Téléverser
            uploadButton.addEventListener("click", function () {
                profileImageInput.click();
            });

            // Afficher le modal pour recadrage après sélection de l'image
            profileImageInput.addEventListener("change", function (event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        imageToCrop.src = e.target.result;
                        modal.classList.remove("hidden");

                        // Initialiser CropperJS
                        if (cropper) cropper.destroy();
                        cropper = new Cropper(imageToCrop, {
                            aspectRatio: 1,
                            viewMode: 1,
                        });
                    };
                    reader.readAsDataURL(file);
                    
                    cropContainer.style.display = "block";
                }
            });

            // Recadrer et afficher l'image dans displayContainer
            cropButton.addEventListener("click", function () {
                if (cropper) {
                    const canvas = cropper.getCroppedCanvas({
                        width: 300,
                        height: 300,
                    });

                    canvas.toBlob((blob) => {
                        const url = URL.createObjectURL(blob);
                        croppedImage.src = url;

                        // Basculer vers l'affichage
                        displayContainer.style.display = "flex";
                        editContainer.style.display = "none";
                        profileImageViewer.style.display = "none";
                        modal.classList.add("hidden");
                    });
                }
            });

            // Fermer le modal sans recadrer
            closeModal.addEventListener("click", function () {
                modal.classList.add("hidden");
                profileImageInput.value = ""; // Réinitialiser l'input si l'utilisateur annule
            });

            // Modifier l'image en rouvrant le modal
            modifyButton.addEventListener("click", function () {
                modal.classList.remove("hidden");
                profileImageInput.value = ""; // Permettre la sélection d'une nouvelle image
            });

            // Supprimer l'image recadrée
            deleteButton.addEventListener("click", function () {
                if (cropper) {
                    cropper.destroy();
                    cropper = null;
                }
                croppedImage.src = "";
                imageToCrop.src = "";
                profileImageInput.value = "";

                displayContainer.style.display = "none";
                editContainer.style.display = "block";
                cropContainer.style.display = "none"; 
                profileImageViewer.style.display = "flex";
            });
        });
    }
});
