document.addEventListener("DOMContentLoaded", function () {
    if (window.uploadFileConfig) {
        window.uploadFileConfig.forEach(id => {
            const fileInput = document.getElementById(`${id}-fileInput`);
            const displayContainer = document.getElementById(`${id}-displayContainer`);
            const editContainer = document.getElementById(`${id}-editContainer`);
            const fileName = document.getElementById(`${id}-fileName`);
            const filePreview = document.getElementById(`${id}-filePreview`);
            const modifyButton = document.getElementById(`${id}-modifyButton`);
            const deleteButton = document.getElementById(`${id}-deleteButton`);
            const uploadButton = document.getElementById(`${id}-uploadButton`);
            const fileDataInput = document.getElementById(`${id}-fileData`); // Nouveau champ

            uploadButton.addEventListener("click", function () {
                fileInput.click();
            });

            fileInput.addEventListener("change", function (event) {
                const file = event.target.files[0];
                if (file) {
                    fileName.textContent = file.name;

                    const reader = new FileReader();
                    reader.onload = function (e) {
                        // Mettre à jour la valeur de l'input caché avec les données du fichier encodées en base64
                        if (fileDataInput) {
                            fileDataInput.value = e.target.result;
                        }

                        if (file.type.startsWith('image/')) {
                            filePreview.src = e.target.result;
                            filePreview.style.display = "block";
                        } else {
                            filePreview.style.display = "none";
                            filePreview.src = "#";
                        }
                    };
                    reader.readAsDataURL(file);

                    displayContainer.style.display = "flex";
                    editContainer.style.display = "none";
                }
            });

            modifyButton.addEventListener("click", function () {
                fileInput.click();
            });

            deleteButton.addEventListener("click", function () {
                fileInput.value = "";
                fileName.textContent = "";
                filePreview.src = "#";
                filePreview.style.display = "none";

                // Réinitialiser la valeur de l'input caché
                if (fileDataInput) {
                    fileDataInput.value = "";
                }

                displayContainer.style.display = "none";
                editContainer.style.display = "flex";
            });
        });
    }
});
