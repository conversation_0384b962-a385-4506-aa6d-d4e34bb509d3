pipeline {
    agent any

    stages {
        stage('Déploiement sur staging') {
            steps {
                sshPublisher(
                    publishers: [
                        sshPublisherDesc(
                            configName: 'Cyclone-placement-stating',
                            transfers: [
                                sshTransfer(
                                    remoteDirectory: 'sites/staging.cyclone-placement.ch',
                                    sourceFiles: '**/*',
                                    removePrefix: '',
                                    execCommand: '''
                                        cd sites/staging.cyclone-placement.ch
                                        ls -la  # Pour vérification
                                        composer install --no-dev
                                        npm install
                                        npm run build
                                        php artisan optimize:clear
                                    ''',
                                    execTimeout: 1200000
                                )
                            ],
                            usePromotionTimestamp: false,
                            verbose: true
                        )
                    ]
                )
            }
        }
        stage('Purge cache Laravel') {
            steps {
                sshPublisher(
                    publishers: [
                        sshPublisherDesc(
                            configName: 'Cyclone-placement-stating',
                            transfers: [
                                sshTransfer(
                                    remoteDirectory: 'sites/staging.cyclone-placement.ch',
                                    execCommand: '''
                                        cd sites/staging.cyclone-placement.ch
                                        php artisan config:clear
                                        php artisan optimize:clear
                                    ''',
                                    execTimeout: 60000
                                )
                            ],
                            usePromotionTimestamp: false,
                            verbose: true
                        )
                    ]
                )
            }
        }
    }
}

