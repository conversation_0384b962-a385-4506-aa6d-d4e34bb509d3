pipeline {
    agent any

    stages {
        stage('Deploy sur production') {
            steps {
               sshPublisher(publishers: [sshPublisherDesc(configName: 'Production-cyclone-placement', transfers: [sshTransfer(cleanRemote: false, excludes: '', execCommand: '', execTimeout: 120000, flatten: false, makeEmptyDirs: false, noDefaultExcludes: false, patternSeparator: '[, ]+', remoteDirectory: 'sites/cyclone-placement.ch', remoteDirectorySDF: false, removePrefix: '', sourceFiles: '**/*')], usePromotionTimestamp: false, useWorkspaceInPromotion: false, verbose: false)])
            }
        }
    }
}